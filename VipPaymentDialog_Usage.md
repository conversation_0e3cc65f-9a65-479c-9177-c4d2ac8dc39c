# VIP支付弹窗使用说明

## 概述
新实现的VipPaymentDialogFragment是一个从底部往上弹出的会员支付弹窗，基于BottomSheetDialogFragment实现，复用了现有VipActivity的业务逻辑。

## 主要文件
- `VipPaymentDialogFragment.java` - 主要的弹窗实现类
- `dialog_vip_payment.xml` - 弹窗布局文件
- `VipPaymentHelper.java` - 弹窗调用帮助类
- 相关drawable资源文件

## 使用方法

### 方法1：通过Helper类调用（推荐）
```java
// 显示弹窗
VipPaymentHelper.showVipPaymentDialog(this);

// 隐藏弹窗
VipPaymentHelper.hideVipPaymentDialog(this);
```

### 方法2：直接使用DialogFragment
```java
VipPaymentDialogFragment dialog = VipPaymentDialogFragment.newInstance();
dialog.show(getSupportFragmentManager(), "VipPaymentDialog");
```

## 功能特性
1. **从底部弹出** - 使用BottomSheetDialogFragment实现平滑的底部弹出动画
2. **完整支付流程** - 复用VipActivity的支付逻辑，支持支付宝和微信支付
3. **产品选择** - 支持多种会员套餐选择（日卡、月卡、年卡、永久）
4. **登录检查** - 自动检查用户登录状态，未登录时引导登录
5. **支付状态处理** - 完整的支付成功/失败处理逻辑
6. **EventBus集成** - 支付成功后通过EventBus通知其他组件

## 布局结构
- 顶部标题栏（包含关闭按钮）
- 会员信息展示区域
- 会员套餐选择列表
- 支付方式选择（支付宝/微信切换）
- 支付按钮
- 底部协议说明

## 注意事项
1. 调用的Activity必须继承自FragmentActivity
2. 弹窗会自动处理重复显示的情况
3. 支付成功后弹窗会自动关闭
4. 需要确保相关的drawable资源文件存在

## 依赖的现有组件
- VipProductAdapter（会员产品适配器）
- VipInfo.Product（会员产品数据模型）
- ApiServiceDelegate（API服务代理）
- UserAgent（用户代理）
- 支付相关工具类（PayTask、WXPayUtils等）
