---
description: 
globs: 
alwaysApply: true
---
# Git提交规范

## 提交格式
使用 [Conventional Commits](mdc:https:/www.conventionalcommits.org) 规范：

```
<type>[optional scope]: <description>

[optional body]
```

## 提交类型 (type)

- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 重构代码
- **perf**: 性能优化
- **test**: 测试相关
- **build**: 构建系统变更
- **ci**: CI/CD配置
- **chore**: 其他杂项
- **revert**: 撤销提交

## 常用范围 (scope)

YingYu项目常用的scope：
- `multiapp`: 多开应用功能
- `virtual`: 虚拟化核心
- `ui`: 用户界面
- `auth`: 身份验证
- `ads`: 广告相关
- `privacy`: 隐私保护
- `device`: 设备管理
- `location`: 位置管理
- `vip`: VIP功能

## 提交示例

```bash
# 基本格式
feat: 添加用户登录功能
fix: 修复应用崩溃问题
docs: 更新README文档

# 带scope
feat(multiapp): 实现应用分身网格布局
fix(auth): 修复登录超时问题
refactor(ui): 优化底部导航栏

# 带详细说明
feat: 新增多开应用界面功能

- 实现4x2应用网格布局
- 添加底部Tab导航
- 支持应用分身状态显示
- 集成静态数据展示
```

## 提交规则

1. **标题行**：不超过50个字符
2. **使用祈使句**：用"添加"而不是"添加了"
3. **首字母小写**：描述部分小写开头
4. **不加句号**：标题行结尾不加标点
5. **正文可选**：复杂改动可添加详细说明

## 最近提交示例

参考项目中的实际提交：
```
feat: 新增多开应用界面功能

- 新增MultiAppActivity实现4x2应用网格布局
- 添加AppItem和BottomTab数据模型
- 实现AppGridAdapter支持应用分身展示
- 创建蓝紫色渐变背景和状态指示器
- 添加底部Tab导航栏，支持5个功能入口
```

遵循此规范有助于：
- 自动生成更新日志
- 快速理解提交历史
- 便于代码审查和回滚
- 提高团队协作效率
