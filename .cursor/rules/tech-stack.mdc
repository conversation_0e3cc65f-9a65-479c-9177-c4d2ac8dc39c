---
description: 
globs: 
alwaysApply: true
---
# YingYu Android项目技术栈指南

## 项目概述
这是一个基于Android的多模块虚拟化应用项目，包名为`com.yingyu.zh`，支持应用多开和隐私保护功能。

## 构建配置

### Gradle配置
- **Gradle版本**: 6.5 (参见 [gradle-wrapper.properties](mdc:gradle/wrapper/gradle-wrapper.properties))
- **Android Gradle Plugin**: 4.1.0 (参见 [build.gradle](mdc:build.gradle))
- **Build Tools**: 29.0.3 (参见 [gradle.properties](mdc:gradle.properties))

### Android SDK配置
- **编译SDK版本**: 31
- **最低支持版本**: API 21 (Android 5.0)
- **目标SDK版本**: 30
- **使用AndroidX**: 是
- **启用Jetifier**: 是

### Java/Kotlin配置
- **Java版本**: 1.8 (参见 [host/build.gradle](mdc:host/build.gradle))
- **Kotlin版本**: 1.6.0
- **源码兼容性**: JavaVersion.VERSION_1_8

## 项目模块结构
项目采用多模块架构 (参见 [settings.gradle](mdc:settings.gradle)):

- **host**: 主应用模块，包名`com.yingyu.zh`
- **app-arm32**: 32位架构支持模块，包名`com.yingyu.zh.arm32`
- **libStub**: 存根库模块
- **common**: 公共组件模块
- **libSwipeBack**: 滑动返回手势库
- **asm-plugin**: ASM字节码处理插件 (复合构建)

## 核心技术栈

### 数据存储
- **Room**: 2.4.1 - 数据库ORM框架
- **MMKV**: 1.2.10 - 高性能键值存储
- **Gson**: 2.9.1 - JSON序列化/反序列化

### 响应式编程
- **RxJava**: 2.2.17
- **RxAndroid**: 2.1.1

### 网络通信
- **OkHttp**: 3.10.0 - HTTP客户端
- **Retrofit**: 2.5.0 - REST API客户端

### 界面框架
- **AndroidX Libraries**:
  - AppCompat: 1.3.1
  - Material Design: 1.4.0
  - ConstraintLayout: 1.1.3
  - ViewPager2: 1.0.0
- **Glide**: 4.10.0 - 图片加载框架
- **BaseRecyclerViewAdapterHelper**: 3.0.4 - RecyclerView适配器助手

### 第三方服务
- **友盟统计**: 9.8.0
  - 数据统计: common
  - A/B测试: abtest 1.0.0
  - 防作弊: asms 1.8.6
  - 社交分享: share-core/qq/wx 7.3.5
- **穿山甲广告**: mediation-sdk *******
- **腾讯SDK**:
  - QQ开放平台: qqopensdk 3.52.0
  - 微信开放平台: wechat-sdk-android-without-mta 6.8.0

### 工具库
- **EventBus**: 3.3.1 - 事件总线
- **Once**: 1.3.1 - 一次性操作控制
- **StatusBarUtil**: 1.5.1 - 状态栏工具

## 构建插件
- **ASM Plugin**: 自定义字节码处理插件
- **AndResGuard**: 1.2.21 - 资源混淆插件
- **StringFog**: 3.0.0 - 字符串加密插件

## 渠道配置
支持多渠道打包 (参见 [host/build.gradle](mdc:host/build.gradle)):
- 应用宝 (yingyongbao)
- 分享版 (share)
- 小米应用商店 (xiaomi)
- 华为应用市场 (honor)

## Maven仓库配置
使用阿里云镜像和字节跳动仓库:
- 阿里云JCenter镜像
- 阿里云Google镜像
- 字节跳动Volcengine仓库
- 字节跳动Pangle广告仓库

## 代码混淆
- **ProGuard**: 启用代码混淆和资源压缩
- **AndResGuard**: 资源文件名混淆
- **7-Zip压缩**: 支持Apple M1芯片优化

## 开发注意事项
1. 项目使用虚拟化技术，涉及底层Android框架Hook
2. 包含ARM64和ARM32双架构支持
3. 集成了多个第三方SDK，需要注意版本兼容性
4. 使用了自定义ASM插件进行字节码处理
5. 支持应用多开和隐私保护功能

## 配置文件位置
- 主要配置: [gradle.properties](mdc:gradle.properties)
- 项目构建: [build.gradle](mdc:build.gradle)
- 模块配置: [host/build.gradle](mdc:host/build.gradle)
- 应用清单: [host/src/main/AndroidManifest.xml](mdc:host/src/main/AndroidManifest.xml)
