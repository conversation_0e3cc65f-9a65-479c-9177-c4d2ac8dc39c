<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.xyz.hide">

    <application
        android:name="com.xyz.hide.App"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="无界多开32"
        android:supportsRtl="true"
        android:debuggable="false"
        tools:ignore="GoogleAppIndexingWarning,HardcodedDebugMode"
        tools:replace="android:label,android:debuggable">

        <activity
            android:name="com.xyz.hide.SplashActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity-alias android:label="无界多开32" android:icon="@mipmap/ic_launcher"
            android:name="${applicationId}.vbox32"
            android:targetActivity="com.xyz.hide.SplashActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <meta-data
            android:name="android.notch_support"
            android:value="true" />

        <activity android:name="com.xyz.hide.VCameraActivity" />

    </application>
</manifest>
