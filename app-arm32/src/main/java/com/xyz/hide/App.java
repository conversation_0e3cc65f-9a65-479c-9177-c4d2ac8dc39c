package com.xyz.hide;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.webkit.WebView;

import com.fun.vbox.client.core.SettingConfig;
import com.fun.vbox.client.core.VCore;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class App extends Application {

    private static App gApp;
    private SharedPreferences mPreferences;

    public static App getApp() {
        return gApp;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);

        mPreferences = base.getSharedPreferences("hide", Context.MODE_MULTI_PROCESS);

        VCore.get().startup(base, this, new SettingConfig() {
            @Override
            public String getHostPackageName() {
                return "com.yingyu.zh";
            }

            @Override
            public String getExtPackageName() {
                return "com.yingyu.zh31";
            }

            @Override
            public Intent onHandleCameraIntent(Intent originIntent) {
                originIntent.setComponent(new ComponentName(
                        getExtPackageName(), VCameraActivity.class.getName()));
                return originIntent;
            }
        });
        if (Build.VERSION.SDK_INT >= 28) {
            closeAndroidPDialog();
        }
    }

    @Override
    public void onCreate() {
        gApp = this;
        super.onCreate();

        init();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            String processName = VCore.get().getProcessName();
            if (!getPackageName().equals(processName)) {
                int nameIndex = processName.indexOf(":");
                String name = processName.substring(nameIndex + 1);
                WebView.setDataDirectorySuffix(name + "_hide");
            }
        }
    }

    private void init() {
        VCore.get().initialize(new VCore.VirtualInitializer() {
            @Override
            public void onMainProcess() {
                super.onMainProcess();
            }
        });
    }

    public static SharedPreferences getPreferences() {
        return getApp().mPreferences;
    }

    private void closeAndroidPDialog() {
        try {
            Class aClass = Class.forName("android.content.pm.PackageParser$Package");
            Constructor declaredConstructor = aClass.getDeclaredConstructor(String.class);
            declaredConstructor.setAccessible(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            Class cls = Class.forName("android.app.ActivityThread");
            Method declaredMethod = cls.getDeclaredMethod("currentActivityThread");
            declaredMethod.setAccessible(true);
            Object activityThread = declaredMethod.invoke(null);
            @SuppressLint("SoonBlockedPrivateApi")
            Field mHiddenApiWarningShown = cls.getDeclaredField("mHiddenApiWarningShown");
            mHiddenApiWarningShown.setAccessible(true);
            mHiddenApiWarningShown.setBoolean(activityThread, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
