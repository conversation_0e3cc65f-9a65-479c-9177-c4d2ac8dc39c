package com.xyz.hide;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import com.fun.vbox.client.core.VCore;
import com.xyz.hide.utils.IoUtils;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

public class VCameraActivity extends Activity {
    private Button to_choose_pic;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 666 && resultCode == RESULT_OK && null != data) {
            Uri selectedFile = data.getData();
            Uri mUri = getIntent().getParcelableExtra(MediaStore.EXTRA_OUTPUT);
            if (getIntent().hasExtra("_VBOX_|_real_path_")) {
                String srcPath = getRealPathFromUri(VCore.get().getContext(), selectedFile);
                String outPath = getIntent().getStringExtra("_VBOX_|_real_path_");
                try {
                    if (!TextUtils.isEmpty(outPath)) {
                        copy(srcPath, outPath);
                    } else {
                        copy(this, selectedFile, mUri);
                    }
                } catch (Throwable e) {
                    copy(this, selectedFile, mUri);
                }
            } else {
                copy(this, selectedFile, mUri);
            }
            Intent intent = new Intent();
            setResult(RESULT_OK, intent);
        }
        finish();
    }

    private String getRealPathFromUri(Context context, Uri contentUri) {
        Cursor cursor = null;
        try {
            String[] proj = { MediaStore.Images.Media.DATA };
            cursor = context.getContentResolver().query(contentUri, proj, null, null, null);
            int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
            cursor.moveToFirst();
            return cursor.getString(column_index);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_camera);
        to_choose_pic = findViewById(R.id.to_choose_pic);
        to_choose_pic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                choosePic();
            }
        });
        choosePic();
    }

    private void choosePic() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                || checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                }, 0);
            } else {
                chooseRequest();
            }
        } else {
            chooseRequest();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int result : grantResults) {
            if (result == PackageManager.PERMISSION_GRANTED) {
                chooseRequest();
                break;
            }
        }
    }

    private void chooseRequest() {
        String action = getIntent().getAction();
        if (TextUtils.isEmpty(action)) {
            return;
        }
        Intent intent;
        if (action.equals(MediaStore.ACTION_VIDEO_CAPTURE)) {
            intent = new Intent(
                    Intent.ACTION_PICK,
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI);
        } else {
            intent = new Intent(
                    Intent.ACTION_PICK,
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        }
        startActivityForResult(intent, 666);
    }

    private void copy(Context context, Uri srcUri, Uri dstUri) {
        try {
            OutputStream outputStream = context.getContentResolver().openOutputStream(dstUri);
            if (outputStream == null) {
                return;
            }
            InputStream inputStream = context.getContentResolver().openInputStream(srcUri);
            if (inputStream == null) {
                return;
            }
            IoUtils.copy(inputStream, outputStream);
            inputStream.close();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void copy(String srcPath, String outPath) throws Throwable {
        try (FileInputStream srcStream = new FileInputStream(srcPath);
             FileOutputStream outStream = new FileOutputStream(outPath)) {
            IoUtils.copy(srcStream, outStream);
        }
    }
}
