package com.xyz.hide;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.fun.vbox.client.core.VCore;

public class SplashActivity extends Activity {

    @SuppressLint("WrongConstant")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = queryIntentActivity(this, VCore.getConfig().getHostPackageName());
        if (intent == null) {
            return;
        }
        intent.addFlags(268435456);
        startActivity(intent);
        AppIconChangeMgr.getInstance().hideAppLockIcon();
        finish();
    }

    private boolean isAppInstalled(Context context, String packageName) {
        try {
            context.getPackageManager().getApplicationInfo(packageName, 0);
            return true;
        } catch (Throwable th) {
            return false;
        }
    }

    private Intent queryIntentActivity(Context context, String packageName) {
        if (isAppInstalled(context, packageName)) {
            return context.getPackageManager().getLaunchIntentForPackage(packageName);
        }
        return null;
    }

}
