apply plugin: 'com.android.application'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion "${compile_sdk_version}" as Integer
    buildToolsVersion "${build_tools_version}"

    defaultConfig {
        applicationId "com.yingyu.zh31"
        minSdkVersion 23
        targetSdkVersion 30
        versionCode 100
        versionName "1.0.0"

        ndk {
            abiFilters "armeabi-v7a"
        }
    }

    signingConfigs {
        release {
            storeFile file("./../host/keystore/sign_release.jks")
            storePassword 'vDLQoHv4'
            keyAlias 'aoq'
            keyPassword '6dR6ZZKA'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro', 'vb-proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            minifyEnabled false
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro', 'vb-proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def fileName = "arm_32.apk"
            outputFileName = fileName
        }
    }
}

android {
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation fileTree(dir: "../common/libs", include: ["*.jar", "*.aar"])

    implementation 'com.jaeger.statusbarutil:library:1.5.1'
}

//tasks.whenTaskAdded { task ->
//    if (task.name.equalsIgnoreCase("assembleRelease")) {
//        task.finalizedBy(zipArm32)
//    }
//}
//
//task zipArm32(type: Zip) {
//    android.applicationVariants.all { variant ->
//        if (variant.name.equalsIgnoreCase("release")) {
//            File outputPath = new File("$rootDir" + File.separator + "host" + File.separator + "src" + File.separator + "main" + File.separator + "assets" + File.separator)
//            from variant.outputs[0].outputFile
//            archiveFileName.set("x1.sav")
//            destinationDirectory.set(outputPath)
//        }
//    }
//}
