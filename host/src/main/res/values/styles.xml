<resources>
    <!-- ToolBar -->
    <style name="ToolBarStyle" parent="@style/ThemeOverlay.AppCompat.ActionBar">

        <!--menu按钮颜色-->
        <item name="colorControlNormal">@android:color/white</item>
        <!--字体颜色-->
        <!--        <item name="android:textColor">@color/black</item>-->
        <!--修改字体大小-->
        <!--        <item name="android:textSize">14sp</item>-->
        <!--设置menu在 ... 下方-->
        <item name="overlapAnchor">false</item>
        <!--menu背景色-->
        <!--        <item name="android:popupBackground">@color/white</item>-->
    </style>

    <style name="loading_dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowBackground">@drawable/progress_dialog_bg</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="VBDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!--popMenu的Style-->
    <style name="CustomPopMenuStyle" >
        <item name="android:itemBackground">@color/white</item>
        <item name="android:dropDownListViewStyle">@style/popmenuDivier</item>
        <item name="android:textAppearanceSmallPopupMenu">@style/popmeuText</item>
        <item name="android:textAppearanceLargePopupMenu">@style/popmeuText</item>
    </style>
    <!--popmenu的字体颜色-->
    <style name="popmeuText">
        <item name="android:textColor">@color/mainTextColor</item>
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">center</item>
    </style>
    <!--popMenu分割线的颜色-->
    <style name="popmenuDivier">
        <item name="android:divider">@color/white</item>
        <item name="android:dividerHeight">1px</item>
    </style>

    <style name="AnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/slide_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/slide_bottom_out</item>
    </style>

    <style name="MyDialogStyleBottom" parent="Theme.AppCompat.Dialog">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <!--是否启用标题栏-->
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Dark"/>

    <style name="postdev_popup_content_top_btn" parent="@style/Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">15sp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="feature_row_card_item_tips" parent="">
        <item name="android:textSize">9.6sp</item>
        <item name="android:textColor">#1d1d1d</item>
        <item name="android:background">@drawable/banner_tips</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:paddingBottom">2dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:lines">1</item>
        <item name="android:paddingStart">4dp</item>
        <item name="android:paddingEnd">4dp</item>
    </style>

    <style name="TransparentTheme" parent="Theme.DroidPluginDemo">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@android:color/background_dark</item>
        <item name="colorPrimaryDark">@android:color/background_dark</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="CalTheme" parent="Widget.AppCompat.Button.Borderless">
        <item name="height">100dp</item>
    </style>
</resources>
