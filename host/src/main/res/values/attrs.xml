<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="DownloadLoadingView">
        <attr name="maxProgress" format="integer" />
        <attr name="radius" format="dimension" />
        <attr name="strokeWidth" format="dimension" />
        <attr name="roundRadius" format="dimension" />
        <attr name="backgroundColor" format="color" />
    </declare-styleable>

    <declare-styleable name="LabelView">
        <attr name="label_distance" format="dimension" />
        <attr name="label_height" format="dimension"/>
        <attr name="label_strokeWidth" format="dimension"/>
        <attr name="label_backgroundColor" format="color"/>
        <attr name="label_strokeColor" format="color"/>
        <attr name="label_text" format="string"/>
        <attr name="label_textSize" format="dimension" />
        <attr name="label_textStyle" format="enum" >
            <enum name="NORMAL" value="0"/>
            <enum name="BOLD" value="1"/>
            <enum name="ITALIC" value="2"/>
            <enum name="BOLD_ITALIC" value="3"/>
        </attr>
        <attr name="label_textColor" format="color"/>
        <attr name="label_visual" format="boolean"/>
        <attr name="label_orientation" format="enum">
            <enum name="LEFT_TOP" value="1"/>
            <enum name="RIGHT_TOP" value="2"/>
            <enum name="LEFT_BOTTOM" value="3"/>
            <enum name="RIGHT_BOTTOM" value="4"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FitTextView">
        <attr name="ftMinTextSize" format="dimension"/>
        <attr name="ftMaxTextSize" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="EditableSpinner">
        <attr name="downBtnImage" format="reference"/>
        <attr name="downBtnBackground" format="color"/>
        <attr name="downBtnWidth" format="dimension"/>
        <attr name="textSize" format="dimension"/>
        <attr name="textColor" format="color"/>
    </declare-styleable>

    <declare-styleable name="MineRowView">
        <attr name="mine_icon" format="color|reference"/>
        <attr name="mine_title" format="string"/>
        <attr name="mine_title_sp" format="dimension"/>
        <attr name="mine_tv_color" format="reference"/>
        <attr name="mine_detail" format="string"/>
        <attr name="mine_icon_visibility" format="boolean"/>
        <attr name="mine_switch_visibility" format="boolean"/>
        <attr name="mine_arrow_visibility" format="boolean"/>
        <attr name="divider_top_visibility" format="boolean"/>
        <attr name="divider_bottom_visibility" format="boolean"/>
    </declare-styleable>

</resources>