<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_app_detail">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/setting_action"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_700"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/saved_loc"
        app:titleTextColor="@color/white" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/app_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toTopOf="@+id/btn_get"
        app:layout_constraintTop_toBottomOf="@id/setting_action"
        tools:listitem="@layout/checkin_pos_item" />

    <Button
        android:id="@+id/btn_input"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/input_loc_info"
        android:textColor="@color/white"
        android:background="@drawable/common_btn_rect"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_get"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_recycler_view" />

    <Button
        android:id="@+id/btn_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:padding="10dp"
        android:text="@string/get_cur_loc_info"
        android:textColor="@color/white"
        android:background="@drawable/common_btn_rect"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_input"
        app:layout_constraintTop_toBottomOf="@+id/app_recycler_view" />

</androidx.constraintlayout.widget.ConstraintLayout>
