<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="快速登录"
            android:textColor="#131737"
            android:textSize="16sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="13dp"
            app:srcCompat="@drawable/ic_close_black"
            app:tint="#4c5055" />

        <Button
            android:id="@+id/btn_skip"
            style="@style/postdev_popup_content_top_btn"
            android:layout_alignParentEnd="true"
            android:drawableEnd="@drawable/icon_right_btn_new"
            android:drawablePadding="6dp"
            android:drawableTint="#aaa"
            android:text="不想登录，跳过"
            android:textColor="#aaa"
            android:textSize="12sp"
            android:visibility="gone" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#f3f3f3" />

    <TextView
        android:id="@+id/txv_login_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="9dp"
        android:paddingLeft="16dp"
        android:paddingTop="9dp"
        android:paddingRight="16dp"
        android:paddingBottom="9dp"
        android:textColor="#4c5055"
        android:textSize="12.6sp"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lot_login_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="18dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_login_qq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="13dp"
            app:layout_constraintBottom_toTopOf="@+id/txv_login_qq"
            app:layout_constraintEnd_toStartOf="@+id/imv_login_wechat"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:srcCompat="@drawable/login_qq" />

        <TextView
            android:id="@+id/txv_login_qq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="QQ登录"
            android:textColor="#1d1d1d"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/last_login_qq_tips"
            app:layout_constraintEnd_toEndOf="@+id/imv_login_qq"
            app:layout_constraintStart_toStartOf="@+id/imv_login_qq"
            app:layout_constraintTop_toBottomOf="@+id/imv_login_qq" />

        <TextView
            android:id="@+id/last_login_qq_tips"
            style="@style/feature_row_card_item_tips"
            android:text="上次登录"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@+id/separate_view"
            app:layout_constraintEnd_toEndOf="@+id/txv_login_qq"
            app:layout_constraintStart_toStartOf="@+id/txv_login_qq"
            app:layout_constraintTop_toBottomOf="@+id/txv_login_qq" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_login_wechat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="13dp"
            app:layout_constraintBottom_toTopOf="@+id/txv_login_wechat"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imv_login_qq"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:srcCompat="@drawable/login_wechat" />

        <TextView
            android:id="@+id/txv_login_wechat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="微信登录"
            android:textColor="#1d1d1d"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/last_login_wechat_tips"
            app:layout_constraintEnd_toEndOf="@+id/imv_login_wechat"
            app:layout_constraintStart_toStartOf="@+id/imv_login_wechat"
            app:layout_constraintTop_toBottomOf="@+id/imv_login_wechat" />

        <TextView
            android:id="@+id/last_login_wechat_tips"
            style="@style/feature_row_card_item_tips"
            android:text="上次登录"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@+id/separate_view"
            app:layout_constraintEnd_toEndOf="@+id/txv_login_wechat"
            app:layout_constraintStart_toStartOf="@+id/txv_login_wechat"
            app:layout_constraintTop_toBottomOf="@+id/txv_login_wechat" />

        <View
            android:id="@+id/separate_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/lot_privacy"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/last_login_qq_tips" />

        <LinearLayout
            android:id="@+id/lot_privacy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/separate_view">

            <CheckBox
                android:id="@+id/chk_agree_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:button="@drawable/chkbox_type_selector" />

            <TextView
                android:id="@+id/txv_privacy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我已阅读并同意《隐私政策》和《用户协议》"
                android:textColor="#4c5055"
                android:textSize="13sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>