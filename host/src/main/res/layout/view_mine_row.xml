<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/setting_item_height">

    <View
        android:id="@+id/divider_top"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:visibility="visible"
        android:layout_alignParentTop="true"
        android:background="#cdcdcd"/>


    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:visibility="gone"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/setting_row_margin_left"
        android:scaleType="fitCenter"
        />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="16dp"
        android:layout_toEndOf="@+id/iv_icon"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="Give us five stars"/>

    <TextView
        android:id="@+id/tv_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:textColor="@color/gray"
        android:layout_toStartOf="@+id/arrow"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        tools:text="1.1.6"/>

    <ImageView
        android:id="@+id/arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/setting_row_margin_right"
        app:srcCompat="@drawable/ic_right_arrow"/>

    <Switch
        android:id="@+id/row_switch"
        android:layout_centerVertical="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/setting_row_margin_right"
        android:checked="true"
        android:thumb="@drawable/thumb"
        android:track="@drawable/track"
        android:visibility="gone"
        android:layout_alignParentRight="true"
        />
    <View
        android:id="@+id/divider_bottom"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:visibility="visible"
        android:layout_alignParentBottom="true"
        android:background="#cdcdcd"/>

</RelativeLayout>