<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_app_detail">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_700"
        android:paddingBottom="12dp">

        <!-- 内容容器，确保在状态栏下方 -->
        <LinearLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:padding="12dp"
                android:src="@drawable/ic_back_white" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="应用详情"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/plugin_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:overScrollMode="never"
        android:scrollbars="vertical"
        android:layout_marginTop="10dp"
        tools:listitem="@layout/item_app_plugin" />

</LinearLayout>
