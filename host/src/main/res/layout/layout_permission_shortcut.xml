<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/shape_white_corner"
    android:layout_gravity="center"
    android:layout_marginStart="30dp"
    android:layout_marginEnd="30dp"
    >

    <TextView
        android:id="@+id/native_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="18dp"
        android:paddingEnd="18dp"
        android:textSize="18sp"
        android:textStyle="bold"
        android:text="@string/shortcut_permission"
        android:gravity="center"
        android:textColor="@color/title_text"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="30dp"
        />

    <TextView
        android:id="@+id/native_msg_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/native_title_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="14sp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:textColor="@color/desc_text"
        android:layout_marginTop="16dp"
        android:gravity="start"
        android:text="@string/shortcut_permission_tip"
        />

    <CheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/native_msg_tv"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:text="@string/never_remind"
        android:textColor="@color/desc_text"
        android:buttonTint="@color/desc_text"
        />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.6dp"
        app:layout_constraintTop_toBottomOf="@+id/checkbox"
        android:background="@color/gray2"
        android:layout_marginTop="10dp"
        />

    <TextView
        android:id="@+id/native_cancel"
        android:layout_width="0dp"
        android:layout_height="46dp"
        android:text="@string/shortcut_success"
        android:textColor="@color/desc_text"
        android:gravity="center"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/native_ok"
        app:layout_constraintTop_toBottomOf="@+id/divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        />

    <View
        android:layout_width="1dp"
        android:layout_height="46dp"
        android:background="@color/gray2"
        app:layout_constraintTop_toBottomOf="@+id/divider"
        app:layout_constraintStart_toEndOf="@+id/native_cancel"
        />

    <TextView
        android:id="@+id/native_ok"
        android:layout_width="0dp"
        android:layout_height="46dp"
        android:text="@string/goto_set"
        android:textColor="@color/color_rate_btn"
        android:gravity="center"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@+id/native_cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>