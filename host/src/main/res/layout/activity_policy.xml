<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_700"
        android:theme="@style/ToolBarStyle"
        app:title="@string/app_name"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolBar"
        android:focusable="false"
        android:overScrollMode="never">

        <TextView
            android:id="@+id/provacy_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="6dp"
            android:lineSpacingMultiplier="1.3"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/user_policy"
            android:textColor="@color/black"
            android:textIsSelectable="true"
            android:textSize="16sp" />
    </ScrollView>

</RelativeLayout>