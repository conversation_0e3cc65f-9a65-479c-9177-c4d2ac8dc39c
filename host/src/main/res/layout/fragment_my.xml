<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView xmlns:card_view="http://schemas.android.com/apk/res-auto"
        android:id="@+id/card_view1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        card_view:cardBackgroundColor="@color/white"
        card_view:cardCornerRadius="10dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginTop="15dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp">

            <ImageView
                android:id="@+id/iv_login_head"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_login_head"
                card_view:layout_constraintBottom_toBottomOf="parent"
                card_view:layout_constraintLeft_toLeftOf="parent"
                card_view:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                card_view:layout_constraintRight_toRightOf="parent"
                card_view:layout_constraintBottom_toBottomOf="parent"
                card_view:layout_constraintLeft_toRightOf="@+id/iv_login_head"
                card_view:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_login_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/not_logged_in"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    card_view:layout_constraintLeft_toLeftOf="parent"
                    card_view:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvVipTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="欢迎使用"
                    android:textSize="12sp"
                    card_view:layout_constraintLeft_toLeftOf="@+id/tv_login_name"
                    card_view:layout_constraintTop_toBottomOf="@+id/tv_login_name" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_view1">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="10dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="10dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_vip"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_top_visibility="true"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:mine_icon="@drawable/ic_vip"
                    app:mine_title="@string/vip_member"
                    app:mine_tv_color="@color/black"/>

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_invite_redeem"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="false"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_vip"
                    app:mine_icon="@drawable/ic_invite_redeem"
                    app:mine_title="邀请/兑换"
                    app:mine_tv_color="@color/black" />

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_hide_app"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="false"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_invite_redeem"
                    app:mine_icon="@drawable/ic_hide_app"
                    app:mine_title="隐藏应用"
                    app:mine_tv_color="@color/black" />

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_version"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="false"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_hide_app"
                    app:mine_icon="@drawable/ic_new_version"
                    app:mine_title="@string/menu_version"
                    app:mine_tv_color="@color/black" />

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_contact"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="false"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_version"
                    app:mine_icon="@drawable/ic_contact"
                    app:mine_title="@string/menu_contact"
                    app:mine_tv_color="@color/black" />

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_about"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="false"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_contact"
                    app:mine_icon="@drawable/ic_about"
                    app:mine_title="@string/menu_about"
                    app:mine_tv_color="@color/black" />

                <com.xyz.host.home.view.MineRowView
                    android:id="@+id/mrv_logout"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_item_height"
                    app:divider_bottom_visibility="true"
                    app:divider_top_visibility="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mrv_about"
                    app:mine_icon="@drawable/ic_logout"
                    app:mine_title="@string/login"
                    app:mine_tv_color="@color/black" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.cardview.widget.CardView>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>