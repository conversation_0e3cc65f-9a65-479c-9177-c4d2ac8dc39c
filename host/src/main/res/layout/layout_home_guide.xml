<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cs_homeguide"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000"
    >

    <com.xyz.host.home.view.OverlayWithHoleFrameLayout
        android:id="@+id/guideLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="#00000000"
        />


    <TextView
        android:id="@+id/tip_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:drawableTop="@drawable/guide_left_top"
        android:gravity="center_horizontal"
        android:text="点击这里试试吧～\n更多设置，开启更多功能"
        android:textColor="@color/white"
        />

    <TextView
        android:id="@+id/skip_tv"
        android:layout_width="120dp"
        android:layout_height="36dp"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toBottomOf="@id/tip_tv"
        app:layout_constraintLeft_toLeftOf="@id/tip_tv"
        app:layout_constraintRight_toRightOf="@id/tip_tv"
        android:text="朕知道了"
        android:textColor="@color/white"
        android:background="@drawable/shape_stroke_corner_8"
        android:gravity="center"
        android:textSize="16sp"
        />
</androidx.constraintlayout.widget.ConstraintLayout>