<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    android:background="@drawable/shape_coupon_item_bg"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- 左侧内容区域 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 优惠券标题行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 优惠券标题 -->
            <TextView
                android:id="@+id/tv_coupon_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="10元月卡优惠券"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 来源信息 -->
            <TextView
                android:id="@+id/tv_coupon_source"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="(受邀专享)"
                android:textColor="#FF4B4B"
                android:textSize="12sp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 有效期信息 -->
        <TextView
            android:id="@+id/tv_coupon_validity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="3天有效 | 2024/03/28 24:00:00 失效"
            android:textColor="#CCCCCC"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 右侧按钮区域 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <!-- 使用按钮或状态标记 -->
        <TextView
            android:id="@+id/tv_use_button"
            android:layout_width="60dp"
            android:layout_height="32dp"
            android:background="@drawable/shape_coupon_use_button"
            android:gravity="center"
            android:text="去使用"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold" />

        <!-- 状态标记（已过期/已使用） -->
        <TextView
            android:id="@+id/tv_status_mark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/shape_status_mark"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="已过期"
            android:textColor="#666666"
            android:textSize="10sp"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
