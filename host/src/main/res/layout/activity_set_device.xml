<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical" >

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_200"
        app:titleTextColor="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/AppTheme.PopupOverlay"
        app:theme="@style/AppTheme.AppBarOverlay"
        />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="10dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gray"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    android:gravity="center"
                    android:text="Android Id"/>

                <EditText
                    android:id="@+id/edt_androidId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    android:gravity="center"
                    android:text="IMEI"/>

                <EditText
                    android:id="@+id/edt_imei"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    android:gravity="center"
                    android:text="IMSI"/>

                <EditText
                    android:id="@+id/edt_imsi"
                    android:layout_width="0dp"
                    android:textColor="@android:color/black"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="Wifi Mac"/>

                <EditText
                    android:id="@+id/edt_mac"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_marginTop="6dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:text="厂商"/>

                <com.xyz.host.home.view.EditableSpinner
                    android:id="@+id/edt_brand"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:layout_height="40dp"
                    app:textSize="16sp"
                    app:textColor="@android:color/black"
                    app:downBtnImage="@drawable/arrow_down"
                    app:downBtnBackground="@color/colorAccent"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:textColor="@android:color/black"
                    android:text="型号 "/>

                <com.xyz.host.home.view.EditableSpinner
                    android:id="@+id/edt_model"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:layout_height="40dp"
                    app:textSize="16sp"
                    app:textColor="@android:color/black"
                    app:downBtnImage="@drawable/arrow_down"
                    app:downBtnBackground="@color/colorAccent"
                    />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="产品"/>

                <EditText
                    android:id="@+id/edt_name"
                    android:layout_width="0dp"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="设备"/>

                <EditText
                    android:id="@+id/edt_device"
                    android:layout_width="0dp"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="主板"/>

                <EditText
                    android:id="@+id/edt_board"
                    android:layout_width="0dp"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="显示ID"/>

                <EditText
                    android:id="@+id/edt_display"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:layout_marginTop="1dp"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="修订版本"/>

                <EditText
                    android:id="@+id/edt_id"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="硬件序列号"/>

                <EditText
                    android:id="@+id/edt_serial"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_marginTop="1dp"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="硬件制造商"/>

                <EditText
                    android:id="@+id/edt_manufacturer"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/item_bg"
                android:layout_marginTop="1dp"
                android:orientation="horizontal"
                android:paddingLeft="5dp"
                android:paddingRight="5dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@android:color/black"
                    android:text="硬件ID"/>

                <EditText
                    android:id="@+id/edt_fingerprint"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="123"
                    android:textColor="@android:color/black"
                    android:layout_weight="3"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <TextView
        android:id="@+id/device_save_tv"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/save"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:background="@drawable/common_btn_rect"
        android:layout_marginStart="50dp"
        android:layout_marginEnd="50dp"
        />
</LinearLayout>