<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:clickable="true"
    android:focusable="true">

    <!-- 主要引导内容 - 上半部分 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/guide_top_margin"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingStart="32dp"
        android:paddingEnd="32dp">

        <!-- 引导图标 -->
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_add"
            android:layout_marginBottom="20dp"
            android:tint="@android:color/white"
            android:alpha="0.9" />

        <!-- 引导标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="欢迎使用多开应用"
            android:textColor="@android:color/white"
            android:textSize="22sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp"
            android:gravity="center" />

        <!-- 引导描述 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="您还没有创建任何应用分身\n点击底部的【添加】按钮开始创建"
            android:textColor="@android:color/white"
            android:textSize="15sp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:lineSpacingExtra="3dp"
            android:alpha="0.9" />

        <!-- 点击提示 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="点击任意位置关闭引导"
            android:textColor="@android:color/white"
            android:textSize="11sp"
            android:alpha="0.6"
            android:gravity="center" />

    </LinearLayout>

    <!-- 指向添加按钮的箭头和提示 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="@dimen/guide_bottom_margin"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 提示气泡 -->
        <TextView
            android:id="@+id/tv_guide_add_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="点击这里添加应用"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:textStyle="bold"
            android:gravity="center"
            android:background="@drawable/guide_bubble_bg_selector"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:layout_marginBottom="8dp"
            android:clickable="true"
            android:focusable="true" />

        <!-- 向下箭头 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_arrow_down"
            android:tint="@android:color/white"
            android:alpha="0.9" />

    </LinearLayout>

</FrameLayout>
