<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        tools:ignore="ContentDescription">

        <TextView
            android:id="@+id/tv_header"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:background="@color/purple_200"
            android:gravity="start|bottom"
            android:padding="24dp"
            android:textColor="@android:color/white"
            android:textDirection="locale"
            android:textSize="20sp"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/login_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/login_default"
            android:layout_marginTop="20dp"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

        <com.xyz.host.home.view.fittext.FitTextView
            android:id="@+id/tv_login_name"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:text="无界多开"
            android:textColor="@android:color/white"
            android:lines="1"
            android:maxLines="2"
            app:ftMaxTextSize="16sp"
            app:ftMinTextSize="10sp"
            android:textSize="12sp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintTop_toTopOf="@+id/login_icon"
            app:layout_constraintStart_toEndOf="@+id/login_icon"
            android:layout_marginEnd="10dp"
            android:layout_marginStart="10dp" />

        <com.xyz.host.home.view.fittext.FitTextView
            android:id="@+id/tv_status"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:text="未开通会员"
            android:textColor="@android:color/white"
            android:lines="2"
            android:maxLines="3"
            app:ftMaxTextSize="14sp"
            app:ftMinTextSize="8sp"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@+id/tv_login_name"
            app:layout_constraintStart_toEndOf="@+id/login_icon"
            app:layout_constraintBottom_toBottomOf="@+id/login_icon"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp" />

        <Button
            android:id="@+id/btn_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="登录"
            android:textColor="@android:color/white"
            app:layout_constraintTop_toBottomOf="@+id/login_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tv_rate"
            android:layout_marginBottom="10dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp" />

        <Button
            android:id="@+id/btn_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="充值会员"
            android:textColor="@android:color/white"
            app:layout_constraintTop_toBottomOf="@+id/login_icon"
            app:layout_constraintStart_toEndOf="@+id/btn_login"
            app:layout_constraintBottom_toTopOf="@+id/tv_rate"
            android:layout_marginBottom="10dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp" />

        <TextView
            android:id="@+id/tv_rate"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:drawablePadding="24dp"
            android:gravity="center_vertical"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/web"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toBottomOf="@id/tv_header"
            android:layout_marginTop="16dp"
            app:drawableStartCompat="@drawable/ic_web" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintTop_toBottomOf="@id/tv_rate"
            android:background="#f3f3f3" />

        <TextView
            android:id="@+id/tv_share"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:drawablePadding="24dp"
            android:gravity="center_vertical"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:text="@string/menu_share"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toBottomOf="@id/tv_rate"
            app:drawableStartCompat="@drawable/ic_share" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintTop_toBottomOf="@id/tv_share"
            android:background="#f3f3f3" />

        <TextView
            android:id="@+id/tv_about"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:drawablePadding="24dp"
            android:gravity="center_vertical"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:text="@string/menu_about"
            android:textColor="@android:color/black"
            app:layout_constraintTop_toBottomOf="@id/tv_share"
            app:drawableStartCompat="@drawable/ic_menu_settings" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            app:layout_constraintTop_toBottomOf="@id/tv_about"
            android:background="#f3f3f3" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>