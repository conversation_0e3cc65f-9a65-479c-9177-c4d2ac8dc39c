<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/task_top_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/purple_200"
        app:titleTextColor="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/AppTheme.PopupOverlay"
        app:theme="@style/AppTheme.AppBarOverlay"
        />

    <com.amap.api.maps2d.MapView
        android:id="@+id/mapview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/task_top_toolbar"
        />

    <FrameLayout
        android:id="@+id/bottom_point"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="18dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="130dp">

            <View
                android:id="@+id/view_bg"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:background="@drawable/shape_white80_top_corner"
                app:layout_constraintBottom_toTopOf="@id/ll_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:clickable="true"
                android:focusable="true"
                />

            <com.xyz.host.home.view.fittext.FitTextView
                android:id="@+id/tv_address"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="6dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:gravity="center_vertical"
                android:lines="2"
                android:maxLines="3"
                android:text="@string/unknown_location"
                android:textColor="#333333"
                app:ftMaxTextSize="16sp"
                app:ftMinTextSize="10sp"
                app:layout_constraintBottom_toTopOf="@id/ll_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/view_bg" />

            <View
                android:id="@+id/divider_bottom"
                android:layout_width="0dp"
                android:layout_height="2px"
                android:background="@drawable/def_divider_button"
                app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <LinearLayout
                android:id="@+id/ll_bottom"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:divider="@drawable/def_divider_button"
                android:orientation="horizontal"
                android:showDividers="middle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/btn_save"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/save_map_loc"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/btn_checkin"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/saved_loc"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/btn_set"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="保存设置"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>


    <LinearLayout
        android:id="@+id/search_layout"
        style="@style/Theme.AppCompat.Light"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/dialogColorPrimary"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/task_top_toolbar"
        >

        <com.xyz.host.home.view.ListViewPlus
            android:id="@+id/search_results"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>