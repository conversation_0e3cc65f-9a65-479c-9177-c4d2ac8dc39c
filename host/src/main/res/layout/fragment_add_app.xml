<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="@string/loading"
        android:textColor="@color/darkgray"
        android:textSize="14sp" />

    <EditText
        android:id="@+id/search_app_input"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="10dp"
        android:textColorHint="@color/gray"
        android:background="@drawable/white_round_rect"
        android:drawablePadding="3dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:hint="@string/search_app_hint"
        android:inputType="text"
        android:maxLength="20"
        android:maxLines="1"
        android:padding="8dp"
        android:textSize="12sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_app_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/search_app_input"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        android:clipToPadding="false"
        android:paddingBottom="80dp" />
</RelativeLayout>