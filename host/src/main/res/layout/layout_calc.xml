<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolBar">

    <LinearLayout
        app:layout_constraintBottom_toTopOf="@+id/layout_bottom"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingBottom="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">

        <EditText
            android:id="@+id/et1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:cursorVisible="false"
            android:focusable="false"
            android:background="@null"
            android:text=""
            android:textColor="@color/cal_pad"
            android:textSize="30sp"/>

        <EditText
            android:id="@+id/et2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:cursorVisible="false"
            android:focusableInTouchMode="false"
            android:background="@null"
            android:text="0"
            android:textColor="@color/cal_pad"
            android:textSize="36sp"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_gravity="right"
            android:animateLayoutChanges="true"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/btn_pad_ac"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1.5"
                android:scaleType="centerInside"
                android:src="@drawable/btn_pad_c" />


            <ImageView
                android:id="@+id/btn_pad_del"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1.5"
                android:scaleType="center"
                android:src="@drawable/btn_pad_del" />

            <ImageView
                android:id="@+id/btn_pad_div"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="center"
                android:src="@drawable/btn_pad_div" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_pad_number_7"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="7"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_8"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="8"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_9"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="9"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <ImageView
                android:id="@+id/btn_pad_mul"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="center"
                android:src="@drawable/btn_pad_mul" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_pad_number_4"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="4"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_5"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="5"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_6"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="6"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <ImageView
                android:id="@+id/btn_pad_minus"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="center"
                android:src="@drawable/btn_pad_minus" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_pad_number_1"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="1"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_2"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="2"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_3"
                style="@style/CalTheme"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="3"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <ImageView
                android:id="@+id/btn_pad_plus"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:scaleType="center"
                android:src="@drawable/btn_pad_plus" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"

            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_pad_empty"
                style="@style/CalTheme"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="e"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_number_0"
                style="@style/CalTheme"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="0"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <Button
                android:id="@+id/btn_pad_dot"
                style="@style/CalTheme"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:text="."
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="normal" />

            <ImageView
                android:id="@+id/btn_pad_equal"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/shape_label"
                android:scaleType="center"
                android:src="@drawable/btn_pad_equal" />
        </LinearLayout>
    </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
