<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/shape_popup_menu_bg"
    android:padding="4dp"
    android:elevation="12dp">

    <!-- 删除应用 -->
    <LinearLayout
        android:id="@+id/ll_delete_app"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:paddingStart="24dp"
        android:paddingEnd="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="删除应用"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#40FFFFFF"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp" />

    <!-- 添加至桌面 -->
    <LinearLayout
        android:id="@+id/ll_add_to_desktop"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:paddingStart="24dp"
        android:paddingEnd="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加至桌面"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:id="@+id/divider_set_name"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#40FFFFFF"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp" />

    <!-- 设置名称 -->
    <LinearLayout
        android:id="@+id/ll_set_name"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:paddingStart="24dp"
        android:paddingEnd="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置名称"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#40FFFFFF"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp" />

    <!-- 更多设置 -->
    <LinearLayout
        android:id="@+id/ll_more_settings"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:paddingStart="24dp"
        android:paddingEnd="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="更多设置"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

</LinearLayout>
