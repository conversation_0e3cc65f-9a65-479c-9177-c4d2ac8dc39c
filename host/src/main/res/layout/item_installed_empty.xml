<?xml version="1.0" encoding="utf-8"?>
<com.xyz.host.home.view.labelview.LabelCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lcv_items"
    android:layout_width="110dp"
    android:layout_height="130dp"
    android:gravity="center_horizontal"
    app:label_backgroundColor="#009DFC"
    app:label_orientation="RIGHT_TOP">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="126dp"
        android:gravity="center"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center_horizontal">

            <ImageView
                android:id="@+id/iv_app_icon"
                android:src="@drawable/ic_plus"
                android:layout_width="50dp"
                android:layout_height="50dp" />

            <com.xyz.host.home.view.DownloadLoadingView
                android:id="@+id/item_loading_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:visibility="gone"
                app:radius="20dp"
                app:roundRadius="10dp"
                app:strokeWidth="4dp" />
        </FrameLayout>

        <com.xyz.host.home.view.MarqueeTextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="添加应用"
            android:textSize="12sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_isopened"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="14dp"
        android:background="@drawable/shape_bg_btn_add"
        tools:visibility="visible"
        android:visibility="gone" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/item_app_more_iv"
        android:visibility="gone"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="bottom|end"
        android:src="@drawable/ic_item_more" />

</com.xyz.host.home.view.labelview.LabelCardView>