<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground"
    android:paddingStart="20dp"
    android:paddingEnd="20dp"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

    <!-- 菜单图标 -->
    <ImageView
        android:id="@+id/iv_menu_icon"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:src="@drawable/ic_vip"
        android:layout_marginEnd="16dp"
        android:scaleType="centerInside" />

    <!-- 菜单标题 -->
    <TextView
        android:id="@+id/tv_menu_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="菜单标题"
        android:textColor="#333333"
        android:textSize="16sp"
        android:gravity="center_vertical" />

    <!-- 红点提示 -->
    <ImageView
        android:id="@+id/iv_menu_red_dot"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_red_dot"
        android:layout_marginEnd="12dp"
        android:visibility="gone" />

    <!-- 右箭头 -->
    <ImageView
        android:id="@+id/iv_menu_arrow"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:src="@drawable/ic_right_arrow"
        android:scaleType="centerInside"
        android:alpha="0.6" />

</LinearLayout>
