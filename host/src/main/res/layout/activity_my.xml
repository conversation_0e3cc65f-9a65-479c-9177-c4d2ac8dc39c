<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/WhiteSmoke"
    android:fitsSystemWindows="true">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_700"
        android:paddingBottom="12dp">

        <!-- 内容容器，确保在状态栏下方 -->
        <LinearLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:padding="12dp"
                android:src="@drawable/ic_back_white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="个人中心"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

    </RelativeLayout>

    <!-- Fragment容器 -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
