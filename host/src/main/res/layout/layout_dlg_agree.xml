<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    android:background="@drawable/shape_white_corner">

    <TextView
        android:id="@+id/agree_polity_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/private_user_policy"
        android:textColor="@color/jike_text"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/scrol_content"
        android:layout_width="wrap_content"
        android:layout_height="270dp"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/agree_polity_title" >

        <TextView
            android:id="@+id/agree_polity_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.3"
            android:padding="16dp"
            android:text="@string/agree_desc"
            android:textColor="@color/jike_text" />
    </ScrollView>


    <TextView
        android:id="@+id/agree_tv"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="50dp"
        android:background="@drawable/selector_home_action_bg"
        android:gravity="center"
        android:text="@string/agree"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scrol_content" />

    <TextView
        android:id="@+id/refuse_tv"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="50dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/refuse"
        android:textColor="@color/gray"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/agree_tv" />

</androidx.constraintlayout.widget.ConstraintLayout>