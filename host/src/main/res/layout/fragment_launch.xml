<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_reward"
        android:layout_width="0dp"
        android:layout_height="26dp"
        android:background="#11ADD8E6"
        android:gravity="center"
        android:text="@string/loading"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tv_reward"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_tool_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/shape_white_corner"
            tools:listitem="@layout/item_tool"
            android:overScrollMode="never" />
    </androidx.cardview.widget.CardView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_app_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="@dimen/dp_10"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@id/card_view1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:background="@color/WhiteSmoke"
        android:text="@string/loading"
        android:textColor="@color/darkgray"
        android:textSize="14sp"
        app:layout_constraintTop_toBottomOf="@id/tv_reward"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_app"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="30dp"
        app:srcCompat="@drawable/ic_add"
        app:backgroundTint="@color/purple_700"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>