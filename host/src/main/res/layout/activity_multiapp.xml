<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 主要内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@drawable/bg">

        <TextView
            android:id="@+id/tv_reward"
            android:layout_width="match_parent"
            android:layout_height="26dp"
            android:background="#11ADD8E6"
            android:gravity="center"
            android:text="@string/loading"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- 应用网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_app_grid"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="12dp"
            android:clipToPadding="false"
            android:paddingTop="10dp" />

    </LinearLayout>

    <!-- 底部容器 - 包含BottomTabs和广告 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <!-- 底部Dock导航 -->
        <FrameLayout
            android:id="@+id/bottom_tabs_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="20dp">

            <LinearLayout
                android:id="@+id/ll_bottom_tabs"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:orientation="horizontal"
                android:background="@drawable/shape_bottom_dock_bg"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:elevation="8dp">

                <!-- Tab项将在代码中动态添加 -->

            </LinearLayout>
        </FrameLayout>

        <!-- 广告位容器 - 铺满底部 -->
        <FrameLayout
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone" />

    </LinearLayout>

    <!-- 毛玻璃覆盖层 -->
    <FrameLayout
        android:id="@+id/blur_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">

        <!-- 毛玻璃效果背景 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/blur_gradient_background" />

    </FrameLayout>

</FrameLayout>