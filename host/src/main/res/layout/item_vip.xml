<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/vip_all_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_vip_item"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <TextView
        android:id="@+id/title_all_vip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="14dp"
        android:text="@string/vip_all"
        android:textColor="@color/title_text"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/desc_all_vip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_all_vip"
        android:layout_alignStart="@+id/title_all_vip"
        android:layout_toStartOf="@+id/all_price_tv"
        android:ellipsize="end"
        android:lines="1"
        android:layout_marginBottom="14dp"
        android:text="@string/desc_all_vip"
        android:textColor="@color/color_rate_press"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/all_price_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="12dp"
        android:text="@string/price_all"
        android:textColor="@color/color_rate_press"
        android:textSize="20sp"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/mark_vip_all"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_vip_gou"
        android:src="@drawable/ic_gou_white"
        android:visibility="gone" />
</RelativeLayout>