<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_blue_gradient"
    android:fitsSystemWindows="true">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">
        
        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close_black"
            android:tint="@android:color/white"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />
        
        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="个人中心"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />
        
        <!-- 通知按钮 -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_share"
            android:tint="@android:color/white"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />
    </LinearLayout>

    <!-- 用户信息区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingTop="30dp"
        android:paddingBottom="30dp">

        <!-- 用户头像 -->
        <androidx.cardview.widget.CardView
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="45dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_login_head"
                android:scaleType="centerCrop"
                android:padding="8dp" />
        </androidx.cardview.widget.CardView>

        <!-- 登录按钮 -->
        <TextView
            android:id="@+id/tv_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="立即登录"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />
    </LinearLayout>

    <!-- 菜单列表容器 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">
        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/shape_white80_top_corner"
            android:paddingTop="20dp">
            
            <!-- 菜单项容器 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:id="@+id/ll_menu_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 菜单项将在代码中动态添加 -->

                </LinearLayout>
            </androidx.cardview.widget.CardView>
            
            <!-- 底部空白区域 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="60dp" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>
