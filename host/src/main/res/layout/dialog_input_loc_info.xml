<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="200dp">

    <EditText
        android:id="@+id/et_tag"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:hint="输入地址名称"
        android:textColorHint="@color/gray"
        android:textColor="@color/black"
        android:background="@drawable/shape_edit"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:paddingStart="10dp"
        app:layout_constraintBottom_toTopOf="@+id/et_loc_json"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <EditText
        android:id="@+id/et_loc_json"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:textColor="@color/black"
        android:textColorHint="@color/gray"
        android:background="@drawable/shape_edit"
        android:hint="这个信息需要通过本应用分享位置信息，然后输入"
        android:inputType="textMultiLine"
        android:paddingStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_tag" />
</androidx.constraintlayout.widget.ConstraintLayout>