<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="4dp">

    <!-- 应用图标和在线状态 -->
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp">

        <!-- iPhone风格的圆角正方形图标 -->
        <ImageView
            android:id="@+id/iv_app_icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:background="@drawable/shape_iphone_icon_bg"
            android:src="@mipmap/ic_launcher"
            android:elevation="2dp"
            android:adjustViewBounds="false" />

        <!-- 新应用标记小绿点 -->
        <ImageView
            android:id="@+id/iv_online_dot"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_gravity="top|end"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="2dp"
            android:background="@drawable/shape_green_dot"
            android:elevation="4dp"
            android:visibility="visible" />

        <!-- 多开标识标签 -->
        <TextView
            android:id="@+id/tv_user_id_label"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="top|start"
            android:layout_marginTop="0dp"
            android:layout_marginStart="0dp"
            android:background="@drawable/shape_user_id_label"
            android:text="2"
            android:textColor="@android:color/white"
            android:textSize="9sp"
            android:gravity="center"
            android:elevation="5dp"
            android:visibility="gone"
            android:fontFamily="sans-serif-medium" />
    </FrameLayout>

    <!-- 应用名称 -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:text="应用名"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:fontFamily="sans-serif-medium"
        android:shadowColor="#80000000"
        android:shadowDx="0"
        android:shadowDy="1"
        android:shadowRadius="2" />

</LinearLayout>