<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_info1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="8dp"
        android:text="经度"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/tv_info2"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_tag" />

    <TextView
        android:id="@+id/tv_info2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:text="纬度"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/tv_info3"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_info1" />

    <TextView
        android:id="@+id/tv_info3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:text="有基站数据"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/tv_info4"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_info2" />

    <com.xyz.host.home.view.MarqueeTextView
        android:id="@+id/tv_info4"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginBottom="8dp"
        android:text="地址"
        android:textColor="@color/black"
        android:ellipsize="marquee"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@+id/et_tag"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_info3" />

    <EditText
        android:id="@+id/et_tag"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:hint="输入位置描述"
        android:layout_marginTop="10dp"
        android:paddingStart="10dp"
        android:textColorHint="@color/gray"
        android:textColor="@color/black"
        android:background="@drawable/shape_edit"
        android:maxLength="20"
        app:layout_constraintLeft_toLeftOf="@+id/tv_info4"
        app:layout_constraintRight_toRightOf="@+id/tv_info4"
        app:layout_constraintTop_toBottomOf="@+id/tv_info4" />
</androidx.constraintlayout.widget.ConstraintLayout>