<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#F5F5F5"
    android:orientation="vertical"
    android:fitsSystemWindows="true">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:background="@color/purple_700"
        android:paddingBottom="12dp">

        <!-- 内容容器，确保在状态栏下方 -->
        <LinearLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:padding="12dp"
                android:src="@drawable/ic_back_white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="邀请与兑奖"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_rules"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:text="规则"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

        </LinearLayout>

    </RelativeLayout>

    <!-- 可滚动内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 邀请好友卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="邀请好友"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="您的邀请码"
                        android:textColor="#666666"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_invite_code"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="..."
                            android:textColor="#333333"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_copy_code"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_copy_button"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="6dp"
                            android:text="点击复制邀请码"
                            android:textColor="@color/multiapp_brand_red"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_invite_rule_desc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:lineSpacingExtra="4dp"
                        android:text="邀请好友下载无界多开，被邀请人登录并填写邀请码成功后。邀请人和被邀请人各得一张一天体验券。"
                        android:textColor="#666666"
                        android:textSize="13sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 统计数据 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                android:paddingVertical="4dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_invited_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/multiapp_brand_red"
                            android:textSize="32sp"
                            android:textStyle="bold"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toLeftOf="@id/tv_invited_desc" />

                        <TextView
                            android:id="@+id/tv_invited_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:text="个"
                            android:textColor="@color/multiapp_brand_red"
                            android:textSize="14sp" />
                    </LinearLayout>


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="已邀请好友"
                        android:textColor="#666666"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_redeem_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/tv_redeem_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/multiapp_brand_red"
                            android:textSize="32sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginTop="4dp"
                            android:text="张"
                            android:gravity="center_vertical"
                            android:textColor="@color/multiapp_brand_red"
                            android:textSize="14sp" />
                    </LinearLayout>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="可用体验券"
                        android:textColor="#666666"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>


            <!-- 立即分享按钮 -->
            <Button
                android:id="@+id/btn_share_invite"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/shape_confirm_button"
                android:text="立即分享邀请码"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 接受邀请 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="32dp"
                android:text="填写邀请码获得体验券"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="输入好友分享的邀请码，填写成功后即可获得体验券，无需再领取"
                android:textColor="#666666"
                android:textSize="14sp"
                android:lineSpacingExtra="2dp" />

            <EditText
                android:id="@+id/et_redeem_code"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/shape_input_bg"
                android:hint="请输入6位邀请码"
                android:paddingHorizontal="16dp"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="14sp"
                android:maxLength="6"
                android:inputType="textCapCharacters" />

            <Button
                android:id="@+id/btn_confirm_redeem"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:background="@drawable/shape_confirm_button"
                android:text="立即获得体验券"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
