<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/sel_item_clone_bg"
    android:tag="layout/checkin_pos_item_0">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_pos"
        android:layout_marginStart="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="8dp"
        android:text="公司"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/tv_addr"
        app:layout_constraintEnd_toStartOf="@+id/btn_remove"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.xyz.host.home.view.MarqueeTextView
        android:id="@+id/tv_addr"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="10dp"
        android:ellipsize="marquee"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:text="中国北京市"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toStartOf="@+id/btn_remove"
        app:layout_constraintStart_toEndOf="@+id/icon"
        app:layout_constraintTop_toBottomOf="@+id/tv_label"
        app:layout_constraintBottom_toTopOf="@+id/tv_label"
        />

    <Button
        android:id="@+id/btn_remove"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/ic_close5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_addr"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>