<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_gravity="center"
    android:layout_marginStart="30dp"
    android:layout_marginEnd="30dp"
    >


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/native_close_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_close_black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingTop="10dp"
        android:paddingEnd="10dp"
        android:paddingRight="10dp"
        />

    <TextView
        android:id="@+id/native_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingStart="18dp"
        android:paddingEnd="18dp"
        android:textSize="20sp"
        android:textStyle="bold"
        android:text="@string/custom_app_name"
        android:gravity="center"
        android:textColor="@color/black"
        app:layout_constraintTop_toBottomOf="@+id/native_close_iv"
        />

    <EditText
        android:id="@+id/cut_name_et"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/native_title_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="18sp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:padding="10dp"
        android:textColor="@color/black"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="1"
        android:background="@drawable/white_round_rect"
        />

    <TextView
        android:id="@+id/native_ok"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:text="@string/ensure"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:background="@drawable/sel_clone_app_btn"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_margin="36dp"
        app:layout_constraintTop_toBottomOf="@+id/cut_name_et"
        android:paddingStart="50dp"
        android:paddingEnd="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
