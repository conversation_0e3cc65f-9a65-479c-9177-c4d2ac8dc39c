<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_bottom_bg"
    android:orientation="vertical"
    android:paddingVertical="14dp">

    <!-- 顶部标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="9dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="成为无界多开会员"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_sec_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="解锁无限多开、机型保护等全部特权功能"
            android:textColor="@color/title_second_text"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_close_white"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!-- 会员套餐列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_vip_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        tools:listitem="@layout/item_vip_horizontal" />

    <!-- 购买提示 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:paddingHorizontal="16dp"
        android:text="购买后有效期自动延长"
        android:textColor="@color/title_thrid_text"
        android:textSize="12sp" />

    <!-- 支付方式选择 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="16dp"
        android:layout_marginBottom="12dp"
        android:text="支付方式"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- 微信支付选项 -->
    <LinearLayout
        android:id="@+id/ll_wechat_pay"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:paddingHorizontal="10dp"
        android:background="@drawable/payment_option_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/icon_pay_weixin" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:text="微信支付"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/iv_wechat_selected"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_radio_selected"
            android:visibility="visible" />

    </LinearLayout>

    <!-- 支付宝支付选项 -->
    <LinearLayout
        android:id="@+id/ll_alipay"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:paddingHorizontal="10dp"
        android:background="@drawable/payment_option_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/icon_pay_alipay" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:text="支付宝支付"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/iv_alipay_selected"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_radio_unselected"
            android:visibility="visible" />

    </LinearLayout>

    <!-- 邀请好友卡片 -->
    <LinearLayout
        android:id="@+id/ll_invite_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/invite_card_bg"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <!-- 礼品盒图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_invite_redeem"
            android:layout_marginEnd="12dp" />

        <!-- 文案区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="邀请好友，免费获得体验券"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="每邀请1人成功，双方各得1天VIP体验"
                android:textColor="#CCFFFFFF"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 箭头图标 -->
        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_arrow_right"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 支付按钮 -->
    <TextView
        android:id="@+id/vip_goto_pay"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/pink_button_bg"
        android:gravity="center"
        android:text="同意协议并购买"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 底部说明 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开通代表阅读并同意"
            android:textColor="@color/title_thrid_text"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_purchase_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="《购买须知》"
            android:textColor="@color/title_thrid_text"
            android:textSize="12sp"
            android:textStyle="bold"
            android:background="?attr/selectableItemBackground"
            android:padding="4dp" />

    </LinearLayout>

</LinearLayout>
