<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="100dp"
    android:layout_height="116dp"
    android:background="@drawable/vip_card_normal_bg"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!-- 主要内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_vip_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="年卡"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tv_vip_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="8dp"
            android:text="¥128"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <!-- 原价 -->
        <LinearLayout
            android:id="@+id/ll_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:visibility="gone">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="原价"
                android:textColor="#9F9F9F"
                android:textSize="10sp" />

            <FrameLayout
                android:layout_width="25dp"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tv_original_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="500"
                    android:textColor="#9F9F9F"
                    android:textSize="10sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:background="#9F9F9F" />
            </FrameLayout>

        </LinearLayout>
    </LinearLayout>

    <!-- 右上角折扣标签 - 位于边框外 -->
    <TextView
        android:id="@+id/tv_discount_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_marginTop="-12dp"
        android:layout_marginEnd="-16dp"
        android:background="@drawable/discount_tag_corner_bg"
        android:elevation="8dp"
        android:paddingHorizontal="4dp"
        android:paddingVertical="4dp"
        android:text="折合¥0.35/天"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_leftbottom_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:background="@drawable/item_leftbottom_tag_corner_bg"
        android:elevation="8dp"
        android:paddingHorizontal="6dp"
        android:paddingVertical="4dp"
        android:layout_marginLeft="2dp"
        android:layout_marginBottom="2dp"
        android:text="限时返场"
        android:textColor="@color/white"
        android:textSize="10sp"
        android:textStyle="bold"
        android:visibility="gone" />
</FrameLayout>
