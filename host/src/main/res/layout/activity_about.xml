<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/WhiteSmoke">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_700"
        android:paddingBottom="12dp">

        <!-- 内容容器，确保在状态栏下方 -->
        <LinearLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:padding="12dp"
                android:src="@drawable/ic_back_white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/app_name"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

    </RelativeLayout>

    <!-- 内容区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

    <ImageView
        android:id="@+id/about_icon"
        android:layout_width="66dp"
        android:layout_height="66dp"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.16" />

    <TextView
        android:id="@+id/about_app_verion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/app_name"
        android:textColor="@color/black"
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/about_icon" />

    <TextView
        android:id="@+id/about_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="ICP备案号：粤ICP备2025443745号-1A >"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toTopOf="@+id/about_copyright"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/about_copyright"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="@string/copyright"
        android:textColor="@color/gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/about_privacy_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:text="@string/service_privacy_policy"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/about_logout_tv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="18sp"
        android:autoLink="all"
        android:padding="4dp"
        />

    <TextView
        android:id="@+id/about_logout_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="36dp"
        android:text="注销账号"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/about_number"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="14sp"
        android:autoLink="all"
        android:padding="4dp"
        />

    <TextView
        android:id="@+id/about_use_policy_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:text="@string/service_use_policy"
        android:textColor="@color/black"
        app:layout_constraintBottom_toTopOf="@+id/about_privacy_tv"
        app:layout_constraintStart_toStartOf="@+id/about_privacy_tv"
        android:textSize="18sp"
        android:autoLink="all"
        android:padding="4dp"
        />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
