<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    android:background="@drawable/shape_white_corner"
    android:minWidth="300dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="18dp"
                android:text="提示"
                android:textColor="@color/jike_text"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.xyz.host.home.view.fittext.FitTextView
                android:id="@+id/message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="18dp"
                android:layout_marginBottom="18dp"
                android:lineSpacingMultiplier="1.3"
                android:text="为保证及时收到应用信息，请打开常驻通知栏，\n打开设置也可能会让应用【自启动】来保活\n在设置->授予权限也可以找到开关"
                android:textAlignment="center"
                android:textColor="@color/black3"
                android:textSize="14sp"
                app:ftMaxTextSize="18sp"
                app:ftMinTextSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <CheckBox
                android:id="@+id/checkbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="16dp"
                android:buttonTint="@color/gray"
                android:text="不再提示"
                android:textColor="@color/black3"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/message"
                tools:checked="false" />

            <View
                android:id="@+id/divide_view"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="#F2F2F2"
                app:layout_constraintTop_toBottomOf="@+id/checkbox" />


            <TextView
                android:id="@+id/cancel_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:gravity="center"
                android:text="取消"
                android:textAllCaps="true"
                android:textColor="@color/gray2"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/ok_button"
                app:layout_constraintTop_toBottomOf="@+id/divide_view" />

            <View
                android:id="@+id/cancel_line"
                android:layout_width="1dp"
                android:layout_height="48dp"
                android:background="#F2F2F2"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toEndOf="@+id/cancel_button"
                app:layout_constraintTop_toBottomOf="@+id/divide_view" />

            <TextView
                android:id="@+id/ok_button"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:gravity="center"
                android:text="确定"
                android:textAllCaps="true"
                android:textColor="@color/jike_text"
                app:layout_constraintLeft_toRightOf="@+id/cancel_button"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divide_view" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>