<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:background="@drawable/shape_white_corner"
    android:orientation="vertical"
    android:paddingTop="10dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_margin="5dp"
        android:paddingTop="10dp"
        android:paddingBottom="8dp"
        android:text="@string/title_input_location"
        android:textColor="@color/black3"
        android:textSize="16sp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_lon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/shape_cut_stroke"
            android:gravity="center"
            android:hint="@string/hint_lng"
            android:imeOptions="actionDone"
            android:inputType="numberSigned|numberDecimal"
            android:lines="1"
            android:singleLine="true"
            android:textColor="@color/black2"
            android:textColorHint="@color/gray2"
            android:textSize="14sp" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_lat"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/shape_cut_stroke"
            android:gravity="center"
            android:hint="@string/hint_lat"
            android:imeOptions="actionNext"
            android:inputType="numberSigned|numberDecimal"
            android:lines="1"
            android:singleLine="true"
            android:textColor="@color/black2"
            android:textColorHint="@color/gray2"
            android:textSize="14sp" />
    </FrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/gray" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@android:string/cancel"
            android:textColor="@android:color/black" />

        <View
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/gray" />

        <TextView
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@android:string/ok"
            android:textColor="@android:color/black" />
    </LinearLayout>
</LinearLayout>