<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="4dp">

    <!-- Tab图标和红点提示 -->
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="2dp">

        <ImageView
            android:id="@+id/iv_tab_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_add" />

        <!-- 红点提示 -->
        <ImageView
            android:id="@+id/iv_red_dot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_gravity="top|end"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="0dp"
            android:background="@drawable/shape_red_dot"
            android:visibility="gone" />
    </FrameLayout>

    <!-- Tab标题 -->
    <TextView
        android:id="@+id/tv_tab_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="标题"
        android:textColor="@android:color/white"
        android:textSize="8sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:shadowColor="#80000000"
        android:shadowDx="0"
        android:shadowDy="1"
        android:shadowRadius="1" />

</LinearLayout>