<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/retention_dialog_bg"
    android:orientation="vertical"
    android:paddingVertical="20dp">

    <!-- 顶部关闭按钮 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_close_white"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:gravity="center"
        android:orientation="vertical">

        <!-- 图标 -->
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/ic_invite_redeem" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="新用户福利"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- 描述 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:lineSpacingMultiplier="1.3"
            android:text="注册即送12小时VIP体验\n立即享受无限多开等特权功能"
            android:textColor="#CCFFFFFF"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:orientation="horizontal">

        <!-- 主要操作按钮 -->
        <TextView
            android:id="@+id/btn_primary"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/pink_button_bg"
            android:gravity="center"
            android:text="立即注册"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
