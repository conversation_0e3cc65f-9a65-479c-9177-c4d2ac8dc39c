<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:lv="http://schemas.android.com/tools"
    android:background="@drawable/sel_item_clone_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/plugin_icon_iv"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:src="@drawable/ic_vip_position"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="16dp"
        android:background="@drawable/shape_plugin_bg"
        android:scaleType="center"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        />

    <TextView
        android:id="@+id/plugin_function_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/plugin_icon_iv"
        app:layout_constraintBottom_toTopOf="@+id/plugin_desc_tv"
        android:text="位置隐藏"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:layout_marginStart="10dp"
        app:layout_constraintVertical_chainStyle="packed"
        />

    <com.xyz.host.home.view.MarqueeTextView
        android:id="@+id/plugin_desc_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/plugin_function_tv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/plugin_icon_iv"
        app:layout_constraintEnd_toStartOf="@id/plugin_switch"
        android:layout_marginEnd="10dp"
        android:ellipsize="marquee"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:text="点击设置位置"
        android:textColor="@color/white"
        android:layout_marginStart="10dp"
        android:layout_marginTop="6dp"
        />

    <Switch
        android:id="@+id/plugin_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:checked="true"
        android:thumb="@drawable/thumb"
        android:track="@drawable/track"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>