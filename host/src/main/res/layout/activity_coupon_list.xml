<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1A1A1A"
    android:orientation="vertical">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_700"
        android:paddingBottom="12dp">

        <!-- 内容容器，确保在状态栏下方 -->
        <LinearLayout
            android:id="@+id/title_content"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="返回"
                android:padding="12dp"
                android:src="@drawable/ic_back_white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="卡券记录"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 占位，保持标题居中 -->
            <View
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </LinearLayout>

    </RelativeLayout>

    <!-- 内容区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 优惠券列表 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_coupon_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:clipToPadding="false" />

            <!-- 底部提示 -->
            <TextView
                android:id="@+id/tv_bottom_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="16dp"
                android:text="暂无更多"
                android:textColor="#666666"
                android:textSize="12sp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 空状态视图 -->
        <LinearLayout
            android:id="@+id/ll_empty_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="暂无优惠券"
                android:textColor="#999999"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="邀请好友获取更多优惠券"
                android:textColor="#CCCCCC"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 加载状态视图 -->
        <LinearLayout
            android:id="@+id/ll_loading_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminateTint="@color/multiapp_brand_red" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="加载中..."
                android:textColor="#999999"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
