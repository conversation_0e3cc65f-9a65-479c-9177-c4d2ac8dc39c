<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="10dp">

    <ImageView
        android:id="@+id/iv_app_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"/>

    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@id/iv_app_icon"/>

    <TextView
        android:id="@+id/tv_abi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@id/btn_add"
        android:layout_marginRight="16dp"
        android:visibility="gone"
        tools:text="32位"/>

    <TextView
        android:id="@+id/btn_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:background="@drawable/shape_bg_btn_add"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:text="添加"/>
</RelativeLayout>