<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="20dp" />
            <solid android:color="#60FFFFFF" />
            <stroke 
                android:width="1dp"
                android:color="#80FFFFFF" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="20dp" />
            <solid android:color="#40FFFFFF" />
            <stroke 
                android:width="1dp"
                android:color="#60FFFFFF" />
        </shape>
    </item>
    
</selector>
