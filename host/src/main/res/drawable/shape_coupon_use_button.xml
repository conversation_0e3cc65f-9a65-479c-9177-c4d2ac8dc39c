<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 正常状态 -->
    <item android:state_enabled="true">
        <shape>
            <solid android:color="@color/multiapp_brand_red" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- 禁用状态 -->
    <item android:state_enabled="false">
        <shape>
            <solid android:color="#666666" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <solid android:color="#CC3A3A" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
