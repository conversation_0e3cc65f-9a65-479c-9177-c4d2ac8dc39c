package com.xyz.host.sp;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.xyz.host.MainApp;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class AppSharePref {
   private SharedPreferences mSharedPreferences = null;
   private static volatile AppSharePref sSnapSharePref;

   public static AppSharePref get() {
      if (sSnapSharePref == null) {
         synchronized (AppSharePref.class) {
            if (sSnapSharePref == null) {
               sSnapSharePref = new AppSharePref(MainApp.getApp());
            }
         }
      }
      return sSnapSharePref;
   }

   private AppSharePref(Context context) {
      if (context == null) {
         context = MainApp.getApp();
      }
      mSharedPreferences = context.getSharedPreferences("v_app", Context.MODE_MULTI_PROCESS);
   }

   /**************************基本方法定义************************************/
   public void putBoolean(String keyString, boolean value) {
      mSharedPreferences.edit().putBoolean(keyString, value).apply();
   }

   public boolean getBoolean(String keyString) {
      return mSharedPreferences.getBoolean(keyString, false);
   }

   public boolean getBoolean(String keyString, boolean defValue) {
      return mSharedPreferences.getBoolean(keyString, defValue);
   }

   public void putString(String keyString, String value) {
      mSharedPreferences.edit().putString(keyString, value).apply();
   }

   public String getString(String keyString) {
      return mSharedPreferences.getString(keyString, "");
   }

   public String getString(String keyString, String defValue) {
      return mSharedPreferences.getString(keyString, defValue);
   }

   public void putInt(String keyString, int value) {
      mSharedPreferences.edit().putInt(keyString, value).apply();
   }

   public int getInt(String keyString) {
      return mSharedPreferences.getInt(keyString, -1);
   }

   public int getInt(String keyString, int defaultValue) {
      return mSharedPreferences.getInt(keyString, defaultValue);
   }

   public void putLong(String keyString, long value) {
      mSharedPreferences.edit().putLong(keyString, value).apply();
   }

   public long getLong(String keyString) {
      return mSharedPreferences.getLong(keyString, -1);
   }

   public long getLong(String keyString, long defValue) {
      return mSharedPreferences.getLong(keyString, defValue);
   }

   /**
    * 保存List
    * @param tag
    * @param datalist
    */
   public <T> void putList(String tag, List<T> datalist) {
      if (null == datalist)
         return;
      putString(tag, MainApp.getGson().toJson(datalist));
   }

   /**
    * 获取List
    * @param tag
    * @return
    */
   public <T> List<T> getList(Class<T> clazz,String tag) {
      List<T> datalist = new ArrayList<T>();
      String strJson = getString(tag);
      if (TextUtils.isEmpty(strJson)) {
         return datalist;
      }
      Type listType = com.google.gson.internal.$Gson$Types.newParameterizedTypeWithOwner(null, ArrayList.class, clazz);
      datalist = MainApp.getGson().fromJson(strJson, listType);
      return datalist;
   }

   /***************************业务 KEY 的定义***********************************/
   public static final String KEY_AGREE_POLICY = "agree_policy";

   public static final String TIP_CONTENT = "tip_content";     // 内容， html格式

   public static final String UMENG_CHANNEL = "UMENG_CHANNEL";
   public static final String CHANNEL_HUAWEI = "huawei";

   public static final String TIP_DETAIL_CONTENT = "tip_detail_content";
   public static final String TIP_DETAIL_CONFIG = "tip_detail_config";
   public static final String CONFIG_FUNCTION = "function_config";
   public static final String CONFIG_FUNCTION_EXT = "function_ext_config";

   public static final String KEY_USER_INFO = "user_info";
   public static final String KEY_SHOW_USE_GUIDE = "show_use_guide";
   public static final String KEY_HIDE_PWD = "hide_pwd";


   public static final String Has_Click_Item_More = "hasClickItemMore";
   public static final String HAS_LONG_PRESSED_APP = "hasLongPressedApp";
}