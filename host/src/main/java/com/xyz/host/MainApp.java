package com.xyz.host;

import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;

import com.amap.api.services.core.ServiceSettings;
import com.fun.vbox.client.core.SettingConfig;
import com.fun.vbox.client.core.VCore;
import com.google.gson.Gson;
import com.umeng.analytics.MobclickAgent;
import com.umeng.cconfig.RemoteConfigSettings;
import com.umeng.cconfig.UMRemoteConfig;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.socialize.PlatformConfig;
import com.xyz.host.ads.TTAdManagerHolder;
import com.xyz.host.db.DbManager;
import com.xyz.host.delegate.MyComponentDelegate;
import com.xyz.host.home.VCameraActivity;
import com.xyz.host.maskprivacy.PrivacyUtils;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.ProcessUtil;

import jonathanfinerty.once.Once;

public class MainApp extends Application {
    private static MainApp gApp;
    private static SharedPreferences gSharedPrefs;
    private static final Gson gson = new Gson();
    private boolean mIsInit;

    public static MainApp getApp() {
        return gApp;
    }

    public static SharedPreferences getSharedPrefs() {
        return gSharedPrefs;
    }

    public static Gson getGson() {
        return gson;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        gApp = this;
        VCore.get().startup(base, this, new SettingConfig() {
            @Override
            public String getHostPackageName() {
                return "com.yingyu.zh";
            }

            @Override
            public String getExtPackageName() {
                return "com.yingyu.zh31";
            }

            @Override
            public Intent onHandleCameraIntent(Intent originIntent) {
                originIntent.setComponent(new ComponentName(
                        getHostPackageName(), VCameraActivity.class.getName()));
                return originIntent;
            }
        });
    }

    @Override
    public void onCreate() {
        super.onCreate();
        gSharedPrefs = getSharedPreferences("config", Context.MODE_PRIVATE);

        if (ProcessUtil.isMainProcess(this)) {
            Once.initialise(this);
            DbManager.getInstance().init(this);
        }

        if (!PrivacyUtils.isAllowPrivacy()) {
            android.util.Log.e("PrivacyGuard", "yes");
            return;
        }

        initWhenPrivacyAgreed();
    }

    public void initWhenPrivacyAgreed() {
        if (!PrivacyUtils.isAllowPrivacy()) {
            return;
        }
        if (mIsInit) {
            return;
        }
        VCore virtualCore = VCore.get();
        virtualCore.initialize(new VCore.VirtualInitializer() {
            @Override
            public void onMainProcess() {
                super.onMainProcess();
                if (!VCore.get().isEngineLaunched()) {
                    VCore.get().waitForEngine();
                }
                initUM();
                TTAdManagerHolder.init(gApp);

                ServiceSettings.updatePrivacyShow(gApp,true,true);
                ServiceSettings.updatePrivacyAgree(gApp,true);
                UserAgent.getInstance().init();
                // VirtualEngineCall.startForeground();
            }

            @Override
            public void onVirtualProcess() {
                super.onVirtualProcess();
                virtualCore.registerActivityLifecycleCallback(new MyComponentDelegate());
            }
        });
        mIsInit = true;
    }

    private void initUM() {
        //友盟初始化
        UMConfigure.preInit(this, "686dd0f7bc47b67d83a8eda9",
                CommonUtils.getMetaData(this, "UMENG_CHANNEL"));
        if (PrivacyUtils.isAllowPrivacy()) {
            UMRemoteConfig.getInstance().init(this);
            UMRemoteConfig.getInstance().setConfigSettings(new RemoteConfigSettings.Builder().setAutoUpdateModeEnabled(true).build());
            UMRemoteConfig.getInstance().setDefaults(R.xml.cloud_config_parms);
            UMConfigure.init(this, "686dd0f7bc47b67d83a8eda9",
                    CommonUtils.getMetaData(this, "UMENG_CHANNEL"),
                    UMConfigure.DEVICE_TYPE_PHONE, null);
        }

        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.AUTO);
        UMConfigure.setLogEnabled(BuildConfig.DEBUG);

        PlatformConfig.setWeixin("wxbcafdc841a20840f", "f12fef9bd96d6624430d5015e16107e3");
        PlatformConfig.setWXFileProvider(getPackageName() + ".provider");
        PlatformConfig.setQQZone("102800656", "Z0FLGzXv791f7NQK");
        PlatformConfig.setQQFileProvider(getPackageName() + ".provider");
    }
}
