package com.xyz.host.model;

import android.view.View;

public class ToolBean {
    private String name;
    private String hint;
    private int iconResId;
    private View.OnClickListener onClickListener;

    public ToolBean(){

    }
    public String getName() {
        return name;
    }

    public ToolBean setName(String name) {
        this.name = name;
        return this;
    }

    public String getHint() {
        return hint;
    }

    public ToolBean setHint(String hint) {
        this.hint = hint;
        return this;
    }

    public int getIconResId() {
        return iconResId;
    }

    public ToolBean setIconResId(int iconResId) {
        this.iconResId = iconResId;
        return this;
    }

    public View.OnClickListener getOnClickListener() {
        return onClickListener;
    }

    public ToolBean setOnClickListener(View.OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
        return this;
    }
}
