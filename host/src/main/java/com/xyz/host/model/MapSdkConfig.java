package com.xyz.host.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * [{
 * 	"p": "com.immomo.momo",
 * 	"s": "04:47:FA:95:5C:5C:94:3D:B0:04:F2:28:12:32:EB:AE:95:DF:FD:84:com.immomo.momo",
 * 	"a": "f0316c789154ffe6d796c07268ea0bb7"
 * }, {
 * 	"p": "com.duowan.mobile",
 * 	"s": "8B:7C:C2:1D:48:AD:8F:C6:F0:82:BF:E6:27:67:03:9A:75:64:0A:16:com.duowan.mobile",
 * 	"a": "a6fd544a587e7135941d3660eedd9c7c"
 * }, {
 * 	"p": "com.ss.android.article.news",
 * 	"s": "00:A5:84:E3:75:B5:57:3C:89:E1:F0:6F:5C:F6:0D:0D:65:DD:B6:32:com.ss.android.article.news",
 * 	"a": "366cd25e5dab888807cd295d18505796"
 * }, {
 * 	"p": "com.ss.android.ugc.aweme",
 * 	"s": "00:A5:84:E3:75:B5:57:3C:89:E1:F0:6F:5C:F6:0D:0D:65:DD:B6:32:com.ss.android.ugc.aweme",
 * 	"a": "fc1380ca35bc1ce958ea3bb6dade138e"
 * }]
 */
public class MapSdkConfig {
    @SerializedName("p")
    public String p;
    @SerializedName("s")
    public String s;
    @SerializedName("a")
    public String a;

    private static final List<MapSdkConfig> sList = new ArrayList<>();
    private static MapSdkConfig sMapSdkConfig = null;
    public static synchronized void init() {
        try {
            initPreset();
            String json = UMRemoteConfig.getInstance().getConfigValue("map_sdk_config");
            if (!TextUtils.isEmpty(json)) {
                Type type = new TypeToken<List<MapSdkConfig>>() {
                }.getType();
                List<MapSdkConfig> list = MainApp.getGson().fromJson(json, type);
                sList.addAll(list);
            }
        } catch (Throwable ignored) {
            //
        }
        sMapSdkConfig = initRandom();
        //Log.w("myne", "m:" + sMapSdkConfig.p);
    }

    public static synchronized MapSdkConfig getConfig() {
        return sMapSdkConfig;
    }

    private static void initPreset() {
        MapSdkConfig config = new MapSdkConfig();
        config.p = "com.immomo.momo";
        config.s = "04:47:FA:95:5C:5C:94:3D:B0:04:F2:28:12:32:EB:AE:95:DF:FD:84:com.immomo.momo";
        config.a = "f0316c789154ffe6d796c07268ea0bb7";
        sList.add(config);

        MapSdkConfig config2 = new MapSdkConfig();
        config2.p = "com.duowan.mobile";
        config2.s = "8B:7C:C2:1D:48:AD:8F:C6:F0:82:BF:E6:27:67:03:9A:75:64:0A:16:com.duowan.mobile";
        config2.a = "a6fd544a587e7135941d3660eedd9c7c";
        sList.add(config2);
    }

    private static MapSdkConfig initRandom() {
        try {
            int len = sList.size();
            int index = (int) (Math.random() * len);
            return sList.get(index);
        } catch (Throwable ignored) {
            return sList.get(0);
        }
    }
}
