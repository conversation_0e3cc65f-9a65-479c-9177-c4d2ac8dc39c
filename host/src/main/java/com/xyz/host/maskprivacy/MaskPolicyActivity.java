package com.xyz.host.maskprivacy;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.xyz.host.R;

public class MaskPolicyActivity extends AppCompatActivity {

    private String mAction;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_policy);

        mAction = getIntent().getStringExtra("action");

        ScrollView scrollView = findViewById(R.id.scroll_view);
        scrollView.smoothScrollTo(0,0);

        initData();
    }


    public void initData() {
        Toolbar mToolBar = findViewById(R.id.toolBar);
        setSupportActionBar(mToolBar);

        ActionBar bar = getSupportActionBar();
        if (bar != null) {
            bar.setDisplayHomeAsUpEnabled(true);
            bar.setHomeButtonEnabled(true);
        }
        mToolBar.setNavigationOnClickListener(v -> finish());

        if (!TextUtils.isEmpty(mAction)) {
            mToolBar.setTitle(mAction);
        }

        TextView contentTv = findViewById(R.id.provacy_tv);
        if (getString(R.string.service_use_policy).equals(mAction)) {
            contentTv.setText(R.string.user_policy);
        }
    }
}