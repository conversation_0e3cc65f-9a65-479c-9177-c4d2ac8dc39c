package com.xyz.host.maskprivacy;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;

import com.xyz.host.R;
import com.xyz.host.home.WebActivity;

public class MaskAgreeDialog extends AppCompatDialog {

    private final Context mContext;

    public MaskAgreeDialog(@NonNull Context context) {
        this(context, R.style.VBDialogTheme);
        initViews(context);
    }

    public MaskAgreeDialog(@NonNull Context context, int theme) {
        super(context, theme);
        initViews(context);
        setCanceledOnTouchOutside(false);
        this.mContext = context;

        if (getWindow() != null) {
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
    }

    private void initViews(Context context) {
        setContentView(R.layout.layout_dlg_agree);

        TextView mSubmitTv = findViewById(R.id.agree_tv);
        mSubmitTv.setOnClickListener(v -> {
            PrivacyUtils.markAllowPrivacy();
            dismiss();
        });
        TextView refuseTv = findViewById(R.id.refuse_tv);
        refuseTv.setOnClickListener(v -> {
            dismiss();
            ((Activity)mContext).finish();
        });

        TextView msgTv = findViewById(R.id.agree_polity_desc);
        String message = msgTv.getText().toString();

        SpannableStringBuilder ssb = new SpannableStringBuilder();
        ssb.append(message);
        final int start = message.indexOf("《");//第一个出现的位置
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                Intent privacyIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(WebActivity.PRIVACY_URL));
                mContext.startActivity(privacyIntent);
            }

            @Override

            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(mContext.getResources().getColor(R.color.green_agree));       //设置文件颜色
                // 去掉下划线
                ds.setUnderlineText(false);
            }

        }, start, start + 6, 0);

        final int end = message.lastIndexOf("《");//最后一个出现的位置

        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                Intent intent1 = new Intent(mContext, MaskPolicyActivity.class);
                intent1.putExtra("action", mContext.getString(R.string.service_use_policy));
                mContext.startActivity(intent1);
            }

            @Override

            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(mContext.getResources().getColor(R.color.green_agree));       //设置文件颜色
                // 去掉下划线
                ds.setUnderlineText(false);
            }

        }, end, end + 6, 0);

        msgTv.setMovementMethod(LinkMovementMethod.getInstance());
        msgTv.setText(ssb, TextView.BufferType.SPANNABLE);
    }
}