package com.xyz.host.maskprivacy;

import com.xyz.host.MainApp;
import com.xyz.host.sp.AppSharePref;

public class PrivacyUtils {
    public static boolean isAllowPrivacy() {
        return AppSharePref.get()
                .getBoolean(AppSharePref.KEY_AGREE_POLICY, false);
    }

    public static void markAllowPrivacy() {
        AppSharePref.get()
                .putBoolean(AppSharePref.KEY_AGREE_POLICY, true);
    }
}
