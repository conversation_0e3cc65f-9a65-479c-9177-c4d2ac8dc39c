package com.xyz.host.home.adpater;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.fun.vbox.client.ipc.VActivityManager;
import com.fun.vbox.client.ipc.VirtualLocationManager;
import com.xyz.host.R;
import com.xyz.host.model.PluginInfo;

import java.util.ArrayList;
import java.util.List;

public class AppPluginAdapter extends RecyclerView.Adapter<AppPluginAdapter.ViewHolder> {

    private List<PluginInfo> mPluginList;
    private onItemClickListener mClickListener;
    private onItemSwitchListener mSwitchListener;
    private Context mContext;
    private int mUserId;
    private String mPkg;

    public AppPluginAdapter(Context context, int userId, String pkg) {
        this.mContext = context;
        this.mUserId = userId;
        this.mPkg = pkg;
    }

    public List<PluginInfo> getList() {
        return mPluginList;
    }

    public PluginInfo getPosPluginInfo() {
        if (mPluginList.isEmpty()) {
            return null;
        }
        for (PluginInfo info: mPluginList) {
            if (info.type == PluginInfo.TYPE_POS) {
                return info;
            }
        }
        return null;
    }

    public void setList(List<PluginInfo> models) {
        this.mPluginList = models;
        notifyDataSetChanged();
    }

    public void add(PluginInfo info) {
        if (mPluginList == null) {
            mPluginList = new ArrayList<>();
        }
        mPluginList.add(info);
        notifyDataSetChanged();
    }

    public void clear() {
        if (mPluginList != null) {
            mPluginList.clear();
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(
                LayoutInflater.from(mContext).inflate(R.layout.item_app_plugin, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PluginInfo info = mPluginList.get(position);
        if (TextUtils.isEmpty(info.pkg)) {
            holder.itemView.setVisibility(View.GONE);
            return;
        }
        if (info.showSwitch) {
            holder.pluginSwitch.setVisibility(View.VISIBLE);
        } else {
            holder.pluginSwitch.setVisibility(View.GONE);
        }
        holder.iconView.setImageResource(info.iconId);
        holder.nameView.setText(info.name);
        holder.descTV.setText(info.desc);
        holder.itemView.setOnClickListener(v -> {
            if (info.type == PluginInfo.TYPE_POS2
                    || info.type == PluginInfo.TYPE_CAMERA) {
                holder.pluginSwitch.performClick();
            } else {
                mClickListener.onItemClick(info, position);
            }
        });

        if (info.type == PluginInfo.TYPE_POS) {
            holder.pluginSwitch.setChecked(VirtualLocationManager.get().getMode(mUserId, mPkg) != VirtualLocationManager.MODE_CLOSE);
            holder.pluginSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (mSwitchListener != null) {
                    mSwitchListener.onItemSwitch(info, isChecked);
                }
            });
        }

        if (info.type == PluginInfo.TYPE_POS2) {
            holder.pluginSwitch.setChecked(VirtualLocationManager.get().getEnableHook(mUserId, mPkg));
            holder.pluginSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                VirtualLocationManager.get().setEnableHook(mUserId, mPkg, isChecked);
            });
        }

        if (info.type == PluginInfo.TYPE_CAMERA) {
            int flag = VActivityManager.get().getAppOptionFlag(mPkg);
            boolean enable = (flag & VActivityManager.OPTION_CAMERA) != 0;
            holder.pluginSwitch.setChecked(enable);
            holder.pluginSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    VActivityManager.get().addAppOptionFlag(mPkg, VActivityManager.OPTION_CAMERA);
                } else {
                    VActivityManager.get().removeAppOptionFlag(mPkg,
                            VActivityManager.OPTION_CAMERA);
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return mPluginList == null ? 0 : mPluginList.size();
    }

    public void setOnItemClickListener(onItemClickListener listener) {
        mClickListener = listener;
    }

    public void setOnItemSwitchListener(onItemSwitchListener listener) {
        mSwitchListener = listener;
    }

    public interface onItemClickListener {

        void onItemClick(PluginInfo pluginInfo, int position);

    }

    public interface onItemSwitchListener {

        void onItemSwitch(PluginInfo pluginInfo, boolean isChecked);

    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView iconView;
        private TextView nameView;
        private TextView descTV;
        private Switch pluginSwitch;

        ViewHolder(View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.plugin_icon_iv);
            nameView = itemView.findViewById(R.id.plugin_function_tv);
            descTV = itemView.findViewById(R.id.plugin_desc_tv);
            pluginSwitch = itemView.findViewById(R.id.plugin_switch);
        }
    }
}

