package com.xyz.host.home.add;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.fun.vbox.utils.AbiUtils;
import com.xyz.host.R;
import com.xyz.host.db.AppInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class AppListAdapter extends RecyclerView.Adapter<AppListAdapter.ViewHolder> implements Filterable {

    private List<AppInfo> mAppList;
    private List<AppInfo> mFilterList;
    private final Activity mActivity;
    private boolean mIsAppHide = false;

    public AppListAdapter(Activity activity, boolean isAppHide) {
        this.mActivity = activity;
        mIsAppHide = isAppHide;
    }

    public AppListAdapter(Activity activity) {
        this.mActivity = activity;
    }

    public void setAppList(List<AppInfo> appList) {
        this.mAppList = appList;
        this.mFilterList = appList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = View.inflate(parent.getContext(), R.layout.item_add_app, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AppInfo appInfo = mFilterList.get(position);
        holder.ivAppIcon.setImageBitmap(appInfo.getAppIcon());
        holder.tvAppName.setText(appInfo.getAppName());
        if (!AbiUtils.is64BitAbi(appInfo.getApplicationInfo())) {
            holder.tvAbi.setVisibility(View.VISIBLE);
            holder.tvAbi.setText("32位");
        } else {
            holder.tvAbi.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return mFilterList == null ? 0 : mFilterList.size();
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            @Override
            protected FilterResults performFiltering(CharSequence constraint) {
                String charString = constraint.toString();
                if (TextUtils.isEmpty(charString)) {
                    mFilterList = mAppList;
                } else {
                    List<AppInfo> filteredList = new ArrayList<>();
                    for (AppInfo installAppItem : mAppList) {
                        if (installAppItem.getAppName().toLowerCase(Locale.ROOT)
                                .contains(charString.toLowerCase(Locale.ROOT))) {
                            filteredList.add(installAppItem);
                        }
                    }
                    mFilterList = filteredList;
                }

                FilterResults filterResults = new FilterResults();
                filterResults.values = mFilterList;
                return filterResults;
            }

            @SuppressLint("NotifyDataSetChanged")
            @Override
            protected void publishResults(CharSequence constraint, FilterResults results) {
                mFilterList = (List<AppInfo>) results.values;
                notifyDataSetChanged();
            }
        };
    }

    class ViewHolder extends RecyclerView.ViewHolder {

        private final ImageView ivAppIcon;
        private final TextView tvAppName;

        private final TextView tvAbi;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAppIcon = itemView.findViewById(R.id.iv_app_icon);
            tvAppName = itemView.findViewById(R.id.tv_app_name);
            tvAbi = itemView.findViewById(R.id.tv_abi);
            View btnAdd = itemView.findViewById(R.id.btn_add);

            btnAdd.setOnClickListener(v -> {
                AppInfo appInfo = mFilterList.get(getAdapterPosition());
                Intent intent = new Intent();
                intent.putExtra("appInfo", appInfo);
                if (mIsAppHide) {
                    intent.putExtra("type", "app_hide");
                }
                mActivity.setResult(101, intent);
                mActivity.finish();
            });
        }
    }
}
