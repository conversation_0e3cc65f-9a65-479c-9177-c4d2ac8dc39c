package com.xyz.host.home.adpater;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.xyz.host.db.LocInfo;

import java.util.ArrayList;
import java.util.List;

public class LocInfoAdapter extends RecyclerView.Adapter<LocInfoAdapter.ViewHolder> {

    private List<LocInfo> mAppList;
    private onItemClickListener mClickListener;
    private onItemClickListener mRemoveListener;
    private Context mContext;

    public LocInfoAdapter(Context context) {
        this.mContext = context;
    }

    public List<LocInfo> getList() {
        return mAppList;
    }

    public void setList(List<LocInfo> models) {
        this.mAppList = models;
        notifyDataSetChanged();
    }

    public void add(LocInfo info) {
        if (mAppList == null) {
            mAppList = new ArrayList<>();
        }
        mAppList.add(info);
    }

    public void add(int index, LocInfo info) {
        if (mAppList == null) {
            mAppList = new ArrayList<>();
        }
        mAppList.add(index, info);
    }

    public void remove(LocInfo info) {
        if (mAppList != null) {
            mAppList.remove(info);
        }
    }

    public void clear() {
        if (mAppList != null) {
            mAppList.clear();
        }
    }

    public void moveItem(int pos, int targetPos) {
        if (targetPos >= mAppList.size()) {
            targetPos = mAppList.size() - 1;
        }
        LocInfo model = mAppList.remove(pos);
        mAppList.add(targetPos, model);
        notifyItemMoved(pos, targetPos);
        notifyItemRangeChanged(Math.min(pos, targetPos), Math.abs(pos - targetPos) + 1);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(
                LayoutInflater.from(mContext).inflate(R.layout.checkin_pos_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        LocInfo info = mAppList.get(position);
        holder.label.setText(info.getName());
        holder.addr.setText(info.getAddress());
        if (info.getBaseStationId() == 0 && info.getLac() == 0) {
            holder.icon.setImageResource(R.drawable.ic_pos);
        } else {
            holder.icon.setImageResource(R.drawable.ic_base_station);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mClickListener != null) {
                    mClickListener.onItemClick(info, position);
                }
            }
        });
        holder.remove.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRemoveListener != null) {
                    mRemoveListener.onItemClick(info, position);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mAppList == null ? 0 : mAppList.size();
    }

    public void setOnItemClickListener(onItemClickListener listener) {
        mClickListener = listener;
    }

    public void setOnItemRemoveListener(onItemClickListener listener) {
        mRemoveListener = listener;
    }

    public interface onItemClickListener {
        void onItemClick(LocInfo pluginInfo, int position);
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView label;
        private TextView addr;
        private Button remove;
        private ImageView icon;

        ViewHolder(View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.icon);
            label = itemView.findViewById(R.id.tv_label);
            addr = itemView.findViewById(R.id.tv_addr);
            remove = itemView.findViewById(R.id.btn_remove);
        }
    }
}
