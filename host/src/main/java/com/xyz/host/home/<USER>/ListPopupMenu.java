package com.xyz.host.home.view;

import android.content.Context;
import android.view.ContextThemeWrapper;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.ListPopupWindow;

import androidx.core.content.ContextCompat;

import com.xyz.host.R;

public class ListPopupMenu {
    private final Context mContext;
    private final ListPopupWindow mListPopupWindow;
    private final ArrayAdapter<String> mModelAdapter;
    private boolean mShowMulti;

    private OnItemClickListener mOnItemClickListener;

    public enum MENU_ITEM {
        SHORTCUT, CUSTOM_NAME, DELETE, MORE
    }

    public ListPopupMenu(Context context) {
        mContext = context;

        ContextThemeWrapper contextThemeWrapper =
                new ContextThemeWrapper(context, R.style.CustomPopMenuStyle);
        mListPopupWindow = new ListPopupWindow(contextThemeWrapper);
        mListPopupWindow.setBackgroundDrawable(ContextCompat.getDrawable(mContext, R.drawable.popup_bg));
        String[] arr = context.getResources().getStringArray(R.array.app_menu);
        mModelAdapter = new ArrayAdapter<>(context, R.layout.item_spinner, arr);
        mListPopupWindow.setAdapter(mModelAdapter);
        mListPopupWindow.setWidth(dip2px(context, 120));
        mListPopupWindow.setHeight(ListPopupWindow.WRAP_CONTENT);
        mListPopupWindow.setModal(true);
        mListPopupWindow.setOnItemClickListener((parent, view, position, id) -> {
            handleMenuItem(MENU_ITEM.values()[position]);
        });
    }

    private void handleMenuItem(MENU_ITEM customName) {
        if (mOnItemClickListener != null) {
            mOnItemClickListener.onClick(customName);
        }
        mListPopupWindow.dismiss();
    }

    public void showLocation(View view) {
        mListPopupWindow.setAnchorView(view);
        mListPopupWindow.show();
    }

    public void showMultiMenu(boolean isShow) {
        mShowMulti = isShow;
    }

    private int dip2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    public interface OnItemClickListener {
        void onClick(MENU_ITEM item);
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

}