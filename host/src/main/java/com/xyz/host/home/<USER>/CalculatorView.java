package com.xyz.host.home.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.xyz.host.R;
import com.xyz.host.utils.CalculateUtils;

public class CalculatorView extends FrameLayout {
    protected String mExpression = "";
    protected boolean mLastEqual = false;

    private ICallback mCallback;

    public interface ICallback {
        void verify(String exp);
        void reset();
    }

    public CalculatorView(Context context) {
        this(context, null);
    }

    public CalculatorView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CalculatorView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    public void setCallback(ICallback callback) {
        mCallback = callback;
    }

    private Button btnPadNumber0;
    private Button btnPadNumber1;
    private Button btnPadNumber2;
    private Button btnPadNumber3;
    private Button btnPadNumber4;
    private Button btnPadNumber5;
    private Button btnPadNumber6;
    private Button btnPadNumber7;
    private Button btnPadNumber8;
    private Button btnPadNumber9;
    private ImageView btnPadDiv;
    private ImageView btnPadMul;
    private ImageView btnPadMinus;
    private ImageView btnPadPlus;
    private Button btnPadDot;
    private ImageView btnPadDel;
    private ImageView btnPadAc;
    private ImageView btnPadEqual;
    private Button btnPadEmpty;
    private EditText et1;
    private EditText et2;

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.layout_calc, this, true);
        btnPadNumber0 = findViewById(R.id.btn_pad_number_0);
        btnPadNumber1 = findViewById(R.id.btn_pad_number_1);
        btnPadNumber2 = findViewById(R.id.btn_pad_number_2);
        btnPadNumber3 = findViewById(R.id.btn_pad_number_3);
        btnPadNumber4 = findViewById(R.id.btn_pad_number_4);
        btnPadNumber5 = findViewById(R.id.btn_pad_number_5);
        btnPadNumber6 = findViewById(R.id.btn_pad_number_6);
        btnPadNumber7 = findViewById(R.id.btn_pad_number_7);
        btnPadNumber8 = findViewById(R.id.btn_pad_number_8);
        btnPadNumber9 = findViewById(R.id.btn_pad_number_9);
        btnPadDiv = findViewById(R.id.btn_pad_div);
        btnPadMul = findViewById(R.id.btn_pad_mul);
        btnPadMinus = findViewById(R.id.btn_pad_minus);
        btnPadPlus = findViewById(R.id.btn_pad_plus);
        btnPadDot = findViewById(R.id.btn_pad_dot);
        btnPadDel = findViewById(R.id.btn_pad_del);
        btnPadAc = findViewById(R.id.btn_pad_ac);
        btnPadEqual = findViewById(R.id.btn_pad_equal);
        btnPadEmpty = findViewById(R.id.btn_pad_empty);
        et1 = findViewById(R.id.et1);
        et2 = findViewById(R.id.et2);

        btnPadNumber1.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber2.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber3.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber4.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber5.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber6.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber7.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber8.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber9.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        btnPadNumber0.setOnClickListener(v -> {
            inputNum((Button) v);
        });
        // ➗
        btnPadDiv.setOnClickListener(v -> {
            updateMethod(String.valueOf((char) 247));
        });
        // *
        btnPadMul.setOnClickListener(v -> {
            updateMethod(String.valueOf((char) 215));
        });
        //-
        btnPadMinus.setOnClickListener(v -> {
            updateMethod(String.valueOf('-'));
        });
        //+
        btnPadPlus.setOnClickListener(v -> {
            updateMethod(String.valueOf('+'));
        });
        btnPadDot.setOnClickListener(v -> {
            updateMethod(".");
        });
        btnPadDel.setOnClickListener(v -> {
            if (mExpression.length() < 1) {
                return;
            }
            mExpression = mExpression.substring(0, mExpression.length() - 1);
            et2.setText(mExpression);
            et2.setSelection(mExpression.length());
            mLastEqual = false;
        });
        btnPadAc.setOnClickListener(v -> {
            inputAc();
        });
        btnPadEqual.setOnClickListener(v -> {
            inputEqual();
        });
        btnPadEmpty.setOnClickListener(v -> {
            inputE();
        });
    }

    private void inputAc() {
        mExpression = "";
        et2.setText("0");
        et1.setText(null);
        mLastEqual = false;
    }

    private int mEqualCount = 0;

    @SuppressLint("SetTextI18n")
    protected void inputEqual() {
        if (mLastEqual) {
            if (++mEqualCount >= 4) {
                mEqualCount = 0;
                if (mCallback != null) {
                    mCallback.reset();
                }
            }
            return; //如果上次还是按的等号，那么什么也不做
        }
        et1.setText(mExpression + "=");
        et1.setSelection(mExpression.length() + 1);//在第一行显示计算表达式
        try {
            mExpression = CalculateUtils.calculate(mExpression);
            et2.setText(mExpression);//在第二行显示计算结果
        } catch (Exception exception) {
            et2.setText("表达式错误!");//在第二行显示计算结果
            mExpression = "";
        }
        // 为下一次按计算器键盘做准备。
        // 如果下次按的是数字，那么清空第二行重新输入第一个数。
        // 如果是非数字，那就当这次的结果是输入的第一个数，直接参与运算。
        mLastEqual = true;
    }

    @SuppressLint("SetTextI18n")
    protected void inputE() {
        mExpression += (char) 215 + "2.718281828459045235";
        et1.setText(mExpression + "=");
        et1.setSelection(mExpression.length() + 1);//在第一行显示计算表达式
        try {
            mExpression = CalculateUtils.calculate(mExpression);
            et2.setText(mExpression);//在第二行显示计算结果
        } catch (Exception exception) {
            et2.setText("表达式错误!");//在第二行显示计算结果
            mExpression = "";
        }
        // 为下一次按计算器键盘做准备。
        // 如果下次按的是数字，那么清空第二行重新输入第一个数。
        // 如果是非数字，那就当这次的结果是输入的第一个数，直接参与运算。
        mLastEqual = true;
    }

    protected void updateMethod(String s) {
        mExpression += s;
        et2.setText(mExpression);
        et2.setSelection(mExpression.length());
        mLastEqual = false;
    }

    protected void inputNum(Button button) {
        if (mLastEqual) {
            mExpression = "";//这次按的数字，如果上次按了等号，则清空表达式
            mLastEqual = false;
        }
        mExpression += button.getText();
        et2.setText(mExpression);
        et2.setSelection(mExpression.length());
        if (mCallback != null) {
            mCallback.verify(mExpression);
        }
    }

    public void clear() {
        inputAc();
    }
}
