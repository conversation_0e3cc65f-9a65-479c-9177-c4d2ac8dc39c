package com.xyz.host.home.add;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.R;
import com.xyz.host.ads.TtBannerAdView;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.CommonUtils;
import com.yingyu.zh.multiapp.AdIdByReviewMode;

public class AddAppActivity extends AppCompatActivity {

    private final String[] tabs = {"安装应用", "内部存储"};

    private TabLayout tabLayout;
    private ViewPager2 viewPager2;

    private final int activeColor = Color.parseColor("#FFFFFF");
    private final int normalColor = Color.parseColor("#FFFFFF");

    private TabLayoutMediator mediator;
    private String mType = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_app);

        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("type")) {
            mType = intent.getStringExtra("type");
        }
        boolean isAppHide = "app_hide".equals(mType);

        // 获取控件对象
        Toolbar mToolBar = findViewById(R.id.toolBar);
        if (isAppHide) {
            tabs[0] = "隐藏应用";
        }
        setSupportActionBar(mToolBar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        mToolBar.setNavigationOnClickListener(v -> finish());

        tabLayout = findViewById(R.id.tab_layout);
        viewPager2 = findViewById(R.id.view_pager);

        //禁用预加载
        viewPager2.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);
        //Adapter
        viewPager2.setAdapter(new FragmentStateAdapter(getSupportFragmentManager(), getLifecycle()) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                Fragment fragment = null;
                if (position == 0) {
                    fragment = new AddAppFragment(isAppHide);
                } else {
                    fragment = new AddFileFragment();
                }
                return fragment;
            }

            @Override
            public int getItemCount() {
                return 1;
            }
        });
        //viewPager 页面切换监听
        viewPager2.registerOnPageChangeCallback(changeCallback);
        mediator = new TabLayoutMediator(tabLayout, viewPager2, (tab, position) -> {
            TextView tabView = new TextView(AddAppActivity.this);
            int px4 = CommonUtils.dip2px(this, 4f);
            tabView.setPadding(0, px4, 0, px4);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            tabView.setLayoutParams(params);

            int[][] states = new int[2][];
            states[0] = new int[]{android.R.attr.state_selected};
            states[1] = new int[]{};

            int[] colors = new int[]{activeColor, normalColor};
            ColorStateList colorStateList = new ColorStateList(states, colors);
            tabView.setText(tabs[position]);
            tabView.setTextColor(colorStateList);
            tabView.setGravity(Gravity.CENTER);
            tab.setCustomView(tabView);
        });
        //要执行这一句才是真正将两者绑定起来
        mediator.attach();

        if (!UserAgent.getInstance().isVipUser()) {
            TtBannerAdView adView = findViewById(R.id.bannerAd);
            String codeId = AdIdByReviewMode.getAdId("app_list_ads_id");
            adView.load(this, codeId);
        }
    }

    private ViewPager2.OnPageChangeCallback changeCallback = new ViewPager2.OnPageChangeCallback() {
        @Override
        public void onPageSelected(int position) {
            //可以来设置选中时tab的大小
            int tabCount = tabLayout.getTabCount();
            for (int i = 0; i < tabCount; i++) {
                TabLayout.Tab tab = tabLayout.getTabAt(i);
                TextView tabView = (TextView) tab.getCustomView();
                if (tab.getPosition() == position) {
                    tabView.setBackgroundResource(R.drawable.shape_bg_tablayout_select);
                } else {
                    tabView.setBackgroundResource(0);
                }
            }
        }
    };


    @Override
    protected void onDestroy() {
        mediator.detach();
        viewPager2.unregisterOnPageChangeCallback(changeCallback);
        super.onDestroy();
    }
}