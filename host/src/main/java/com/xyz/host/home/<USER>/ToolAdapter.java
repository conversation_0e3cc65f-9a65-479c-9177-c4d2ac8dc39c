package com.xyz.host.home.adpater;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.xyz.host.model.ToolBean;

import java.util.ArrayList;

public class ToolAdapter extends RecyclerView.Adapter<ToolAdapter.ViewHolder> {

    private ArrayList<ToolBean> mTools;
    private Context mContext;

    public ToolAdapter(Context context, ArrayList<ToolBean> list) {
        mContext = context;
        mTools = list;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_tool, parent,
                false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ToolBean toolBean = mTools.get(position);
        holder.ivFun.setImageResource(toolBean.getIconResId());
        holder.tvFunName.setText(toolBean.getName());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                toolBean.getOnClickListener().onClick(v);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mTools.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {

        public ImageView ivFun;
        public TextView tvFunName;
        public TextView tvFunHint;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivFun = itemView.findViewById(R.id.ivFun);
            tvFunName = itemView.findViewById(R.id.tvFunName);
        }
    }
}
