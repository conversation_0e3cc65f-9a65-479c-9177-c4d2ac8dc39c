package com.xyz.host.home.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.os.Build;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.util.Pair;

public class OverlayWithHoleFrameLayout extends FrameLayout {
    public OverlayWithHoleFrameLayout(@NonNull Context context) {
        super(context);
        prepareView(context);
    }

    public OverlayWithHoleFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        prepareView(context);
    }

    public OverlayWithHoleFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        prepareView(context);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public OverlayWithHoleFrameLayout(@NonNull Context context,
                                      @Nullable AttributeSet attrs,
                                      int defStyleAttr,
                                      int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        prepareView(context);
    }

    /**
     * 初始化view设置
     *
     * @param context
     */
    private void prepareView(Context context) {
        setWillNotDraw(false);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (null != rect) {
            return !isInHole(ev);
        } else {
            return super.dispatchTouchEvent(ev);
        }
    }

    /**
     * 判断事件是否在hole里面
     *
     * @param ev
     * @return
     */
    private boolean isInHole(MotionEvent ev) {
        return ev.getRawY() >= rect.top && ev.getRawY() <= rect.bottom
                && ev.getRawX() >= rect.left && ev.getRawX() <= rect.right;
    }

    private int overlayColor = 0xcc000000;

    /**
     * 设置遮罩颜色
     *
     * @param color
     */
    public void setOverlayColor(int color) {
        if (this.overlayColor == color) {
            return;
        }
        this.overlayColor = color;
        //
        if (null != rect) {
            setOverlayHole(rect, radius);
        }
    }

    private float borderWidth = 0;
    private int borderColor;

    /**
     * 设置边框
     *
     * @param width
     * @param color
     */
    public void setOverlayBorder(float width, int color) {
        if (this.borderWidth == width && this.borderColor == color) {
            return;
        }
        this.borderWidth = 2 * width;
        this.borderColor = color;
        //
        if (null != rect) {
            setOverlayHole(rect, radius);
        }
    }

    /**
     * 设定裁剪位置
     *
     * @param left
     * @param top
     * @param right
     * @param bottom
     * @param radius
     */
    public void setOverlayHole(int left, int top, int right, int bottom, float radius) {
        RectF rect = new RectF(left, top, right, bottom);
        setOverlayHole(rect, radius);
    }

    private RectF rect;
    private float radius;

    /**
     * 设定裁剪位置
     *
     * @param rect
     * @param radius
     */
    public void setOverlayHole(RectF rect, float radius) {
        if (null == rect) {
            return;
        }
        this.rect = rect;
        this.radius = radius;
        //
        if (null == overlay || overlay.isRecycled()) {
            Pair<Integer, Integer> size = obtainOverlaySize();
            overlay = Bitmap.createBitmap(size.first, size.second, Bitmap.Config.ARGB_8888);
        }
        overlay.eraseColor(Color.TRANSPARENT);
        Canvas canvas = new Canvas(overlay);
        canvas.drawColor(overlayColor);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        if (borderWidth > 0) {
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(borderWidth);
            paint.setColor(borderColor);
            canvas.drawRoundRect(rect, radius, radius, paint);
        }
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(Color.TRANSPARENT);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        canvas.drawRoundRect(rect, radius, radius, paint);
        canvas.setBitmap(null);
        //
        postInvalidate();
    }

    /**
     * 获取view尺寸
     *
     * @return
     */
    private Pair<Integer, Integer> obtainOverlaySize() {
        if (getMeasuredWidth() > 0 && getMeasuredHeight() > 0) {
            return new Pair<>(getMeasuredWidth(), getMeasuredHeight());
        }
        View parent = (View) getParent();
        if (null != parent && parent.getMeasuredWidth() > 0 && parent.getMeasuredHeight() > 0) {
            return new Pair<>(parent.getMeasuredWidth(), parent.getMeasuredHeight());
        }
        DisplayMetrics display = getContext().getResources().getDisplayMetrics();
        return new Pair<>(display.widthPixels, display.heightPixels);
    }

    private Bitmap overlay;

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (null != overlay && !overlay.isRecycled()) {
            canvas.drawBitmap(overlay, 0, 0, null);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (null != overlay && !overlay.isRecycled()) {
            overlay.recycle();
        }
        overlay = null;
    }
}
