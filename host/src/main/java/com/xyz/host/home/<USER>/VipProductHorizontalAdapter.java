package com.xyz.host.home.adpater;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.xyz.host.model.VipInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * VIP产品水平滑动适配器
 */
public class VipProductHorizontalAdapter extends RecyclerView.Adapter<VipProductHorizontalAdapter.ViewHolder> {
    private List<VipInfo.Product> mProductList;
    private onItemClickListener mClickListener;
    private Context mContext;

    public VipProductHorizontalAdapter(Context context) {
        this.mContext = context;
    }

    public List<VipInfo.Product> getList() {
        return mProductList;
    }

    public void setList(List<VipInfo.Product> models) {
        this.mProductList = models;
        notifyDataSetChanged();
    }

    public void add(VipInfo.Product info) {
        if (mProductList == null) {
            mProductList = new ArrayList<>();
        }
        mProductList.add(info);
        notifyDataSetChanged();
    }

    public void clear() {
        if (mProductList != null) {
            mProductList.clear();
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_vip_horizontal, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        VipInfo.Product info = mProductList.get(position);
        
        // 设置标题
        holder.title.setText(info.title);
        
        // 设置价格
        holder.price.setText("¥" + info.price);
        
        // 设置原价和折扣标签（根据产品类型）
        if ("year".equals(info.type)) { // 年卡显示折扣
            holder.leftBottomTag.setVisibility(View.GONE);
            holder.llOriginalPrice.setVisibility(View.VISIBLE);
            holder.originalPrice.setText("¥300");
            holder.discountTag.setVisibility(View.VISIBLE);
            // 动态计算年卡每天费用
            String dailyCost = calculateDailyCost(info.price, 365);
            holder.discountTag.setText("折合¥" + dailyCost + "/天");
        } else if ("season".equals(info.type)) {
            holder.leftBottomTag.setVisibility(View.GONE);
            holder.llOriginalPrice.setVisibility(View.VISIBLE);
            holder.originalPrice.setText("¥120");
            holder.discountTag.setVisibility(View.VISIBLE);
            // 动态计算季卡每天费用
            String dailyCost = calculateDailyCost(info.price, 90);
            holder.discountTag.setText("折合¥" + dailyCost + "/天");
        } else if ("month".equals(info.type)) { // 月卡显示每天费用
            holder.leftBottomTag.setVisibility(View.GONE);
            holder.llOriginalPrice.setVisibility(View.GONE);
            holder.discountTag.setVisibility(View.VISIBLE);
            // 动态计算月卡每天费用
            String dailyCost = calculateDailyCost(info.price, 30);
            holder.discountTag.setText("折合¥" + dailyCost + "/天");
        } else if ("forever".equals(info.type)){
            holder.leftBottomTag.setVisibility(View.VISIBLE);
            holder.llOriginalPrice.setVisibility(View.GONE);
            holder.discountTag.setVisibility(View.GONE);
        }
        
        // 设置选中状态
        if (info.checked) {
            holder.itemView.setBackgroundResource(R.drawable.vip_card_selected_bg);
        } else {
            holder.itemView.setBackgroundResource(R.drawable.vip_card_normal_bg);
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (mClickListener != null) {
                mClickListener.onItemClick(info, holder.getAdapterPosition());
            }
        });
    }

    @Override
    public int getItemCount() {
        return mProductList == null ? 0 : mProductList.size();
    }

    public void setOnItemClickListener(onItemClickListener listener) {
        mClickListener = listener;
    }

    /**
     * 计算每天费用
     * @param price 总价格（字符串格式）
     * @param days 天数
     * @return 格式化的每天费用字符串（保留2位小数）
     */
    private String calculateDailyCost(String price, int days) {
        try {
            double totalPrice = Double.parseDouble(price);
            double dailyCost = totalPrice / days;
            // 保留2位小数
            return String.format("%.2f", dailyCost);
        } catch (NumberFormatException e) {
            // 如果价格解析失败，返回默认值
            return "0.00";
        }
    }

    public interface onItemClickListener {
        void onItemClick(VipInfo.Product vipInfo, int position);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private TextView title;
        private TextView price;
        private TextView originalPrice;

        private LinearLayout llOriginalPrice;
        private TextView discountTag;

        private TextView leftBottomTag;

        ViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.tv_vip_title);
            price = itemView.findViewById(R.id.tv_vip_price);
            originalPrice = itemView.findViewById(R.id.tv_original_price);
            llOriginalPrice = itemView.findViewById(R.id.ll_original_price);
            discountTag = itemView.findViewById(R.id.tv_discount_tag);
            leftBottomTag = itemView.findViewById(R.id.tv_leftbottom_tag);
        }
    }
}
