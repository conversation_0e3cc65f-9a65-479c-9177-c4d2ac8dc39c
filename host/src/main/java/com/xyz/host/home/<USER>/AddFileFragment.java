package com.xyz.host.home.add;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.xyz.host.db.AppInfo;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.ReflectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class AddFileFragment extends Fragment {

    private AppListAdapter appListAdapter;
    private List<AppInfo> appInfos;

    private TextView tvTips;

    private int iconSize = 100;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_add_app, container, false);
        init(rootView);
        return rootView;
    }

    private void init(View rootView) {
        iconSize = CommonUtils.dip2px(getContext(), 50f);
        RecyclerView rvAppList = rootView.findViewById(R.id.rv_app_list);
        tvTips = rootView.findViewById(R.id.tv_tips);

        rvAppList.setLayoutManager(new LinearLayoutManager(getContext()));

        appListAdapter = new AppListAdapter(getActivity());
        rvAppList.setAdapter(appListAdapter);

        new Thread() {
            @Override
            public void run() {
                super.run();
                appInfos = new ArrayList<>();
                // Sdcard
                File sdcardFile = null;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    StorageManager storage = (StorageManager) getContext().getSystemService(Context.STORAGE_SERVICE);
                    for (StorageVolume volume : storage.getStorageVolumes()) {
                        //getUserLabel
                        sdcardFile = (File) ReflectionUtils.getFieldValue(volume, "mPath");
                    }
                } else {
                    File storageFir = Environment.getExternalStorageDirectory();
                    if (storageFir != null && storageFir.isDirectory()) {
                        sdcardFile = storageFir;
                    }
                }

                findApk(sdcardFile);
                FragmentActivity activity = getActivity();
                if (CommonUtils.isLiveActivity(activity)) {
                    activity.runOnUiThread(() -> {
                        appListAdapter.setAppList(appInfos);
                        appListAdapter.notifyDataSetChanged();
                        if (!appInfos.isEmpty()) {
                            tvTips.setVisibility(View.GONE);
                        } else {
                            tvTips.setText(getString(R.string.no_data));
                        }
                    });
                }
            }
        }.start();
    }

    private void findApk(File file) {
        if (file == null) {
            return;
        }

        File[] files = file.listFiles();
        if (files == null) {
            return;
        }

        for (File item : files) {
            if (item.isFile()) {
                String filePath = item.getAbsolutePath();
                if (filePath.endsWith(".apk")) {
                    if (getContext() == null) {
                        return;
                    }
                    PackageInfo packageInfo = getContext().getPackageManager().getPackageArchiveInfo(filePath, PackageManager.GET_PERMISSIONS);
                    if (packageInfo == null) {
                        return;
                    }
                    ApplicationInfo applicationInfo = packageInfo.applicationInfo;
                    applicationInfo.sourceDir = filePath;
                    applicationInfo.publicSourceDir = filePath;
                    Bitmap appIcon = CommonUtils.drawable2Bitmap(applicationInfo.loadIcon(getContext().getPackageManager()));
                    if (appIcon != null) {
                        appIcon = Bitmap.createScaledBitmap(appIcon, iconSize, iconSize, true);
                    }
                    AppInfo appInfo = new AppInfo();
                    appInfo.setInstallType("file");
                    appInfo.setPackageName(packageInfo.applicationInfo.packageName);
                    appInfo.setAppIcon(appIcon);
                    appInfo.setAppName(packageInfo.applicationInfo.loadLabel(getContext().getPackageManager()).toString());
                    appInfo.setApkPath(filePath);
                    appInfo.setApplicationInfo(applicationInfo);
                    appInfos.add(appInfo);
                }
            } else if (item.isDirectory()) {
                findApk(item);
            }
        }
    }

}
