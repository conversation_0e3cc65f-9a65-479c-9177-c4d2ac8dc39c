package com.xyz.host.home.view;

import android.content.Context;
import android.text.Html;
import android.text.Spanned;
import android.util.Base64;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatDialog;

import com.xyz.host.R;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.sp.AppSharePref;

import java.nio.charset.StandardCharsets;

public class NumberInputDialog extends AppCompatDialog implements View.OnClickListener {
    private TextView mOkTv;
    private Context mContext;
    private EditText mEdtPass;


    public NumberInputDialog(Context context) {
        super(context, R.style.VBDialogTheme);
        setContentView(R.layout.dialog_number_input);
        this.mContext = context;


        initView();
        initData();

        if (getWindow() != null) {
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
    }


    private void initData() {

    }

    private void initView() {
        mOkTv = findViewById(R.id.native_ok);
        mOkTv.setOnClickListener(this);
        findViewById(R.id.native_cancel).setOnClickListener(this);
        findViewById(R.id.tv_reset).setOnClickListener(this);
        mEdtPass = findViewById(R.id.edt_pass);
        TextView textMsg = findViewById(R.id.native_msg_tv);
        textMsg.setText(Html.fromHtml("设置密码为数字，至少4位 <br/> 如果忘记密码，<font color='red' size='20'>在计算器界面连续点击'='号5次，会解除密码</font>"));
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.native_cancel) {
            dismiss();
        } else if (v.getId() == R.id.tv_reset) {
            new CommonDialog.Builder(getContext())
                    .setMessage("确认清除密码吗，清除后打开App无需输入密码则可以进入")
                    .setCallback(new CommonDialog.Callback() {
                        @Override
                        public void onOk() {
                            AppSharePref.get().putString(AppSharePref.KEY_HIDE_PWD, "");
                            Toast.makeText(getContext(), "清除成功", Toast.LENGTH_SHORT).show();
                            dismiss();
                            NumberInputDialog.this.dismiss();
                        }
                    })
                    .show();
        } else if (v.getId() == R.id.native_ok) {
            String numberStr = mEdtPass.getText().toString();
            if (numberStr.length() < 4) {
                Toast.makeText(getContext(), "需要输入4位或以上", Toast.LENGTH_SHORT).show();
            } else {
                gotoSetting(numberStr);
                dismiss();
            }
        }
    }


    private void gotoSetting(String numberStr) {
        try {
            String encodedNumber = Base64.encodeToString(
                    numberStr.getBytes(StandardCharsets.UTF_8), Base64.DEFAULT);
            AppSharePref.get().putString(AppSharePref.KEY_HIDE_PWD, encodedNumber);

            Spanned text = Html.fromHtml("您设置的密码是：<font color='red' size='20'>" + numberStr + "</font>,请牢记密码 <br/> 如果忘记密码，<font color='red' size='20'>在计算器界面连续点击'='号5次，会解除密码</font>");
            new CommonDialog.Builder(getContext())
                    .setMessage(text)
                    .show();
        } catch (Throwable e) {
            Toast.makeText(getContext(), "设置失败，稍候重试", Toast.LENGTH_SHORT).show();
        }
    }


}
