package com.xyz.host.home.view;

import android.animation.ValueAnimator;
import android.animation.ValueAnimator.AnimatorUpdateListener;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.widget.ImageView;

@SuppressLint("AppCompatCustomView")
public class SplashImageView extends ImageView {
    private final int b;
    private float c;
    private final int d;
    private float e;
    private final int f;
    private float g;
    private final int h;
    private float i;
    private Paint j;
    private int k;
    private int l;
    private int m;
    private int n;
    public int p;
    private final ValueAnimator r;
    private int s;
    private int t;
    private boolean u;

    public SplashImageView(Context var1) {
        this(var1, (AttributeSet)null);
    }

    public SplashImageView(Context var1, AttributeSet var2) {
        this(var1, var2, 0);
    }

    public SplashImageView(Context var1, AttributeSet var2, int var3) {
        super(var1, var2, var3);
        this.b = Color.parseColor("#00fff6");
        this.c = 4.0F;
        this.d = Color.parseColor("#ff8dcb");
        this.e = 5.0F;
        this.f = Color.parseColor("#FFC600");
        this.g = 4.0F;
        this.h = Color.parseColor("#FF6262");
        this.i = 3.0F;
        this.p = 54;
        this.r = ValueAnimator.ofInt(0, 300);
        this.s = -90;
        this.t = 0;
        this.e(var1);
    }

    private void e(Context var1) {
        float var2 = var1.getResources().getDisplayMetrics().density;
        this.c *= var2;
        this.e *= var2;
        this.g *= var2;
        this.i *= var2;
        this.p = (int)((float)this.p * var2);
        Paint var3 = new Paint();
        this.j = var3;
        var3.setAntiAlias(true);
        this.r.setRepeatMode(ValueAnimator.RESTART);
        this.r.setDuration(4000L);
        this.r.setRepeatCount(-1);
        this.r.addUpdateListener(new UpdateListener(this));
        this.u = true;
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (this.u && !this.r.isRunning()) {
            this.r.start();
        }

    }

    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (this.u) {
            this.r.cancel();
        }

    }

    public void onDraw(Canvas var1) {
        float var2 = (float)Math.round((float)this.s * 0.9F);

        float var3 = (float)this.getWidth() / 2.0F;
        float var4 = (float)this.getHeight() / 2.0F;
        var1.translate(var3, var4);
        var1.rotate(var2);
        this.j.setColor(this.b);
        var1.drawCircle(0.0F, (float)(-this.k * this.t) / 100.0F - (float)this.p, this.c, this.j);
        var1.rotate(90.0F);
        this.j.setColor(this.d);
        var1.drawCircle(0.0F, (float)(-this.l * this.t) / 100.0F - (float)this.p, this.e, this.j);
        var1.rotate(90.0F);
        this.j.setColor(this.f);
        var1.drawCircle(0.0F, (float)(-this.m * this.t) / 100.0F - (float)this.p, this.g, this.j);
        var1.rotate(90.0F);
        this.j.setColor(this.h);
        var1.drawCircle(0.0F, (float)(-this.n * this.t) / 100.0F - (float)this.p, this.i, this.j);
        var1.rotate(-270.0F);
        var1.rotate(-var2);
        var1.translate(-var3, -var4);
        super.onDraw(var1);
    }

    public void onLayout(boolean var1, int var2, int var3, int var4, int var5) {
        super.onLayout(var1, var2, var3, var4, var5);
        double var6 = Math.pow((double)((float)this.getWidth() / 2.0F), 2.0D);
        var2 = (int)Math.round(Math.sqrt(Math.pow((double)((float)this.getHeight() / 2.0F), 2.0D) + var6)) / 2;
        this.k = var2;
        this.l = var2;
        this.m = var2;
        this.n = var2;
    }

    public static class UpdateListener implements AnimatorUpdateListener {
        public final SplashImageView a;

        private UpdateListener(SplashImageView var1) {
            this.a = var1;
        }

        public void onAnimationUpdate(ValueAnimator var1) {
            label23: {
                int var2 = (Integer)var1.getAnimatedValue();
                if (var2 >= 100) {
                    if (var2 <= 200) {
                        var2 -= 100;
                        if (this.a.s == var2) {
                            return;
                        }

                        this.a.s = var2;
                        break label23;
                    }

                    var2 = 300 - var2;
                }
                if (this.a.t == var2) {
                    return;
                }

                this.a.t = var2;
            }

            this.a.invalidate();
        }
    }
}
