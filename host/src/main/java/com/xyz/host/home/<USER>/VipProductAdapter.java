package com.xyz.host.home.adpater;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.xyz.host.model.VipInfo;

import java.util.ArrayList;
import java.util.List;

public class VipProductAdapter extends RecyclerView.Adapter<VipProductAdapter.ViewHolder> {
    private List<VipInfo.Product> mPluginList;
    private onItemClickListener mClickListener;
    private Context mContext;

    public VipProductAdapter(Context context) {
        this.mContext = context;
    }

    public List<VipInfo.Product> getList() {
        return mPluginList;
    }

    public void setList(List<VipInfo.Product> models) {
        this.mPluginList = models;
        notifyDataSetChanged();
    }

    public void add(VipInfo.Product info) {
        if (mPluginList == null) {
            mPluginList = new ArrayList<>();
        }
        mPluginList.add(info);
        notifyDataSetChanged();
    }

    public void clear() {
        if (mPluginList != null) {
            mPluginList.clear();
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(
                LayoutInflater.from(mContext).inflate(R.layout.item_vip, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        VipInfo.Product info = mPluginList.get(position);
        holder.title.setText(info.title);
        holder.desc.setText(info.desc);
        holder.price.setText(mContext.getString(R.string.price_format, info.price));
        holder.select.setVisibility(info.checked ? View.VISIBLE : View.GONE);
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mClickListener != null) {
                    mClickListener.onItemClick(info, holder.getAdapterPosition());
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mPluginList == null ? 0 : mPluginList.size();
    }

    public void setOnItemClickListener(onItemClickListener listener) {
        mClickListener = listener;
    }

    public interface onItemClickListener {
        void onItemClick(VipInfo.Product vipInfo, int position);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private TextView title;
        private TextView desc;
        private TextView price;
        private ImageView select;

        ViewHolder(View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.title_all_vip);
            desc = itemView.findViewById(R.id.desc_all_vip);
            price = itemView.findViewById(R.id.all_price_tv);
            select = itemView.findViewById(R.id.mark_vip_all);
        }
    }
}

