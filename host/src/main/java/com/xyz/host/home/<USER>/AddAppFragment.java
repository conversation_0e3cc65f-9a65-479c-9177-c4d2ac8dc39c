package com.xyz.host.home.add;

import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.db.AppInfo;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

public class AddAppFragment extends Fragment {

    private AppListAdapter appListAdapter;
    private List<AppInfo> appInfos;

    private TextView tvTips;
    private EditText mSearchApp;

    private int iconSize = 100;
    private boolean mIsAppHide = false;

    public AddAppFragment() {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    public AddAppFragment(boolean isAppHide) {
        mIsAppHide = isAppHide;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.fragment_add_app, container, false);
        init(rootView);
        return rootView;
    }

    private ActivityResultLauncher<String> mRequestPermission = registerForActivityResult(
            new ActivityResultContracts.RequestPermission(), new ActivityResultCallback<Boolean>() {
                @Override
                public void onActivityResult(Boolean result) {
                    if (result) {
                        loadData();
                    } else {
                        new CommonDialog.Builder(getContext())
                                .setCancelable(false)
                                .setMessage("需要到【设置->权限管理->获取应用列表】确认打开")
                                .setCallback(new CommonDialog.Callback() {
                                    @Override
                                    public void onOk() {
                                        super.onOk();
                                        gotoSetting();
                                    }
                                }).show();
                    }
                }
            }
    );

    private void init(View rootView) {
        iconSize = CommonUtils.dip2px(getContext(), 50f);

        RecyclerView rvAppList = rootView.findViewById(R.id.rv_app_list);
        tvTips = rootView.findViewById(R.id.tv_tips);

        rvAppList.setLayoutManager(new LinearLayoutManager(getContext()));

        appListAdapter = new AppListAdapter(getActivity(), mIsAppHide);
        rvAppList.setAdapter(appListAdapter);

        mSearchApp = rootView.findViewById(R.id.search_app_input);
        mSearchApp.setVisibility(View.VISIBLE);
        mSearchApp.setOnClickListener((v)->{ if(!mSearchApp.isInputMethodTarget()){ mSearchApp.clearFocus();} });
        mSearchApp.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence sequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence sequence, int i, int i1, int i2) {
                appListAdapter.getFilter().filter(sequence.toString());
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        loadData();
    }

    private void request() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(getActivity(), "com.android.permission.GET_INSTALLED_APPS") != PackageManager.PERMISSION_GRANTED) {
                mRequestPermission.launch("com.android.permission.GET_INSTALLED_APPS");
            }
        }
    }

    private void gotoSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", MainApp.getApp().getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadData() {
        new Thread() {
            @Override
            public void run() {
                super.run();
                List<PackageInfo> installedPackages = getContext().getPackageManager().getInstalledPackages(0);
                appInfos = new ArrayList<>();
                for (int i = 0; i < installedPackages.size(); i++) {
                    ApplicationInfo applicationInfo = installedPackages.get(i).applicationInfo;

                    if (getContext() == null) {
                        return;
                    }

                    if (getContext().getPackageName().equals(applicationInfo.packageName)) {
                        continue;
                    }

                    if ((applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) {
                        continue;
                    }

                    Bitmap appIcon = CommonUtils.drawable2Bitmap(applicationInfo.loadIcon(getContext().getPackageManager()));
                    if (appIcon != null) {
                        appIcon = Bitmap.createScaledBitmap(appIcon, iconSize, iconSize, true);
                    }
                    AppInfo appInfo = new AppInfo();
                    appInfo.setInstallType("app");
                    if (mIsAppHide){
                        appInfo.setInstallType("file");
                        String path = applicationInfo.publicSourceDir != null ? applicationInfo.publicSourceDir : applicationInfo.sourceDir;
                        appInfo.setApkPath(path);
                    }
                    appInfo.setPackageName(applicationInfo.packageName);
                    appInfo.setAppIcon(appIcon);

                    if (getContext() == null) {
                        return;
                    }

                    appInfo.setAppName(applicationInfo.loadLabel(getContext().getPackageManager()).toString());
                    appInfo.setApplicationInfo(applicationInfo);
                    appInfos.add(appInfo);
                }

                FragmentActivity activity = getActivity();
                if (CommonUtils.isLiveActivity(activity)) {
                    activity.runOnUiThread(() -> {
                        appListAdapter.setAppList(appInfos);
                        appListAdapter.notifyDataSetChanged();
                        if (!appInfos.isEmpty()) {
                            tvTips.setVisibility(View.GONE);
                        } else {
                            tvTips.setText(getString(R.string.no_data));
                            request();
                        }
                    });
                }

            }
        }.start();
    }

}
