package com.xyz.host.home.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.DimenRes;

import com.xyz.host.R;

public class MineRowView extends RelativeLayout {

   private ImageView ivIcon;
   private TextView tvTitle;
   private ImageView arrow;
   private Switch mSwitch;

   private TextView mDetailTv;
   private View mDividerTop;
   private View mDividerBottom;

   public MineRowView(Context context) {
      this(context, null);
   }

   public MineRowView(Context context, AttributeSet attrs) {
      this(context, attrs, 0);
   }

   public MineRowView(Context context, AttributeSet attrs, int defStyleAttr) {
      super(context, attrs, defStyleAttr);
      initView(context);
      initAttributeSet(context, attrs);
   }

   private void initView(Context context) {
      LayoutInflater.from(context).inflate(R.layout.view_mine_row, this, true);
      ivIcon = findViewById(R.id.iv_icon);
      tvTitle = findViewById(R.id.tv_title);
      arrow = findViewById(R.id.arrow);
      mSwitch = findViewById(R.id.row_switch);
      mDetailTv = findViewById(R.id.tv_detail);
      mDividerTop = findViewById(R.id.divider_top);
      mDividerBottom = findViewById(R.id.divider_bottom);
   }

   private void initAttributeSet(Context context, AttributeSet attrs) {
      TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.MineRowView);
      int iconResId = typedArray.getResourceId(R.styleable.MineRowView_mine_icon, 0);
      if (iconResId != 0) {
         ivIcon.setImageResource(iconResId);
      }

      String title = typedArray.getString(R.styleable.MineRowView_mine_title);
      String detail = typedArray.getString(R.styleable.MineRowView_mine_detail);
      int tvColor = typedArray.getResourceId(R.styleable.MineRowView_mine_tv_color, R.color.white);
      tvTitle.setText(title);
      tvTitle.setTextColor(getResources().getColor(tvColor));
      if (!TextUtils.isEmpty(detail)) {
         mDetailTv.setText(detail);
         mDetailTv.setVisibility(View.VISIBLE);
      }
      boolean iconVisibility = typedArray.getBoolean(R.styleable.MineRowView_mine_icon_visibility
              , true);
      ivIcon.setVisibility(iconVisibility ? View.VISIBLE : View.GONE);
      boolean switchVisibility =
              typedArray.getBoolean(R.styleable.MineRowView_mine_switch_visibility, false);
      mSwitch.setVisibility(switchVisibility ? View.VISIBLE : View.GONE);
      boolean arrowVisibility = typedArray.getBoolean(R.styleable.MineRowView_mine_arrow_visibility, true);
      arrow.setVisibility(arrowVisibility ? View.VISIBLE : View.GONE);
      boolean dividerTopVisibility =
              typedArray.getBoolean(R.styleable.MineRowView_divider_top_visibility, false);
      mDividerTop.setVisibility(dividerTopVisibility ? View.VISIBLE : View.GONE);
      boolean dividerBottomVisibility =
              typedArray.getBoolean(R.styleable.MineRowView_divider_bottom_visibility, false);
      mDividerBottom.setVisibility(dividerBottomVisibility ? View.VISIBLE : View.GONE);
      typedArray.recycle();
   }

   public void setTvTitle(String title) {
      tvTitle.setText(title);
   }

   public void setTitleTextSize(@DimenRes int id){
      tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX,
              getResources().getDimension(id));
   }

   public void setDetailMsg(String msg) {
      mDetailTv.setText(msg);
   }

   public void setArrowIcon(int resId){
      arrow.setImageResource(resId);
   }

   public void setSwitchChecked(boolean isChecked){
      mSwitch.setChecked(isChecked);
   }

   public void setOnCheckedChangeListener(CompoundButton.OnCheckedChangeListener listener){
      mSwitch.setOnCheckedChangeListener(listener);
   }

}