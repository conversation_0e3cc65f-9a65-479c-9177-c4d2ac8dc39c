package com.xyz.host.home;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.xyz.host.BuildConfig;
import com.xyz.host.R;
import com.xyz.host.maskprivacy.PolicyActivity;

import java.util.Locale;

public class AboutActivity extends AppCompatActivity implements View.OnClickListener {
    private final static String TAG = "AboutActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置沉浸式状态栏
        setupImmersiveStatusBar();

        setContentView(R.layout.activity_about);

        init();
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.purple_700));

        // 设置状态栏文字为白色
        window.getDecorView().setSystemUiVisibility(0);
    }

    private void init() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(v -> finish());

        findViewById(R.id.about_privacy_tv).setOnClickListener(this);
        findViewById(R.id.about_use_policy_tv).setOnClickListener(this);
        findViewById(R.id.about_number).setOnClickListener(this);
        findViewById(R.id.about_logout_tv).setOnClickListener(this);

        TextView txtVersion = findViewById(R.id.about_app_verion);

        txtVersion.setText(String.format(Locale.getDefault(), "%s V%s",
                getString(R.string.app_name), BuildConfig.VERSION_NAME));
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.about_privacy_tv) {
            Intent privacyIntent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://wj.aoqi360.com/yingyu_privacy.html"));
            startActivity(privacyIntent);
        } else if (v.getId() == R.id.about_use_policy_tv) {
            Intent intent1 = new Intent(this, PolicyActivity.class);
            intent1.putExtra("action", getString(R.string.service_use_policy));
            startActivity(intent1);
        } else if (v.getId() == R.id.about_number) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://beian.miit.gov.cn/"));
            startActivity(intent);
        } else if (v.getId() == R.id.about_logout_tv) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://wj.aoqi360.com/yingyu_logout.html"));
            startActivity(intent);
        }
    }
}