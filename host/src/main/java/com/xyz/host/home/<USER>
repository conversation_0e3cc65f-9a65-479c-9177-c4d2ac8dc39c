package com.xyz.host.home;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;

import androidx.appcompat.app.AppCompatActivity;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.CSJAdError;
import com.bytedance.sdk.openadsdk.CSJSplashAd;
import com.bytedance.sdk.openadsdk.TTAdConstant;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAppDownloadListener;
import com.fun.vbox.client.core.VCore;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.ads.TTAdManagerHolder;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.home.view.CalculatorView;
import com.xyz.host.maskprivacy.MaskAgreeDialog;
import com.xyz.host.maskprivacy.PrivacyUtils;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.UIUtils;
import com.yingyu.zh.multiapp.AdIdByReviewMode;
import com.yingyu.zh.multiapp.MultiAppActivity;

import java.nio.charset.StandardCharsets;

public class SplashActivity extends AppCompatActivity {
    private static final String TAG = "SplashActivity";
    private TTAdNative mTTAdNative;
    private FrameLayout mSplashContainer;
    private static final int AD_TIME_OUT = 3000;
    private String mCodeId = "";
    private boolean mIsDialogShowing = false;
    private boolean mIsInit;

    public SplashActivity() {
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        if (!PrivacyUtils.isAllowPrivacy()) {
            onPrivacyIntercept();
            return;
        }

        initWhenPrivacyAgreed();
    }

    private void initWhenPrivacyAgreed() {
        if (mIsInit) {
            return;
        }

        mSplashContainer = findViewById(R.id.splash_container);
        mTTAdNative = TTAdManagerHolder.get().createAdNative(this);

        initFlag();

        if (UserAgent.getInstance().isVipUser()) {
            goToMainActivity();
        } else {
            mCodeId = AdIdByReviewMode.getAdId("splash_ads_id");
            Log.d(TAG, "loadSplashAd adid : " + mCodeId);

            if (TextUtils.isEmpty(mCodeId)) {
                new Handler().postDelayed(this::goToMainActivity, 800);
            } else {
                loadSplashAd();
            }
        }
    }

    private void onPrivacyIntercept() {
        if (PrivacyUtils.isAllowPrivacy()) {
            return;
        }
        if (mIsDialogShowing) {
            return;
        }
        MaskAgreeDialog agreeDialog = new MaskAgreeDialog(this);
        agreeDialog.setOnDismissListener(dialog -> {
            mIsDialogShowing = false;
            if (PrivacyUtils.isAllowPrivacy()) {
                MainApp.getApp().initWhenPrivacyAgreed();
                initWhenPrivacyAgreed();
            } else {
                finish();
            }
        });
        agreeDialog.show();
        mIsDialogShowing = true;
    }

    private void initFlag() {
        try {
            Intent intent = getIntent();
            if (intent != null) {
                Uri uri = intent.getData();
                if (uri != null) {
                    String data = uri.getQueryParameter("data");
                    if (!TextUtils.isEmpty(data) && "virtual".equals(data)) {
                        VCore.getPref().edit().putBoolean("loc_flag", true).apply();
                    }
                }
            }
        } catch (Throwable e) {
            // Ignored.
        }
        mIsInit = true;
    }

    private void loadSplashAd() {
        DisplayMetrics dm = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getRealMetrics(dm);
        float splashWidthDp = UIUtils.getScreenWidthDp(this);
        int splashWidthPx = UIUtils.getScreenWidthInPx(this);
        int screenHeightPx = UIUtils.getScreenHeight(this);
        float screenHeightDp = UIUtils.px2dip(this, screenHeightPx);
        float splashHeightDp;
        int splashHeightPx;

        splashHeightDp = screenHeightDp;
        splashHeightPx = screenHeightPx;

        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(mCodeId)
                .setExpressViewAcceptedSize(splashWidthDp, splashHeightDp) // 单位是dp
                .setImageAcceptedSize(splashWidthPx, splashHeightPx) // 单位是px
                .build();

        mTTAdNative.loadSplashAd(adSlot, new TTAdNative.CSJSplashAdListener() {
            @Override
            public void onSplashLoadSuccess(CSJSplashAd csjSplashAd) {

            }

            @Override
            public void onSplashLoadFail(CSJAdError csjAdError) {
                Log.d(TAG, "onSplashLoadFail:" + csjAdError.getCode() + "," + csjAdError.getMsg());
                showToast(csjAdError.getMsg());
                goToMainActivity();
            }

            @Override
            public void onSplashRenderSuccess(CSJSplashAd ad) {
                Log.d(TAG, "开屏广告请求成功");
                if (ad == null) {
                    return;
                }

                //获取SplashView
                View view = ad.getSplashView();
                if (view != null && mSplashContainer != null && !SplashActivity.this.isFinishing()) {
                    mSplashContainer.setVisibility(View.VISIBLE);
                    mSplashContainer.removeAllViews();
                    mSplashContainer.addView(view);

                } else {
                    goToMainActivity();
                }
                ad.setSplashAdListener(new CSJSplashAd.SplashAdListener() {
                    @Override
                    public void onSplashAdShow(CSJSplashAd csjSplashAd) {

                    }

                    @Override
                    public void onSplashAdClick(CSJSplashAd csjSplashAd) {
                        Log.d(TAG, "开屏广告点击");
                    }

                    @Override
                    public void onSplashAdClose(CSJSplashAd csjSplashAd, int i) {
                        Log.d(TAG, "开屏广告关闭");
                        goToMainActivity();
                    }
                });

                if (ad.getInteractionType() == TTAdConstant.INTERACTION_TYPE_DOWNLOAD) {
                    ad.setDownloadListener(new TTAppDownloadListener() {
                        boolean hasShow = false;

                        @Override
                        public void onIdle() {
                        }

                        @Override
                        public void onDownloadActive(long totalBytes, long currBytes, String fileName, String appName) {
                            if (!hasShow) {
                                showToast("下载中...");
                                hasShow = true;
                            }
                        }

                        @Override
                        public void onDownloadPaused(long totalBytes, long currBytes, String fileName, String appName) {
                            showToast("下载暂停...");

                        }

                        @Override
                        public void onDownloadFailed(long totalBytes, long currBytes, String fileName, String appName) {
                            showToast("下载失败...");

                        }

                        @Override
                        public void onDownloadFinished(long totalBytes, String fileName, String appName) {
                            showToast("下载完成...");

                        }

                        @Override
                        public void onInstalled(String fileName, String appName) {
                            showToast("安装完成...");

                        }
                    });
                }
            }

            @Override
            public void onSplashRenderFail(CSJSplashAd csjSplashAd, CSJAdError csjAdError) {
                Log.d(TAG, "onSplashRenderFail:" + csjAdError.getCode() + "," + csjAdError.getMsg());
                showToast(csjAdError.getMsg());
                goToMainActivity();
            }
        }, AD_TIME_OUT);
    }

    /**
     * 跳转到主页面
     */
    private void goToMainActivity() {
        Log.d(TAG, "goToMainActivity");
        if (mSplashContainer != null) {
            mSplashContainer.removeAllViews();
        }

        final String pwd = AppSharePref.get().getString(AppSharePref.KEY_HIDE_PWD);
        if (!TextUtils.isEmpty(pwd)) {
            byte[] bytes = Base64.decode(pwd, Base64.DEFAULT);
            String decodePwd = new String(bytes, StandardCharsets.UTF_8);
            // Log.e("MyTest", "haha:" + decodePwd);
            CalculatorView viewCalc = findViewById(R.id.viewCalc);
            viewCalc.setVisibility(View.VISIBLE);
            viewCalc.setCallback(new CalculatorView.ICallback() {
                @Override
                public void verify(String exp) {
                    if (decodePwd.equals(exp)) {
                        goMain();
                    }
                }

                @Override
                public void reset() {
                    new CommonDialog.Builder(SplashActivity.this)
                            .setMessage("确认清除密码吗，如果您不是本人，清除操作后面会被本人发现，确认清除吗")
                            .setNegativeButton(R.string.cancel)
                            .setCallback(new CommonDialog.Callback() {
                                @Override
                                public void onOk() {
                                    super.onOk();
                                    AppSharePref.get().putString(AppSharePref.KEY_HIDE_PWD, "");
                                    goMain();
                                }
                            })
                            .show();
                }
            });
            return;
        }
        goMain();
    }

    private void goMain() {
        MultiAppActivity.start(this);
        finish();
    }

    private void showToast(String msg) {
        if (msg != null) {
            try  {
                Log.d(TAG, msg);
            } catch(Exception ignored){
            }
        }
    }
}
