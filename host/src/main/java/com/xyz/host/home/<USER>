package com.xyz.host.home;

import static androidx.recyclerview.widget.ItemTouchHelper.ACTION_STATE_DRAG;
import static androidx.recyclerview.widget.ItemTouchHelper.DOWN;
import static androidx.recyclerview.widget.ItemTouchHelper.END;
import static androidx.recyclerview.widget.ItemTouchHelper.LEFT;
import static androidx.recyclerview.widget.ItemTouchHelper.RIGHT;
import static androidx.recyclerview.widget.ItemTouchHelper.START;
import static androidx.recyclerview.widget.ItemTouchHelper.UP;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.gw.swipeback.SwipeBackLayout;
import com.xyz.host.utils.DialogUtils;
import com.xyz.host.R;
import com.xyz.host.db.DbManager;
import com.xyz.host.db.LocInfo;
import com.xyz.host.home.adpater.LocInfoAdapter;
import com.xyz.host.home.adpater.decorations.ItemOffsetDecoration;
import com.xyz.host.home.base.BaseActivity;
import com.xyz.host.utils.GsonUtils;
import com.xyz.host.utils.LocInfoUtils;
import com.xyz.host.utils.UIUtils;
import com.xyz.host.utils.share.Share2;
import com.xyz.host.utils.share.ShareContentType;

public class CheckInActivity extends BaseActivity {
    public static final int REQUEST_CODE = 1002;

    private LocInfoAdapter mAdapter;

    private AMapLocationClient mLocationClient = null;
    private final LocInfo mLocInfo = new LocInfo();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.slide_bottom_in, R.anim.slide_bottom_out);
        setContentView(R.layout.activity_checkin);

        int heightPixels = getResources()
                .getDisplayMetrics().heightPixels;

        Window win = this.getWindow();
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = (int) (heightPixels * 0.9);
        lp.dimAmount = 0.8f;
        lp.gravity = Gravity.BOTTOM;
        win.setAttributes(lp);

        RecyclerView recyclerView = findViewById(R.id.app_recycler_view);
        recyclerView.setLayoutManager(new StaggeredGridLayoutManager(1,
                OrientationHelper.VERTICAL));
        recyclerView.addItemDecoration(new ItemOffsetDecoration(UIUtils.dp2px(getContext(), 2)));
        mAdapter = new LocInfoAdapter(this);
        recyclerView.setAdapter(mAdapter);
        ItemTouchHelper touchHelper = new ItemTouchHelper(new TouchCallback());
        touchHelper.attachToRecyclerView(recyclerView);
        mAdapter.setOnItemClickListener((locInfo, postion) -> {
            AlertDialog.Builder builder = new AlertDialog.Builder(CheckInActivity.this, R.style.Theme_MaterialComponents_Light_Dialog_Alert);
            StringBuilder sb = new StringBuilder();
            sb.append("位置：");
            sb.append(locInfo.getName());
            builder.setTitle(sb.toString());
            builder.setItems(new String[]{"模拟该地点", "修改描述", "分享信息", "删除"},
                    new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i) {
                            if (i == 0) {
                                Intent intent = new Intent();
                                intent.putExtra("locId", locInfo.getId());
                                setResult(Activity.RESULT_OK, intent);
                                finish();
                            } else if (i == 1) {
                                showInfoDialog(CheckInActivity.this, locInfo, true);
                            } else if (i == 2) {
                                String text = GsonUtils.getGsonString(locInfo);
                                new Share2.Builder(CheckInActivity.this)
                                        .setContentType(ShareContentType.TEXT)
                                        .setTextContent(text)
                                        .setTitle("Share")
                                        .build()
                                        .shareBySystem();
                            } else if (i == 3) {
                                deleteLocInfo(locInfo);
                            }
                        }
                    });
            builder.setPositiveButton("取消", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    dialogInterface.dismiss();
                }
            });
            builder.show();
        });
        mAdapter.setOnItemRemoveListener((locInfo, postion) -> {
            deleteLocInfo(locInfo);
        });

        Button button = findViewById(R.id.btn_get);
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getCurrentLocation();
            }
        });

        findViewById(R.id.btn_input).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                inputLocInfo(CheckInActivity.this);
            }
        });

        SwipeBackLayout swipeBackLayout = new SwipeBackLayout(this);
        swipeBackLayout.attachToActivity(this);
        swipeBackLayout.setDirectionMode(SwipeBackLayout.FROM_TOP);
        swipeBackLayout.setMaskAlpha(200);

        loadData();
    }

    private void inputLocInfo(Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.Theme_MaterialComponents_Light_Dialog_Alert);
        builder.setTitle("地址数据");

        View view =
                LayoutInflater.from(context)
                        .inflate(R.layout.dialog_input_loc_info, null);
        EditText editText = view.findViewById(R.id.et_loc_json);
        EditText editName = view.findViewById(R.id.et_tag);

        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                dialogInterface.dismiss();
            }
        });
        builder.setPositiveButton("保存", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                String name = editName.getText().toString();
                String text = editText.getText().toString();
                if (TextUtils.isEmpty(name) || TextUtils.isEmpty(text)) {
                    Toast.makeText(context, "请输入名称/信息再保存", Toast.LENGTH_SHORT).show();
                } else {
                    try {
                        LocInfo locInfo = GsonUtils.gson2Bean(text, LocInfo.class);
                        locInfo.setId(0);
                        locInfo.setName(name);
                        locInfo.setLocOrder(DbManager.getInstance().getLocInfoDao().getNextOrderId());
                        DbManager.getInstance().getLocInfoDao().insertOne(locInfo);
                        mAdapter.add(locInfo);
                        mAdapter.notifyDataSetChanged();
                        dialogInterface.dismiss();
                    } catch (Throwable e) {
                        Toast.makeText(context, "信息有误！", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });
        builder.setCancelable(false);
        builder.setView(view);
        builder.show();
    }

    private void deleteLocInfo(LocInfo locInfo) {
        new AlertDialog.Builder(CheckInActivity.this, R.style.Theme_MaterialComponents_Light_Dialog_Alert).setCancelable(false).setMessage("确定要删除吗？")
                .setNegativeButton(
                        "取消", null).setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                mAdapter.remove(locInfo);
                DbManager.getInstance().getLocInfoDao().deleteById(locInfo);
                mAdapter.notifyDataSetChanged();
            }
        }).show();
    }

    private void getCurrentLocation() {
        final Dialog dialog = DialogUtils.createLoadingDialog(this, "请稍等...", true,
                0);
        dialog.show();
        if (mLocationClient != null) {
            mLocationClient.onDestroy();
        }
        try {
            mLocationClient = new AMapLocationClient(this);
        } catch (Exception e) {
            dialog.dismiss();
            return;
        }
        mLocationClient.setLocationListener(new AMapLocationListener() {
            @Override
            public void onLocationChanged(AMapLocation amapLocation) {
                mLocationClient.unRegisterLocationListener(this);
                mLocationClient.stopLocation();

                if (amapLocation != null && amapLocation.getErrorCode() == 0) {
                    mLocInfo.setLongitude(amapLocation.getLongitude());
                    mLocInfo.setLatitude(amapLocation.getLatitude());
                    mLocInfo.setAddress(amapLocation.getAddress());
                    LocInfoUtils.initBaseStationInfo(mLocInfo);
                    showInfoDialog(CheckInActivity.this, mLocInfo, false);
                } else {
                    Toast.makeText(CheckInActivity.this, "获取失败，重新获取吧", Toast.LENGTH_SHORT).show();
                }
                dialog.dismiss();
            }
        });
        AMapLocationClientOption locationOption = new AMapLocationClientOption();
        locationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        mLocationClient.setLocationOption(locationOption);
        mLocationClient.startLocation();
    }

    public void showInfoDialog(Context context, LocInfo info, boolean edit) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.Theme_MaterialComponents_Light_Dialog_Alert);
        builder.setTitle("地址数据");

        View view =
                LayoutInflater.from(context)
                        .inflate(R.layout.dialog_checkin_loc_info, null);
        TextView textView1 = view.findViewById(R.id.tv_info1);
        TextView textView2 = view.findViewById(R.id.tv_info2);
        TextView textView3 = view.findViewById(R.id.tv_info3);
        TextView textView4 = view.findViewById(R.id.tv_info4);
        EditText editText = view.findViewById(R.id.et_tag);
        editText.setText(info.getName());

        textView1.setText("经度：" + info.getLongitude());
        textView2.setText("纬度：" + info.getLatitude());
        if (info.getBaseStationId() == 0 && info.getLac() == 0) {
            textView3.setText("基站数据：无");
        } else {
            textView3.setText("基站数据：有");
        }
        textView4.setText("地址：" + info.getAddress());

        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                dialogInterface.dismiss();
            }
        });
        builder.setPositiveButton("保存", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                String name = editText.getText().toString();
                if (TextUtils.isEmpty(name)) {
                    Toast.makeText(context, "请输入名称再保存", Toast.LENGTH_SHORT).show();
                } else {
                    if (edit) {
                        info.setName(name);
                        DbManager.getInstance().getLocInfoDao().updateById(info);
                    } else {
                        info.setName(name);
                        LocInfo saveLocInfo = new LocInfo(info);
                        saveLocInfo.setLocOrder(DbManager.getInstance().getLocInfoDao().getNextOrderId());
                        DbManager.getInstance().getLocInfoDao().insertOne(saveLocInfo);
                        mAdapter.add(saveLocInfo);
                    }
                    mAdapter.notifyDataSetChanged();
                    dialogInterface.dismiss();

                }
            }
        });
        builder.setCancelable(false);
        builder.setView(view);
        builder.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mLocationClient != null) {
            mLocationClient.onDestroy();
        }
    }

    private void loadData() {
        final Dialog dialog = DialogUtils.createLoadingDialog(this, "请稍等...", true,
                0);
        dialog.show();
        DbManager.getInstance().getLocInfoDao().getAllObserve().observe(this, list -> {
            dialog.dismiss();
            if (list.size() > 0) {
                mAdapter.setList(list);
            }
        });
    }

    private class TouchCallback extends ItemTouchHelper.SimpleCallback {
        boolean upAtDeleteAppArea;
        RecyclerView.ViewHolder dragHolder;
        private int lastItemDrag;

        TouchCallback() {
            super(UP | DOWN | LEFT | RIGHT | START | END, 0);
        }

        @Override
        public int interpolateOutOfBoundsScroll(RecyclerView recyclerView, int viewSize, int viewSizeOutOfBounds,
                                                int totalSize, long msSinceStartScroll) {
            return 0;
        }

        @Override
        public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            return super.getMovementFlags(recyclerView, viewHolder);
        }

        @Override
        public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder,
                              RecyclerView.ViewHolder target) {
            int pos = viewHolder.getAdapterPosition();
            int targetPos = target.getAdapterPosition();
            mAdapter.moveItem(pos, targetPos);
            return true;
        }

        @Override
        public boolean isLongPressDragEnabled() {
            return true;
        }

        @Override
        public boolean isItemViewSwipeEnabled() {
            return false;
        }

        @Override
        public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {

        }

        @Override
        public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
            if (viewHolder instanceof LocInfoAdapter.ViewHolder) {
                if (actionState == ACTION_STATE_DRAG) {
                    lastItemDrag = viewHolder.getAdapterPosition();
                    if (dragHolder != viewHolder) {
                        dragHolder = viewHolder;
                        viewHolder.itemView.setScaleX(1.2f);
                        viewHolder.itemView.setScaleY(1.2f);
                    }
                }
            }
            super.onSelectedChanged(viewHolder, actionState);
        }

        @Override
        public boolean canDropOver(RecyclerView recyclerView, RecyclerView.ViewHolder current,
                                   RecyclerView.ViewHolder target) {
            return !upAtDeleteAppArea;
        }

        @Override
        public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
            if (viewHolder instanceof LocInfoAdapter.ViewHolder) {
                viewHolder.itemView.setScaleX(1f);
                viewHolder.itemView.setScaleY(1f);
                int pos = viewHolder.getAdapterPosition();
                if (lastItemDrag != pos) {
                    DbManager.getInstance().updateLocOrder(mAdapter.getList());
                }
            }
            super.clearView(recyclerView, viewHolder);
        }
    }

}
