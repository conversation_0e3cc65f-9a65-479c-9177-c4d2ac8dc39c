package com.xyz.host.home;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.os.Bundle;
import android.os.PersistableBundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.ArrayAdapter;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.MenuItemCompat;

import com.amap.api.maps2d.AMap;
import com.amap.api.maps2d.CameraUpdateFactory;
import com.amap.api.maps2d.MapView;
import com.amap.api.maps2d.model.BitmapDescriptor;
import com.amap.api.maps2d.model.BitmapDescriptorFactory;
import com.amap.api.maps2d.model.Circle;
import com.amap.api.maps2d.model.CircleOptions;
import com.amap.api.maps2d.model.LatLng;
import com.amap.api.maps2d.model.Marker;
import com.amap.api.maps2d.model.MarkerOptions;
import com.amap.api.maps2d.model.MyLocationStyle;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.geocoder.GeocodeResult;
import com.amap.api.services.geocoder.GeocodeSearch;
import com.amap.api.services.geocoder.RegeocodeQuery;
import com.amap.api.services.geocoder.RegeocodeResult;
import com.amap.api.services.geocoder.StreetNumber;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiSearch;
import com.fun.vbox.client.core.VCore;
import com.fun.vbox.client.ipc.VLocationManager;
import com.fun.vbox.client.ipc.VirtualLocationManager;
import com.fun.vbox.entity.VAppInfo;
import com.fun.vbox.remote.vloc.VCell;
import com.fun.vbox.remote.vloc.VLocation;
import com.fun.vbox.utils.lo.GPSUtil;
import com.xyz.host.R;
import com.xyz.host.db.DbManager;
import com.xyz.host.db.LocInfo;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.home.base.BaseActivity;
import com.xyz.host.home.view.fittext.FitTextView;
import com.xyz.host.model.LocationData;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.CommonUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class SetLocationActivity extends BaseActivity
        implements PoiSearch.OnPoiSearchListener, AMap.OnMyLocationChangeListener,
        View.OnClickListener {

    private AMap mAMap;
    private MapView mapView;
    private SearchView mSearchView;
    private MenuItem mSearchMenuItem;
    private ListView mSearchResult;
    private View mSearchLayout;
    private ArrayAdapter<MapSearchResult> mSearchAdapter;
    private VAppInfo mAppInfo;
    private String mCurPkg;
    private int mCurUserId;
    private LocationData mSelectData;
    private Marker mLocMarker;
    private Circle mCircle;
    private GeocodeSearch mGeocoderSearch;
    private FitTextView mLocationTv;
    private Toolbar mToolbar;
    private boolean mSetLocChanged;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == CheckInActivity.REQUEST_CODE && resultCode == RESULT_OK &&
                null != data) {
            int id = data.getIntExtra("locId", 0);
            if (id != 0) {
                LocInfo locInfo = DbManager.getInstance().getLocInfoDao().getLocInfo(id);
                VCell vCell = locInfo.getVCell();
                VirtualLocationManager.get().setCell(mCurUserId, mCurPkg, vCell);
                if (mSelectData != null && mSelectData.location != null) {
                    mSelectData.location.address = locInfo.getAddress();
                }
                go(locInfo.getLatitude(), locInfo.getLongitude());
            }
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_set_location);
        mToolbar = findViewById(R.id.task_top_toolbar);
        mToolbar.setTitle(R.string.location_title);
        setSupportActionBar(mToolbar);
        enableBackHome();

        mapView = findViewById(R.id.mapview);
        mapView.onCreate(savedInstanceState); // 此方法必须重写
        initView();
        initMap();
        initData();
    }

    private void initView() {
        mSearchResult = bind(R.id.search_results);
        mSearchLayout = bind(R.id.search_layout);
        mSearchAdapter =
                new ArrayAdapter<>(this, R.layout.item_white_text, new ArrayList<>());
        mSearchResult.setAdapter(mSearchAdapter);
        MapSearchResult.NULL.setAddress(getString(R.string.tip_no_find_points));
        mSearchResult.setOnItemClickListener((adapterView, view, i, l) -> {
            MapSearchResult searchResult = mSearchAdapter.getItem(i);
            if (searchResult != MapSearchResult.NULL) {
                clearMarker(); //这里要清空一下
                mSearchMenuItem.collapseActionView();
                gotoLocation(searchResult.lat, searchResult.lng);
            }
        });

        findViewById(R.id.btn_checkin).setOnClickListener(this);
        findViewById(R.id.btn_save).setOnClickListener(this);
        findViewById(R.id.btn_set).setOnClickListener(this);
        mLocationTv = findViewById(R.id.tv_address);
    }

    private void initData() {
        //data
        mAppInfo = getIntent().getParcelableExtra("appInfo");
        mCurPkg = mAppInfo.packageName;
        mCurUserId = mAppInfo.userId;

        mSelectData = new LocationData(mCurPkg, mCurUserId);
        updateLocation();
    }

    private void updateLocation() {
        readLocation(mSelectData);

        if (mSelectData.location != null) {
            gotoLocation(mSelectData.location.getLatitude(), mSelectData.location.getLongitude());
        } else {
            mSelectData.location = new VLocation();
            gotoLocation(39.917366, 116.396957, 15.7f);
            MyLocationStyle myLocationStyle = new MyLocationStyle();
            mAMap.setMyLocationStyle(myLocationStyle);
            myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATE);
            mAMap.setMyLocationEnabled(true);
            //mAMap.setMyLocationType(AMap.LOCATION_TYPE_LOCATE);
            mAMap.setOnMyLocationChangeListener(this);
        }
    }

    private void readLocation(LocationData locationData) {
        try {
            locationData.mode = VirtualLocationManager.get()
                    .getMode(locationData.userId, locationData.packageName);
            locationData.location = VLocationManager.get()
                    .getLocation(locationData.packageName, locationData.userId);
            if (locationData.location != null) {
                double[] doubles = GPSUtil.gps84_To_Gcj02(locationData.location.latitude,
                        locationData.location.longitude);
                locationData.location.latitude = doubles[0];
                locationData.location.longitude = doubles[1];
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initMap() {
        mAMap = mapView.getMap();
        mAMap.getUiSettings().setZoomControlsEnabled(false);
        mAMap.setOnMapClickListener(new AMap.OnMapClickListener() {
            @Override
            public void onMapClick(LatLng latLng) {
                gotoLocation(latLng.latitude, latLng.longitude, mAMap.getCameraPosition().zoom);
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.map_menu, menu);
        MenuItem menuItem = menu.findItem(R.id.action_search);
        mSearchMenuItem = menuItem;
        mSearchView = (SearchView) MenuItemCompat.getActionView(menuItem);

        mToolbar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mSearchMenuItem.expandActionView();
            }
        });

        mSearchView.setImeOptions(EditorInfo.IME_ACTION_SEARCH);
        mSearchView.setSubmitButtonEnabled(true);
        mSearchView.setQueryHint(getString(R.string.tip_input_keywords));
        try {
            ImageView goButton = mSearchView.findViewById(androidx.appcompat.R.id.search_go_btn);
            goButton.setImageResource(R.drawable.ic_search_locate);
        } catch (Exception e) {
            // Ignored.
        }
        MenuItemCompat
                .setOnActionExpandListener(menuItem, new MenuItemCompat.OnActionExpandListener() {
                    @Override
                    public boolean onMenuItemActionExpand(MenuItem item) {
                        mSearchLayout.setVisibility(View.VISIBLE);
                        return true;
                    }

                    @Override
                    public boolean onMenuItemActionCollapse(MenuItem item) {
                        mSearchLayout.setVisibility(View.GONE);
                        return true;
                    }
                });
        mSearchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String newText) {
                if (!TextUtils.isEmpty(newText)) {
                    doSearchQuery(newText);
                } else {
                    mSearchAdapter.clear();
                    mSearchAdapter.notifyDataSetChanged();
                }
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                return true;
            }
        });
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_search:
                break;
            case R.id.action_lat:
                showInputWindow();
                break;
            default:
                return super.onOptionsItemSelected(item);
        }
        return true;
    }

    @Override
    public void onSaveInstanceState(Bundle outState, PersistableBundle outPersistentState) {
        super.onSaveInstanceState(outState, outPersistentState);
        mapView.onSaveInstanceState(outState);
    }

    /**
     * 开始进行poi搜索
     */
    protected void doSearchQuery(String keyword) {
        // 第一个参数表示搜索字符串，第二个参数表示poi搜索类型，第三个参数表示poi搜索区域（空字符串代表全国）
        PoiSearch.Query query = new PoiSearch.Query(keyword, "", "");
        query.setPageSize(15);// 设置每页最多返回多少条poiitem
        query.setPageNum(1);// 设置查第一页

        PoiSearch poiSearch = null;
        try {
            poiSearch = new PoiSearch(this, query);
            poiSearch.setOnPoiSearchListener(this);
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            Log.e("ChooseLocationActivity", "", e);
        }
    }

    private void gotoLocation(double lat, double lng) {
        gotoLocation(lat, lng, 17f);
    }

    @SuppressLint("DefaultLocale")
    private void gotoLocation(double lat, double lng, float zoom) {
        if (lat == 0 && lng == 0) {
            return;
        }
        LatLng location = new LatLng(lat, lng);
        mAMap.animateCamera(CameraUpdateFactory.newLatLngZoom(location, zoom));
        if (mCircle == null) {
            mAMap.clear();
            addCircle(location, getRadius(zoom));//添加定位精度圆
            addMarker(location);//添加定位图标
        } else {
            mCircle.setCenter(location);
            mCircle.setRadius(getRadius(zoom));
            mLocMarker.setPosition(location);
        }
        checkSetLocChange(lat, lng);
        mSelectData.location.latitude = lat;
        mSelectData.location.longitude = lng;
        updateAddress();
    }

    private void checkSetLocChange(double lat, double lng) {
        mSetLocChanged = mSelectData.location.latitude != lat
                || mSelectData.location.longitude != lng;
    }

    private double getRadius(float zoom) {
        double radius = 100;
        if (zoom >= 17) {
            radius = 40;
        } else if (zoom > 15) {
            radius = 200;
        } else if (zoom > 13) {
            radius = 400;
        } else if (zoom > 10) {
            radius = 1000;
        } else {
            radius = 2000;
        }
        return radius;
    }

    private void updateAddress() {
        if (mGeocoderSearch == null) {
            try {
                mGeocoderSearch = new GeocodeSearch(this);
                mGeocoderSearch.setOnGeocodeSearchListener(new GeocodeSearch.OnGeocodeSearchListener() {

                    @Override
                    public void onGeocodeSearched(GeocodeResult result, int rCode) {
                    }

                    @Override
                    public void onRegeocodeSearched(RegeocodeResult result, int rCode) {
                        String formatAddress = result.getRegeocodeAddress().getFormatAddress();
                        if (TextUtils.isEmpty(formatAddress)) {
                            updateAddress2();
                            return;
                        }

                        String province = result.getRegeocodeAddress().getProvince();
                        String city = result.getRegeocodeAddress().getCity();
                        String district = result.getRegeocodeAddress().getDistrict();
                        StreetNumber streetNumber = result.getRegeocodeAddress().getStreetNumber();
                        String street = "";
                        String number = "";
                        if (streetNumber != null) {
                            street = streetNumber.getStreet();
                            number = streetNumber.getNumber();
                        }

                        if (mSelectData != null && mSelectData.location != null) {
                            mSelectData.location.address = formatAddress;
                            mSelectData.location.address_province = province;
                            mSelectData.location.address_city = city;
                            mSelectData.location.address_district = district;
                            mSelectData.location.address_street = street;
                            mSelectData.location.address_number = number;
                        }
                        mLocationTv.setText(formatAddress);
                    }
                });
            } catch (Throwable e) {
                Log.e("ChooseLocationActivity", "", e);
            }
        }
        LatLonPoint lp =
                new LatLonPoint(mSelectData.location.latitude, mSelectData.location.longitude);
        RegeocodeQuery query = new RegeocodeQuery(lp, 200, GeocodeSearch.AMAP);
        mGeocoderSearch.getFromLocationAsyn(query);
    }

    Geocoder mGeocoderSearch2;
    private void updateAddress2() {
        if (mGeocoderSearch2 == null) {
            mGeocoderSearch2 = new Geocoder(this, Locale.getDefault());
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                String strAddress = "";
                List<Address> list = null;
                try {
                    list = mGeocoderSearch2.getFromLocation(mSelectData.location.latitude, mSelectData.location.longitude, 1);
                } catch (IOException e) {
                    // Ignored.
                }
                if (list != null && !list.isEmpty()) {
                    Address address = list.get(0);
                    strAddress = address.getAddressLine(0);
                }
                if (CommonUtils.isLiveActivity(SetLocationActivity.this)) {
                    String finalStrAddress = strAddress;
                    runOnUiThread(() -> {
                        if (mSelectData != null && mSelectData.location != null) {
                            mSelectData.location.address = finalStrAddress;
                        }
                        mLocationTv.setText(finalStrAddress);
                    });
                }
            }
        }).start();
    }

    private void addCircle(LatLng latlng, double radius) {
        CircleOptions options = new CircleOptions();
        options.fillColor(R.color.blue_60);
        options.strokeWidth(1f);
        options.strokeColor(R.color.blue_60);
        options.center(latlng);
        options.radius(radius);
        mCircle = mAMap.addCircle(options);
    }

    private void addMarker(LatLng latlng) {
        if (mLocMarker != null) {
            return;
        }
        Bitmap bMap = BitmapFactory.decodeResource(this.getResources(),
                R.drawable.navi_map_gps_locked);
        BitmapDescriptor des = BitmapDescriptorFactory.fromBitmap(bMap);
        MarkerOptions options = new MarkerOptions();
        options.icon(des);
        options.anchor(0.5f, 0.5f);
        options.position(latlng);
        mLocMarker = mAMap.addMarker(options);
    }

    private void clearMarker() {
        mCircle = null;
        mLocMarker = null;
    }

    @Override
    public void onPoiSearched(PoiResult poiResult, int rCode) {
        if (rCode == AMapException.CODE_AMAP_SUCCESS) {
            if (poiResult != null && poiResult.getQuery() != null) {// 搜索poi的结果
                // 取得搜索到的poiitems有多少页
                List<PoiItem> poiItems = poiResult.getPois();// 取得第一页的poiitem数据，页数从数字0开始
                if (poiItems != null && poiItems.size() > 0) {
                    mAMap.clear();// 清理之前的图标
                    mSearchAdapter.clear();

                    for (PoiItem item : poiItems) {
                        MapSearchResult result = new MapSearchResult(item.getTitle(),
                                item.getLatLonPoint().getLatitude(),
                                item.getLatLonPoint().getLongitude());
                        result.setCity(item.getCityName());
                        mSearchAdapter.add(result);
                    }
                    mSearchAdapter.notifyDataSetChanged();
                    CommonUtils.hideSoftKeyboard(this);
                } else {
                    Toast.makeText(this, R.string.no_result, Toast.LENGTH_SHORT).show();
                }
            }
        }
    }

    @Override
    public void onPoiItemSearched(PoiItem poiItem, int i) {

    }

    @Override
    public void onMyLocationChange(Location location) {
        gotoLocation(location.getLatitude(), location.getLongitude());
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_set) {
            VirtualLocationManager.get().setCell(mCurUserId, mCurPkg, null);
            go(mSelectData.location.latitude, mSelectData.location.longitude);
        } else if (v.getId() == R.id.btn_checkin) {
            Intent intent = new Intent(this, CheckInActivity.class);
            startActivityForResult(intent, CheckInActivity.REQUEST_CODE);
        } else if (v.getId() == R.id.btn_save) {
            LocInfo locInfo = new LocInfo();
            locInfo.setLatitude(mSelectData.location.latitude);
            locInfo.setLongitude(mSelectData.location.longitude);
            locInfo.setAddress(mSelectData.location.address);
            showInfoDialog(this, locInfo);
        }
    }

    public void showInfoDialog(Context context, LocInfo info) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.Theme_MaterialComponents_Light_Dialog_Alert);
        builder.setTitle("地址数据");

        View view =
                LayoutInflater.from(context)
                        .inflate(R.layout.dialog_checkin_loc_info, null);
        TextView textView1 = view.findViewById(R.id.tv_info1);
        TextView textView2 = view.findViewById(R.id.tv_info2);
        TextView textView3 = view.findViewById(R.id.tv_info3);
        TextView textView4 = view.findViewById(R.id.tv_info4);
        EditText editText = view.findViewById(R.id.et_tag);
        editText.setText(info.getName());

        textView1.setText("经度：" + info.getLongitude());
        textView2.setText("纬度：" + info.getLatitude());
        if (info.getBaseStationId() == 0 && info.getLac() == 0) {
            textView3.setText("基站数据：无");
        } else {
            textView3.setText("基站数据：有");
        }
        textView4.setText("地址：" + info.getAddress());

        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                dialogInterface.dismiss();
            }
        });
        builder.setPositiveButton("保存", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                String name = editText.getText().toString();
                if (TextUtils.isEmpty(name)) {
                    Toast.makeText(context, "请输入名称再保存", Toast.LENGTH_SHORT).show();
                } else {
                    info.setName(name);
                    LocInfo saveLocInfo = new LocInfo(info);
                    saveLocInfo.setLocOrder(DbManager.getInstance().getLocInfoDao().getNextOrderId());
                    DbManager.getInstance().getLocInfoDao().insertOne(saveLocInfo);
                    dialogInterface.dismiss();

                }
            }
        });
        builder.setCancelable(false);
        builder.setView(view);
        builder.show();
    }

    @SuppressLint("DefaultLocale")
    private void go(double lat, double lon) {
        VCore.get().forceStop(this, mAppInfo);
        if (UserAgent.getInstance().isVipUser()) {
            VirtualLocationManager.get()
                    .setMode(mCurUserId, mCurPkg, VirtualLocationManager.MODE_USE_SELF);
        }
        double[] doubles = GPSUtil.gcj02_To_Gps84(lat, lon);
        mSelectData.location.latitude = doubles[0];
        mSelectData.location.longitude = doubles[1];
        VirtualLocationManager.get().setLocation(mCurUserId, mCurPkg, mSelectData.location);
        finish();
    }

    private static class MapSearchResult {
        private String address;
        private double lat;
        private double lng;
        private String city;
        public static final MapSearchResult NULL = new MapSearchResult();

        public MapSearchResult() {
        }

        public MapSearchResult(String address) {
            this.address = address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public MapSearchResult(String address, double lat, double lng) {
            this.address = address;
            this.lat = lat;
            this.lng = lng;
        }

        public void setCity(String city) {
            this.city = city;
        }

        @Override
        public String toString() {
            return address;
        }
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onResume() {
        super.onResume();
        mapView.onResume();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onPause() {
        super.onPause();
        mapView.onPause();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        mapView.onSaveInstanceState(outState);
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    /**
     * 方法必须重写
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        mapView.onDestroy();
    }

    private void showInputWindow() {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        View view = getLayoutInflater().inflate(R.layout.dialog_change_loc, null);
        builder.setView(view);
        Dialog dialog = builder.show();
        dialog.setCanceledOnTouchOutside(false);
        EditText editLon = view.findViewById(R.id.edt_lon);
        EditText editLat = view.findViewById(R.id.edt_lat);
        TextView btnOk = view.findViewById(R.id.btn_ok);

        if (mSelectData.location != null) {
            editLon.setText(CommonUtils.doubleFor8String(mSelectData.location.getLongitude()));
            editLat.setText(CommonUtils.doubleFor8String(mSelectData.location.getLatitude()));
        }

        dialog.setCancelable(false);
        view.findViewById(R.id.btn_cancel).setOnClickListener((v2) -> {
            dialog.dismiss();
        });

        //
        btnOk.setOnClickListener((v2) -> {
            dialog.dismiss();
            double lng = Double.parseDouble(editLon.getText().toString());
            double lat = Double.parseDouble(editLat.getText().toString());
            if (lng >= -180 && lng <= 180
                    && lat >= -90 && lat <= 90) {
                gotoLocation(lat, lng);
            } else {
                new CommonDialog.Builder(this)
                        .setTitle("提示")
                        .setMessage("输入内容有误，请检查格式：\n纬度-90~90，经度-180~180")
                        .setCancelable(true)
                        .setPositiveButton("明白")
                        .show();
            }
        });
    }

    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            onBackClicked();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackClicked() {
        if (!mSetLocChanged) {
            finish();
            return;
        }
        boolean closeLocChangeTips = AppSharePref.get().getBoolean("close_loc_change_tips", false);
        if (closeLocChangeTips) {
            finish();
            return;
        }
        new CommonDialog.Builder(this)
                .setTitle("提示")
                .setMessage("位置有更新，点击右下角【保存设置】生效")
                .setCancelable(true)
                .setPositiveButton("确定")
                .setNegativeButton("取消")
                .setShowCheck("不再提醒", new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        if (isChecked) {
                            AppSharePref.get().putBoolean("close_loc_change_tips", true);
                        }
                    }
                })
                .setCallback(new CommonDialog.Callback() {
                    @Override
                    public void onCancel() {
                        finish();
                    }

                    @Override
                    public void onOk() {

                    }
                })
                .show();
    }

}
