package com.xyz.host.home;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ImageView;
import android.widget.LinearLayout;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alipay.sdk.app.PayTask;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.R;
import com.xyz.host.home.adpater.VipProductAdapter;
import com.xyz.host.home.adpater.VipProductHorizontalAdapter;
import com.xyz.host.home.adpater.decorations.ItemOffsetDecoration;
import com.xyz.host.model.OrderInfo;
import com.xyz.host.model.PayEvent;
import com.xyz.host.model.PayResult;
import com.xyz.host.model.UserInfo;
import com.xyz.host.model.VipInfo;
import com.xyz.host.model.WxOrderInfo;
import com.xyz.host.user.engine.ApiCallback;
import com.xyz.host.user.engine.ApiServiceDelegate;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.GsonUtils;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.utils.UIUtils;
import com.xyz.host.utils.WXPayUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Map;

import io.reactivex.schedulers.Schedulers;

public class VipPaymentDialogFragment extends BottomSheetDialogFragment implements View.OnClickListener {
    private static final String TAG = "VipPaymentDialog";
    
    private VipInfo.Product mSelectProduct;
    private VipProductHorizontalAdapter mAdapter;

    // 标题相关
    private TextView tvTitle;
    private TextView tvSecTitle;

    // 支付方式相关
    private LinearLayout llWechatPay;
    private LinearLayout llAlipay;
    private ImageView ivWechatSelected;
    private ImageView ivAlipaySelected;
    private boolean isWechatSelected = false; // 默认选择微信支付

    public static VipPaymentDialogFragment newInstance() {
        return new VipPaymentDialogFragment();
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置透明背景，避免白色直角边
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            // 设置窗口标志，确保背景透明
            dialog.getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            );
        }

        // 设置弹窗从底部弹出的行为
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置底部sheet的背景为透明，确保圆角效果
                bottomSheet.setBackgroundResource(android.R.color.transparent);
            }
        });

        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_vip_payment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews(view);
        EventBus.getDefault().register(this);
        StatAgent.onEvent(getContext(), "vip_page");
    }

    @SuppressLint("NotifyDataSetChanged")
    private void initViews(View view) {
        // 关闭按钮
        view.findViewById(R.id.btn_close).setOnClickListener(v -> dismiss());

        // 初始化标题和副标题
        initTitleAndSubtitle(view);

        // 初始化支付方式选择
        initPaymentMethods(view);

        // 根据远程配置控制邀请好友卡片的显示
        initInviteCard(view);

        // 会员产品列表 - 改为水平滑动
        RecyclerView recyclerView = view.findViewById(R.id.rv_vip_list);
        LinearLayoutManager mLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(mLayoutManager);
        recyclerView.addItemDecoration(new ItemOffsetDecoration(UIUtils.dp2px(getContext(), 10)));

        mAdapter = new VipProductHorizontalAdapter(getContext());
        setupVipProducts();
        recyclerView.setAdapter(mAdapter);
        
        mAdapter.setOnItemClickListener((info, position) -> {
            for (VipInfo.Product product : mAdapter.getList()) {
                product.checked = false;
            }
            info.checked = true;
            mSelectProduct = info;
            mAdapter.notifyDataSetChanged();
        });

        // 支付按钮
        view.findViewById(R.id.vip_goto_pay).setOnClickListener(this);

        // 购买须知按钮
        view.findViewById(R.id.tv_purchase_tips).setOnClickListener(this);
    }

    /**
     * 初始化标题和副标题
     */
    private void initTitleAndSubtitle(View view) {
        tvTitle = view.findViewById(R.id.tv_title);
        tvSecTitle = view.findViewById(R.id.tv_sec_title);

        updateTitleAndSubtitle();
    }

    /**
     * 更新标题和副标题
     */
    private void updateTitleAndSubtitle() {
        if (tvTitle == null || tvSecTitle == null) {
            return;
        }

        if (UserAgent.getInstance().isVipUser()) {
            // 会员用户：显示欢迎语和有效期
            tvTitle.setText("尊敬的无界多开会员");

            UserInfo userInfo = UserAgent.getInstance().getUserInfo();
            if (userInfo != null) {
                long remaining = userInfo.expireTime - System.currentTimeMillis();
                long days = Math.round((float) remaining / 86400000f);
                if (days > 10000) {
                    tvSecTitle.setText("您的会员永久有效");
                } else {
                    if (!DateUtils.isToday(userInfo.expireTime)) {
                        String dateStr = getDateToString(userInfo.expireTime, "yyyy/MM/dd");
                        tvSecTitle.setText(String.format("会员有效期至 %s", dateStr));
                    } else {
                        String hourStr = getDateToString(userInfo.expireTime, "HH:mm");
                        tvSecTitle.setText(String.format("您的VIP将于今日 %s 到期", hourStr));
                    }
                }
            } else {
                tvSecTitle.setText("会员信息获取中...");
            }
        } else {
            // 非会员用户：显示开通会员的吸引语
            tvTitle.setText("成为无界多开会员");
            tvSecTitle.setText("解锁无限多开、位置保护、机型保护等全部特权功能");
        }
    }

    /**
     * 时间戳转换为指定格式的字符串
     */
    private static String getDateToString(long milSecond, String pattern) {
        Date date = new Date(milSecond);
        SimpleDateFormat format = new SimpleDateFormat(pattern, Locale.getDefault());
        return format.format(date);
    }

    private void setupVipProducts() {
        String productConfig = UMRemoteConfig.getInstance().getConfigValue("vip_info");
        VipInfo vipInfo = GsonUtils.gson2Bean(productConfig, VipInfo.class);
        
        if (vipInfo != null && vipInfo.list != null) {
            mAdapter.setList(vipInfo.list);
        } else {
            // 默认配置 - 匹配设计稿样式
            vipInfo = GsonUtils.gson2Bean("{\"list\":[{\"type\":\"year\",\"price\":\"128\",\"title\":\"年卡\",\"desc\":\"限时首发特惠\",\"checked\":true},{\"type\":\"season\",\"price\":\"90\",\"title\":\"季卡\",\"desc\":\"首发特惠\"},{\"type\":\"month\",\"price\":\"25\",\"title\":\"月卡\",\"desc\":\"购买后有效期自动延长\"}]}", VipInfo.class);
            if (vipInfo != null && vipInfo.list != null) {
                mAdapter.setList(vipInfo.list);
            }
        }

        // 设置默认选中项
        if (vipInfo != null && vipInfo.list != null && !vipInfo.list.isEmpty()) {
            boolean isChecked = false;
            for (VipInfo.Product product : vipInfo.list) {
                isChecked = product.checked;
                if (isChecked) {
                    mSelectProduct = product;
                    mSelectProduct.checked = true;
                    break;
                }
            }

            // 如果配置的都没有选中，则默认选择年卡（第一个）
            if (!isChecked) {
                mSelectProduct = vipInfo.list.get(0);
                mSelectProduct.checked = true;
            }
        }
    }

    /**
     * 初始化支付方式选择
     */
    private void initPaymentMethods(View view) {
        llWechatPay = view.findViewById(R.id.ll_wechat_pay);
        llAlipay = view.findViewById(R.id.ll_alipay);
        ivWechatSelected = view.findViewById(R.id.iv_wechat_selected);
        ivAlipaySelected = view.findViewById(R.id.iv_alipay_selected);

        // 设置点击事件
        llWechatPay.setOnClickListener(v -> selectPaymentMethod(true));
        llAlipay.setOnClickListener(v -> selectPaymentMethod(false));

        // 初始化选择状态
        updatePaymentMethodUI();
    }

    /**
     * 选择支付方式
     * @param isWechat true为微信支付，false为支付宝支付
     */
    private void selectPaymentMethod(boolean isWechat) {
        isWechatSelected = isWechat;
        updatePaymentMethodUI();
    }

    /**
     * 更新支付方式UI状态
     */
    private void updatePaymentMethodUI() {
        if (isWechatSelected) {
            ivWechatSelected.setImageResource(R.drawable.ic_radio_selected);
            ivAlipaySelected.setImageResource(R.drawable.ic_radio_unselected);
        } else {
            ivWechatSelected.setImageResource(R.drawable.ic_radio_unselected);
            ivAlipaySelected.setImageResource(R.drawable.ic_radio_selected);
        }
    }

    /**
     * 根据远程配置初始化邀请好友卡片
     */
    private void initInviteCard(View view) {
        View inviteCard = view.findViewById(R.id.ll_invite_card);
        boolean showInvite = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("show_invite"));

        if (showInvite) {
            inviteCard.setVisibility(View.VISIBLE);
            inviteCard.setOnClickListener(this);
        } else {
            inviteCard.setVisibility(View.GONE);
        }
    }

    private final ActivityResultLauncher<Intent> mLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (UserAgent.getInstance().isLogin()) {
                        // 登录成功后更新标题和副标题
                        updateTitleAndSubtitle();
                        // 然后执行支付
                        buyProduct(isWechatSelected ? 1 : 0); // 1为微信支付，0为支付宝支付
                    }
                }
            });

    private final ActivityResultLauncher<Intent> mInviteLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (UserAgent.getInstance().isLogin()) {
                        // 登录成功后跳转到邀请页面
                        openInvitePage();
                    }
                }
            });

    private void showLogin() {
        Intent intent = new Intent(getContext(), LoginActivity.class);
        mLauncher.launch(intent);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.vip_goto_pay) {
            if (!UserAgent.getInstance().isLogin()) {
                showLogin();
            } else {
                buyProduct(isWechatSelected ? 1 : 0); // 1为微信支付，0为支付宝支付
            }
        } else if (v.getId() == R.id.tv_purchase_tips) {
            // 打开购买须知页面
            showPurchaseTips();
        } else if (v.getId() == R.id.ll_invite_card) {
            // 跳转到邀请页面
            handleInviteCardClick();
        }
    }

    private void buyProduct(int payType) {
        if (mSelectProduct == null) {
            Toast.makeText(getContext(), "请选择会员套餐", Toast.LENGTH_SHORT).show();
            return;
        }

        final ApiServiceDelegate delegate = new ApiServiceDelegate();
        delegate.buy(payType, mSelectProduct.type, new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里处理请求参数
            }

            @Override
            public void onSuccess(String result) {
                Schedulers.io().scheduleDirect(() -> {
                    if (ApiServiceDelegate.PAY_ALI == payType) {
                        handleAlipayResult(result);
                    } else {
                        handleWechatPayResult(result);
                    }
                });
            }

            @Override
            public void onFail(String error) {
                if (getContext() != null) {
                    Toast.makeText(getContext(), "购买异常，请稍后重试", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private void handleAlipayResult(String result) {
        OrderInfo orderInfo = GsonUtils.gson2Bean(result, OrderInfo.class);
        if (orderInfo == null || getActivity() == null) {
            return;
        }

        PayTask alipay = new PayTask(getActivity());
        Map<String, String> mapResult = alipay.payV2(orderInfo.param, true);
        PayResult payResult = new PayResult(mapResult);
        
        if (TextUtils.equals(payResult.getResultStatus(), "9000")) {
            updateUserInfo(new PayEvent(true));
        }
    }

    private void handleWechatPayResult(String result) {
        WxOrderInfo orderInfo = GsonUtils.gson2Bean(result, WxOrderInfo.class);
        if (orderInfo == null || getContext() == null) {
            return;
        }

        WXPayUtils.WXPayBuilder builder = new WXPayUtils.WXPayBuilder();
        builder.setAppId(orderInfo.param.appid)
                .setPartnerId(orderInfo.param.partnerid)
                .setPrepayId(orderInfo.param.prepayid)
                .setPackageValue(orderInfo.param.pkg)
                .setNonceStr(orderInfo.param.noncestr)
                .setTimeStamp(orderInfo.param.timestamp)
                .setSign(orderInfo.param.sign)
                .build()
                .toWXPay(getContext());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateUserInfo(PayEvent event) {
        if (!event.mIsSuccess) {
            return;
        }
        
        new ApiServiceDelegate().user(UserAgent.getInstance().getMobile(), new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 处理请求参数
            }

            @Override
            public void onSuccess(String result) {
                if (getContext() != null) {
                    Toast.makeText(getContext(), "支付成功", Toast.LENGTH_LONG).show();
                    // 支付成功后更新标题显示会员状态
                    updateTitleAndSubtitle();
                    EventBus.getDefault().post(UserAgent.getInstance().getUserInfo());

                    // 延迟关闭弹窗，让用户看到会员状态变化
                    tvTitle.postDelayed(() -> dismiss(), 1000);
                }
            }

            @Override
            public void onFail(String error) {
                // 处理失败情况
            }
        });
    }

    /**
     * 显示购买须知页面
     */
    private void showPurchaseTips() {
        if (getContext() != null) {
            Intent intent = new Intent(getContext(), VipTipActivity.class);
            startActivity(intent);
        }
    }

    /**
     * 处理邀请卡片点击事件
     */
    private void handleInviteCardClick() {
        if (getContext() != null) {
            // 检查用户是否已登录
            if (!UserAgent.getInstance().isLogin()) {
                // 未登录，先引导用户登录
                showLoginForInvite();
            } else {
                // 已登录，直接跳转到邀请页面
                openInvitePage();
            }
        }
    }

    /**
     * 为邀请功能显示登录页面
     */
    private void showLoginForInvite() {
        Intent intent = new Intent(getContext(), LoginActivity.class);
        mInviteLauncher.launch(intent);
    }

    /**
     * 打开邀请页面
     */
    private void openInvitePage() {
        com.yingyu.zh.multiapp.InviteRedeemActivity.start(getContext());
        // 关闭当前弹窗，让用户专注于邀请功能
        dismiss();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }
}
