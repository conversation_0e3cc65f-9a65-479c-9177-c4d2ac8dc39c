package com.xyz.host.home;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.fun.vbox.client.core.VCore;
import com.fun.vbox.utils.VirtualRuntime;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.home.add.AddAppActivity;
import com.xyz.host.home.view.MineRowView;
import com.xyz.host.model.UserInfo;
import com.xyz.host.ads.TtFullScreenAd;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.DialogUtils;
import com.xyz.host.utils.LogcatCollector;
import com.xyz.host.utils.share.Share2;
import com.xyz.host.utils.share.ShareContentType;
import com.yingyu.zh.multiapp.AdIdByReviewMode;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.internal.schedulers.IoScheduler;

public class MyFragment extends Fragment implements View.OnClickListener {

    public static MyFragment newInstance() {
        return new MyFragment();
    }

    private Disposable mDisposable = null;
    private TextView mTvVipTime;
    private ImageView mLoginIcon;
    private TextView mLoginName;
    private MineRowView mBtnLogin;
    private MineRowView mNewVersion;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_my, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        init(view);
    }

    private void init(View root) {
        final Activity activity = getActivity();
        if (!CommonUtils.isLiveActivity(activity)) {
            return;
        }
        mTvVipTime = root.findViewById(R.id.tvVipTime);
        mLoginIcon = root.findViewById(R.id.iv_login_head);
        mLoginName = root.findViewById(R.id.tv_login_name);
        mBtnLogin = root.findViewById(R.id.mrv_logout);
        mBtnLogin.setTvTitle("登录");
        mBtnLogin.setOnClickListener(this);

        root.findViewById(R.id.mrv_vip).setOnClickListener(this);

        // 根据远程配置控制邀请兑换入口的显示
        View inviteRedeemView = root.findViewById(R.id.mrv_invite_redeem);
        boolean showInvite = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("show_invite"));
        if (showInvite) {
            inviteRedeemView.setVisibility(View.VISIBLE);
            inviteRedeemView.setOnClickListener(this);
        } else {
            inviteRedeemView.setVisibility(View.GONE);
        }

        mNewVersion = root.findViewById(R.id.mrv_version);
        mNewVersion.setOnClickListener(this);
        root.findViewById(R.id.mrv_about).setOnClickListener(this);
        root.findViewById(R.id.mrv_contact).setOnClickListener(this);
        // 根据远程配置控制隐藏应用入口的显示
        View hideAppView = root.findViewById(R.id.mrv_hide_app);
        boolean showHideApp = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("show_hide_app"));
        if (showHideApp) {
            hideAppView.setVisibility(View.VISIBLE);
            hideAppView.setOnClickListener(this);
        } else {
            hideAppView.setVisibility(View.GONE);
        }
        root.findViewById(R.id.card_view1).setOnClickListener(this);

        // showRewardTime();
        updateUserInfo();

        EventBus.getDefault().register(this);

        if ("honor".equals(CommonUtils.getMetaData(activity, "UMENG_CHANNEL"))) {
            mNewVersion.setVisibility(View.GONE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLoginEvent(UserInfo userInfo) {
        updateUserInfo();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
        }
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.mrv_about) {
            startActivity(new Intent(getActivity(), AboutActivity.class));
        } else if (view.getId() == R.id.mrv_contact) {
            contact();
        } else if (view.getId() == R.id.card_view1) {
            showLogin();
        } else if (view.getId() == R.id.mrv_logout) {
            showLogin();
        } else if (view.getId() == R.id.mrv_vip) {
            VipPaymentHelper.showVipPaymentDialog(requireActivity());
        } else if (view.getId() == R.id.mrv_invite_redeem) {
            handleInviteRedeem();
        } else if (view.getId() == R.id.mrv_version) {
            boolean isGoBrowser = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("browser_open_nv"));
            if (isGoBrowser) {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(WebActivity.NEW_VERSION_URL));
                startActivity(intent);
            } else {
                WebActivity.go(getActivity(), WebActivity.NEW_VERSION_URL);
            }
        } else if (view.getId() == R.id.mrv_hide_app) {
            handleHideApp();
        }
    }

    /**
     * 处理邀请兑换功能 - 需要先登录
     */
    private void handleInviteRedeem() {
        if (!UserAgent.getInstance().isLogin()) {
            // 未登录，先跳转到登录页面
            LoginActivity.go(getActivity());
            return;
        }
        // 已登录，直接跳转到邀请兑换页面
        com.yingyu.zh.multiapp.InviteRedeemActivity.start(getActivity());
    }

    /**
     * 处理隐藏应用功能
     */
    private void handleHideApp() {
        StatAgent.onEvent(getContext(), "tool", "name","appHide");
        if (!UserAgent.getInstance().isVipUser()) {
            String codeId = AdIdByReviewMode.getAdId("hide_ads_id");
            new TtFullScreenAd().load(getActivity(), codeId);
        }

        StatAgent.onEvent(getActivity(), "tool", "name", "appHide");
        Intent intent = new Intent(getActivity(), AddAppActivity.class);
        intent.putExtra("type", "app_hide");
        startActivityForResult(intent, 100);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 100 && resultCode == 101 && data != null) {
            // 将结果转发给MyActivity处理
            if (getActivity() instanceof com.yingyu.zh.multiapp.MyActivity) {
                ((com.yingyu.zh.multiapp.MyActivity) getActivity()).handleHideAppResult(data);
            }
        }
    }



    private void showLogin() {
        if (UserAgent.getInstance().isLogin()) {
            new CommonDialog.Builder(getContext())
                    .setMessage("确定要退出登录吗？")
                    .setNegativeButton("取消")
                    .setCallback(new CommonDialog.Callback() {

                        @Override
                        public void onOk() {
                            super.onOk();
                            UserAgent.getInstance().setUserInfo(null);
                            UserAgent.getInstance().cleanUserInfo();
                            updateUserInfo();
                        }
                    }).show();
            return;
        }
        LoginActivity.go(getActivity());
    }

    private void updateUserInfo() {
        String nick = TextUtils.isEmpty(UserAgent.getInstance().getNick())
                ? "无界多开" : UserAgent.getInstance().getNick();
        mLoginName.setText(nick);
        if (!TextUtils.isEmpty(UserAgent.getInstance().getHeadUrl())) {
            displayCircleImage(MainApp.getApp(), UserAgent.getInstance().getHeadUrl(), mLoginIcon, 0,
                    R.drawable.ic_login_head);
        } else {
            mLoginIcon.setImageResource(R.drawable.ic_login_head);
        }
        if (UserAgent.getInstance().isLogin()) {
            mBtnLogin.setTvTitle("退出登录");
        } else {
            mBtnLogin.setTvTitle("登录");
        }

        UserInfo userInfo = UserAgent.getInstance().getUserInfo();
        if (UserAgent.getInstance().isVipUser()) {
            if (userInfo != null) {
                long remaining = userInfo.expireTime - System.currentTimeMillis();
                long days = Math.round((float) remaining / 86400000f);
                if (days > 10000) {
                    mTvVipTime.setText(R.string.vip_no_expire);
                } else {
                    if (!DateUtils.isToday(userInfo.expireTime)) {
                        String dateStr = getDateToString(userInfo.expireTime, "yyyy-MM-dd");
                        mTvVipTime.setText(getString(R.string.vip_time_expire, days, dateStr));
                    } else {
                        String hourStr = getDateToString(userInfo.expireTime, "HH:mm");
                        mTvVipTime.setText(getString(R.string.vip_time_expire_hour, hourStr));
                    }
                }
            } else {
                mTvVipTime.setText("暂未开通会员");
            }
        } else {
            mTvVipTime.setText("暂未开通会员");
        }
    }

    private static String getDateToString(long milSecond, String pattern) {
        Date date = new Date(milSecond);
        SimpleDateFormat format = new SimpleDateFormat(pattern, Locale.getDefault());
        return format.format(date);
    }

    @SuppressLint("CheckResult")
    private void displayCircleImage(Context context, String url, ImageView target,
                                    int placeholder, int errorHolder) {
        if (context == null || TextUtils.isEmpty(url)) {
            return;
        }
        RequestBuilder<Drawable> builder = Glide.with(context.getApplicationContext())
                .load(url);
        if (placeholder > 0) {
            builder.placeholder(placeholder);
        }
        if (errorHolder > 0) {
            builder.error(errorHolder);
        }
        builder.apply(RequestOptions.bitmapTransform(new CircleCrop())).into(target);
    }

    private void contact() {
        // shareDebugLog();

        if(VCore.get().isOutsideInstalled("com.tencent.mobileqq")){
            String qq = UMRemoteConfig.getInstance().getConfigValue("contact_qq");
            final String qqUrl = String.format(Locale.ROOT, "mqqwpa://im/chat?chat_type=wpa&uin=%s&version=1", qq);
            try {
                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(qqUrl)));
            } catch (Throwable e) {
                // Ignored.
            }
        } else {
            Toast.makeText(getActivity(),"请安装QQ客户端",Toast.LENGTH_SHORT).show();
        }
    }

    public void shareDebugLog() {
        final Dialog dialog = DialogUtils.createLoadingDialog(getActivity(), "拉取日志中...", true,
                0);
        dialog.show();
        new Thread(new Runnable() {
            @Override
            public void run() {
                File file = LogcatCollector.collectLogCat(1000 * 60 * 30);
                VirtualRuntime.getUIHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        dialog.dismiss();
                        Uri uri = FileProvider.getUriForFile(MainApp.getApp(), MainApp.getApp().getPackageName() + ".provider", file);
                        new Share2.Builder(getActivity())
                                .setContentType(ShareContentType.FILE)
                                .setShareFileUri(uri)
                                .setTitle("Share File")
                                .build()
                                .shareBySystem();
                    }
                });
            }
        }).start();
    }
}
