package com.xyz.host.home;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.gw.swipeback.SuperSwipeBackLayout;
import com.gw.swipeback.SwipeBackLayout;
import com.umeng.socialize.UMAuthListener;
import com.umeng.socialize.UMShareAPI;
import com.umeng.socialize.UMShareConfig;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.xyz.host.R;
import com.xyz.host.maskprivacy.PolicyActivity;
import com.xyz.host.user.engine.ApiCallback;
import com.xyz.host.user.engine.ApiServiceDelegate;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.DialogUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.Map;

public class LoginActivity extends AppCompatActivity implements View.OnClickListener {
    private SuperSwipeBackLayout mSwipeBackLayout;
    private UMShareAPI mUMShareAPI;
    private Dialog mLoadingDialog;
    private CheckBox mChkPrivacy;

    public static void go(Activity activity) {
        Intent intent = new Intent(activity, LoginActivity.class);
        activity.startActivity(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        mUMShareAPI.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.slide_bottom_in, R.anim.slide_bottom_out);
        setContentView(R.layout.dlg_login);

        int heightPixels = getResources()
                .getDisplayMetrics().heightPixels;

        Window win = this.getWindow();
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = (int) (heightPixels * 0.25);
        lp.dimAmount = 0.8f;
        lp.gravity = Gravity.BOTTOM;
        win.setAttributes(lp);

        initViews();
        initData();

        mSwipeBackLayout = new SuperSwipeBackLayout(this);
        mSwipeBackLayout.attachToActivity(this);
        mSwipeBackLayout.setDirectionMode(SwipeBackLayout.FROM_TOP);
        mSwipeBackLayout.setMaskAlpha(100);
    }

    private void initViews() {
        mChkPrivacy = findViewById(R.id.chk_agree_privacy);
        findViewById(R.id.imv_login_qq).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mUMShareAPI.isInstall(LoginActivity.this, SHARE_MEDIA.QQ)) {
                    Toast.makeText(LoginActivity.this, "您未安装QQ，请先安装QQ", Toast.LENGTH_SHORT).show();
                } else if (!mChkPrivacy.isChecked()) {
                    Toast.makeText(LoginActivity.this, "请先阅读并同意《隐私政策》和《用户协议》并勾选", Toast.LENGTH_SHORT).show();
                } else {
                    umLogin(SHARE_MEDIA.QQ);
                }
            }
        });

        findViewById(R.id.imv_login_wechat).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!mUMShareAPI.isInstall(LoginActivity.this, SHARE_MEDIA.WEIXIN)) {
                    Toast.makeText(LoginActivity.this, "您未安装微信，请先安装微信", Toast.LENGTH_SHORT).show();
                } else if (!mChkPrivacy.isChecked()) {
                    Toast.makeText(LoginActivity.this, "请先阅读并同意《隐私政策》和《用户协议》并勾选", Toast.LENGTH_SHORT).show();
                } else {
                    umLogin(SHARE_MEDIA.WEIXIN);
                }
            }
        });

        findViewById(R.id.imv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        initAgreeUserPrivacyPolicy(findViewById(R.id.txv_privacy));
    }

    private void umLogin(SHARE_MEDIA media) {
        mLoadingDialog = DialogUtils.createLoadingDialog(LoginActivity.this, "登录中……");
        mLoadingDialog.show();
        mUMShareAPI.getPlatformInfo(LoginActivity.this, media, new UMAuthListener() {
            @Override
            public void onStart(SHARE_MEDIA share_media) {
                Toast.makeText(LoginActivity.this, "开始登录：" + share_media.name(), Toast.LENGTH_LONG).show();
            }

            @Override
            public void onComplete(SHARE_MEDIA share_media, int i, Map<String, String> map) {
                // Toast.makeText(LoginActivity.this, "成功了", Toast.LENGTH_LONG).show();
                handleLoginSuccess(share_media, map);
            }

            @Override
            public void onError(SHARE_MEDIA share_media, int i, Throwable throwable) {
                mLoadingDialog.dismiss();
                // Toast.makeText(LoginActivity.this, "失败：" + throwable.getMessage(), Toast.LENGTH_LONG).show();
            }

            @Override
            public void onCancel(SHARE_MEDIA share_media, int i) {
                mLoadingDialog.dismiss();
                // Toast.makeText(LoginActivity.this, "取消了", Toast.LENGTH_LONG).show();
            }
        });
    }

    private void handleLoginSuccess(SHARE_MEDIA shareMedia, Map<String, String> map) {
        String openid = map.get("openid");
        String url = map.get("profile_image_url");
        String name = map.get("name");

        UserAgent.getInstance().setMobile(openid);
        UserAgent.getInstance().setHeadUrl(url);
        UserAgent.getInstance().setNick(name);

        new ApiServiceDelegate().login(openid, name, new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {

            }

            @Override
            public void onSuccess(String result) {
                mLoadingDialog.dismiss();
                EventBus.getDefault().post(UserAgent.getInstance().getUserInfo());
                finish();
            }

            @Override
            public void onFail(String error) {
                mLoadingDialog.dismiss();
                Toast.makeText(LoginActivity.this, "登录异常，请稍后重试", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void initData() {
        mUMShareAPI = UMShareAPI.get(LoginActivity.this);
        UMShareConfig config = new UMShareConfig();
        config.isNeedAuthOnGetUserInfo(true);
        mUMShareAPI.setShareConfig(config);
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mUMShareAPI.release();
    }

    private void initAgreeUserPrivacyPolicy(TextView agreeUserPrivacyPolicyTv) {
        String msg = agreeUserPrivacyPolicyTv.getText().toString();
        SpannableStringBuilder ssb = getUserPrivacyPolicy(msg, R.color.black);
        agreeUserPrivacyPolicyTv.setMovementMethod(LinkMovementMethod.getInstance());
        agreeUserPrivacyPolicyTv.setText(ssb, TextView.BufferType.SPANNABLE);
    }

    @NonNull
    private SpannableStringBuilder getUserPrivacyPolicy(String msg, @ColorRes int id) {
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        ssb.append(msg);
        int start = msg.indexOf("《"); //第一个出现的位置
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View view) {
                Intent privacyIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(WebActivity.PRIVACY_URL));
                startActivity(privacyIntent);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(ContextCompat.getColor(LoginActivity.this, id));
                ds.setUnderlineText(true);
            }

        }, start, start + 6, 0);
        int end = msg.lastIndexOf("《");
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View view) {
                Intent intent1 = new Intent(LoginActivity.this, PolicyActivity.class);
                intent1.putExtra("action", getString(R.string.service_use_policy));
                startActivity(intent1);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(ContextCompat.getColor(LoginActivity.this, id));
                ds.setUnderlineText(true);
            }
        }, end, end + 6, 0);
        return ssb;
    }
}