package com.xyz.host.home;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.chad.library.adapter.base.listener.OnItemDragListener;
import com.chad.library.adapter.base.module.BaseDraggableModule;
import com.fun.vbox.client.core.VCore;
import com.fun.vbox.entity.VAppInfo;
import com.fun.vbox.utils.AbiUtils;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.ads.RewardHandler;
import com.xyz.host.ads.TtFullScreenAd;
import com.xyz.host.db.AppInfo;
import com.xyz.host.db.DbManager;
import com.xyz.host.db.DockerBean;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.dialog.HomeGuideDialog;
import com.xyz.host.dialog.ModifyAppNameDialog;
import com.xyz.host.dialog.ShortcutDialog;
import com.xyz.host.home.add.AddAppActivity;
import com.xyz.host.home.adpater.InstalledAppAdapter;
import com.xyz.host.home.adpater.ToolAdapter;
import com.xyz.host.home.adpater.decorations.NoMarginDecoration;
import com.xyz.host.home.view.ListPopupMenu;
import com.xyz.host.home.view.NumberInputDialog;
import com.xyz.host.model.ToolBean;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.ApkUtils;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.GridSpaceItemDecoration;
import com.xyz.host.utils.InstallExtUtils;
import com.xyz.host.utils.StatAgent;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

public class LaunchFragment extends Fragment implements OnClickListener {
    private final static String TAG = "LaunchFragment";

    public static LaunchFragment newInstance() {
        return new LaunchFragment();
    }

    private TextView tvReward;
    private TextView tvTips;
    private InstalledAppAdapter installedAppAdapter;
    private final List<DockerBean> appInfos = new ArrayList<>();
    private RecyclerView rvAppList;
    private int mRewardMin = 30;
    private RewardHandler mRewardHandler;
    private Disposable mDisposable = null;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_launch, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        init(view);
        initTools(view);

        mRewardHandler = new RewardHandler();
        mRewardHandler.init();
        showRewardTime();
    }
    
    public void showUseGuide() {
        if (!AppSharePref.get().getBoolean(AppSharePref.Has_Click_Item_More)) {
            try {
                if (installedAppAdapter.getItemCount() <= 0) {
                    return;
                }
                rvAppList.scrollToPosition(0);
                rvAppList.post(new Runnable() {
                    @Override
                    public void run() {
                        if (rvAppList.getLayoutManager().getChildCount() > 0) {
                            View view = rvAppList.getLayoutManager().getChildAt(0);
                            View moreView = view.findViewById(R.id.item_app_more_iv);
                            Rect globalVisibleRect = new Rect();
                            moreView.getGlobalVisibleRect(globalVisibleRect);
                            HomeGuideDialog dialog = new HomeGuideDialog(getActivity(), globalVisibleRect);
                            dialog.show();
                        }
                    }
                });
            } catch (Throwable ignore) {
                //
            }
        } else {
            afterShowUseGuide();
        }
    }

    private long getRewardTime() {
        long time = AppSharePref.get().getLong("expire_time", 0);
        if (time != 0) {
            long currentTime = System.currentTimeMillis();
            return time - currentTime;
        } else {
            return 0;
        }
    }

    // 当Activity销毁时，取消订阅
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
        }
    }

    @SuppressLint("SetTextI18n")
    private void showRewardTime() {
        long diffTime = getRewardTime();
        if (diffTime <= 0) {
            tvReward.setVisibility(View.GONE);
            return;
        }
        long diffHours = TimeUnit.MILLISECONDS.toHours(diffTime);
        long diffMinutes = TimeUnit.MILLISECONDS.toMinutes(diffTime) % 60;
        long diffSeconds = TimeUnit.MILLISECONDS.toSeconds(diffTime) % 60;

        long totalDiffMinutes = TimeUnit.MILLISECONDS.toMinutes(diffTime);
        if (totalDiffMinutes > mRewardMin) {
            AppSharePref.get().putLong("expire_time", 0);
            Toast.makeText(getActivity(), "时间异常，奖励时间清零", Toast.LENGTH_SHORT).show();
            tvReward.setVisibility(View.GONE);
            return;
        }

        StringBuilder rewardTime = new StringBuilder("奖励时间剩余：");
        if (diffHours > 0) {
            rewardTime.append(diffHours).append("小时");
        }
        if (diffMinutes > 0) {
            rewardTime.append(diffMinutes).append("分钟");
        }
        rewardTime.append(diffSeconds).append("秒");

        tvReward.setVisibility(View.VISIBLE);
        tvReward.setText(rewardTime.toString());

        if (mDisposable == null) {
            mDisposable = Observable.interval(1, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            showRewardTime();
                        }
                    });
        }
    }

    private void afterShowUseGuide() {
    }

    private void init(View root) {
        tvTips = root.findViewById(R.id.tv_tips);
        tvReward = root.findViewById(R.id.tv_reward);
        rvAppList = root.findViewById(R.id.rv_app_list);
        rvAppList.addItemDecoration(new GridSpaceItemDecoration(3,
                CommonUtils.dip2px(MainApp.getApp(), 4f),
                CommonUtils.dip2px(MainApp.getApp(), 4f)));

        rvAppList.setLayoutManager(new GridLayoutManager(MainApp.getApp(), 3));
        installedAppAdapter = new InstalledAppAdapter(appInfos);

        //创建适配器.拖拽
        BaseDraggableModule draggableModule = installedAppAdapter.getDraggableModule();
        draggableModule.setDragEnabled(true);
        draggableModule.getItemTouchHelperCallback().setDragMoveFlags(ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT | ItemTouchHelper.UP | ItemTouchHelper.DOWN);//拖拽方向
        draggableModule.setOnItemDragListener(getOnItemDragListener());

        rvAppList.setAdapter(installedAppAdapter);

        loadAppData(null);

        root.findViewById(R.id.fab_add_app).setOnClickListener(this);

        installedAppAdapter.setOnItemClickListener((adapter, view, position) -> {
            DockerBean bean = (DockerBean) adapter.getItem(position);
            String installType = bean.getInstallType();
            boolean is64bit;
            if ("app".equals(installType)) {
                if (!VCore.get().isOutsideInstalled(bean.getPackageName())) {
                    Toast.makeText(getActivity(), "请先在手机中安装" + bean.getAppName(), Toast.LENGTH_SHORT).show();
                    return;
                }
                is64bit = AbiUtils.is64BitAbi(bean.getPackageName());
            } else {
                is64bit = "64".equals(bean.getAppBit());
            }
            if (!is64bit) {
                if (InstallExtUtils.handlerInstall(getActivity())) {
                    return;
                }
            }
            if (!UserAgent.getInstance().isVipUser()) {
                String value = UMRemoteConfig.getInstance().getConfigValue("free_app_count");
                int freeAppCount = 1;
                try {
                    freeAppCount = Integer.parseInt(value);
                } catch (Throwable e) {
                    // Ignored.
                }
                if (installedAppAdapter.getItemCount() > freeAppCount || bean.getUserId() != 0) {
                    String rewardTip = "";
                    try {
                        String strValue = UMRemoteConfig.getInstance().getConfigValue("reward_min");
                        mRewardMin = Integer.parseInt(strValue);
                        rewardTip = "奖励" + strValue + "分钟";
                    } catch (Throwable e) {
                        // Ignored.
                    }

                    if (mRewardHandler.handleReward(getActivity(), new Runnable() {
                        boolean isLaunched = false;
                        @Override
                        public void run() {
                            showRewardTime();
                            if (isLaunched) {
                                return;
                            }
                            launchApp(bean, is64bit);
                            isLaunched = true;
                        }
                    })) {
                        return;
                    }
                }
            }
            launchApp(bean, is64bit);
        });

        ListPopupMenu menu = new ListPopupMenu(getActivity());
        installedAppAdapter.setMoreClickListener(new InstalledAppAdapter.OnMoreClickListener() {
            @Override
            public void onMoreClick(View view, DockerBean model) {
                AppSharePref.get().putBoolean(AppSharePref.Has_Click_Item_More, true);
                Context context = getActivity();
                menu.setOnItemClickListener(item -> {
                    if (item == ListPopupMenu.MENU_ITEM.SHORTCUT) {
                        new ShortcutDialog(context, model).show();
                    } else if (item == ListPopupMenu.MENU_ITEM.DELETE) {
                        new AlertDialog.Builder(context)
                                .setTitle(R.string.delete_app)
                                .setMessage(getString(R.string.delete_app_msg, model.getAppName()))
                                .setPositiveButton(android.R.string.yes, (dialog, which) -> {
                                    DbManager.getInstance().deleteOne(model.getUserId(), model.getPackageName());
                                    if ("app".equals(model.getInstallType())) {
                                        VCore.get().uninstallPackage(getActivity(), model.getAppInfo());
                                    } else {
                                        List<DockerBean> list = DbManager.getInstance().getListByPkg(model.getPackageName());
                                        if (list == null || list.isEmpty()) {
                                            VCore.get().fullUninstallApk(getActivity(), model.getAppInfo());
                                        }
                                    }
                                    installedAppAdapter.remove(model);
                                })
                                .setNegativeButton(android.R.string.no, null)
                                .show();
                    } else if (item == ListPopupMenu.MENU_ITEM.CUSTOM_NAME) {
                        new ModifyAppNameDialog(getActivity(), model.getUserId(), model.getPackageName(), model.getAppName())
                                .setModifyNameCallback(name -> {
                                    model.setAppName(name);
                                    installedAppAdapter.notifyDataSetChanged();
                                    DbManager.getInstance().updateAppName(model);
                                }).show();
                    } else if (item == ListPopupMenu.MENU_ITEM.MORE) {
                        AppDetailActivity.go(getActivity(), model.getAppInfo(), model.getAppName());
                    }
                });
                menu.showLocation(view);
            }
        });
    }

    private void launchApp(DockerBean bean, boolean is64bit) {
        ShortcutHandleActivity.go(getActivity(), bean);
        DbManager.getInstance().updateOpenByPkg(bean);
        if (is64bit) {
            StatAgent.onEvent(getActivity(), "dual_launch", "name", bean.getAppName());
        } else {
            StatAgent.onEvent(getActivity(), "dual_launch_32", "name", bean.getAppName());
        }
    }

    private void initTools(View root) {
        ArrayList<ToolBean> list = new ArrayList<>();
        list.add(new ToolBean()
                .setName(getString(R.string.app_hide))
                .setIconResId(R.drawable.ic_hide_app)
                .setOnClickListener(v -> {
                    if (!UserAgent.getInstance().isVipUser()) {
                        String codeId = UMRemoteConfig.getInstance().getConfigValue("hide_ads_id");
                        new TtFullScreenAd().load(getActivity(), codeId);
                    }

                    StatAgent.onEvent(getActivity(), "tool", "name", "appHide");
                    Intent intent = new Intent(getActivity(), AddAppActivity.class);
                    intent.putExtra("type", "app_hide");
                    startActivityForResult(intent, 100);
                }));

        if (!"xiaomi".equals(CommonUtils.getMetaData(getActivity(), "UMENG_CHANNEL"))) {
            int componentEnabled = MainApp.getApp().getPackageManager().getComponentEnabledSetting(
                    new ComponentName(MainApp.getApp(), SplashActivity.class.getName()));
            boolean isDefaultLauncher = PackageManager.COMPONENT_ENABLED_STATE_DEFAULT == componentEnabled
                    || PackageManager.COMPONENT_ENABLED_STATE_ENABLED == componentEnabled;
            int resId = R.drawable.ic_launcher;
            if (isDefaultLauncher) {
                resId = R.drawable.ic_calculator;
            }
            list.add(new ToolBean()
                    .setName(getString(R.string.change_icon))
                    .setIconResId(resId)
                    .setOnClickListener(v -> {
                        if (!UserAgent.getInstance().isVipUser()) {
                            String codeId = UMRemoteConfig.getInstance().getConfigValue("app_icon_ads_id");
                            new TtFullScreenAd().load(getActivity(), codeId);
                        }

                        StatAgent.onEvent(getActivity(), "tool", "name", "appIcon");
                        new CommonDialog.Builder(getActivity())
                                .setTitle(R.string.notice)
                                .setMessage("设置图标会退出应用，可以去桌面找到图标")
                                .setPositiveButton(R.string.ensure)
                                .setNegativeButton(R.string.cancel)
                                .setCallback(new CommonDialog.Callback() {
                                    @Override
                                    public void onOk() {
                                        camoApp(isDefaultLauncher);
                                    }
                                }).show();
                    }));
        }

        list.add(new ToolBean()
                .setName(getString(R.string.set_pass))
                .setIconResId(R.drawable.ic_set_pass)
                .setOnClickListener(v -> {
                    if (!UserAgent.getInstance().isVipUser()) {
                        String codeId = UMRemoteConfig.getInstance().getConfigValue("pass_full_ads_id");
                        new TtFullScreenAd().load(getActivity(), codeId);
                    }

                    StatAgent.onEvent(getActivity(), "tool", "name", "setPass");
                    new NumberInputDialog(getActivity()).show();
                }));

        list.add(new ToolBean()
                .setName(getString(R.string.share))
                .setIconResId(R.drawable.ic_share)
                .setOnClickListener(v -> {
                    StatAgent.onEvent(getActivity(), "tool", "name", "share");
                    CommonUtils.share(getActivity());
                }));

        list.add(new ToolBean()
                .setName(getString(R.string.remove_ad))
                .setIconResId(R.drawable.ic_vip)
                .setOnClickListener(v -> {
                    StatAgent.onEvent(getActivity(), "tool", "name", "vip");
                    VipPaymentHelper.showVipPaymentDialog(getActivity());
                }));

        RecyclerView rvTools = root.findViewById(R.id.rv_tool_list);
        NoMarginDecoration marginDecoration = new NoMarginDecoration(0);
        rvTools.addItemDecoration(marginDecoration);
        ToolAdapter adapter = new ToolAdapter(MainApp.getApp(), list);
        rvTools.setAdapter(adapter);
        rvTools.setLayoutManager(new StaggeredGridLayoutManager(5, StaggeredGridLayoutManager.VERTICAL));
    }

    private void camoApp(boolean isSetCalc) {
        if (isSetCalc) {
            MainApp.getApp().getPackageManager().setComponentEnabledSetting(new ComponentName(MainApp.getApp(), SplashActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
            MainApp.getApp().getPackageManager().setComponentEnabledSetting(new ComponentName(MainApp.getApp(), SplashCalculatorActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        } else {
            MainApp.getApp().getPackageManager().setComponentEnabledSetting(new ComponentName(MainApp.getApp(), SplashCalculatorActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
            MainApp.getApp().getPackageManager().setComponentEnabledSetting(new ComponentName(MainApp.getApp(), SplashActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }
    }

    private void loadAppData(Integer userId) {
        DbManager.getInstance().getAllObserve(userId).observe(getActivity(), dockerBeans -> {
            tvTips.setVisibility(View.GONE);
//            if (dockerBeans.size() > 0) {
//                tvTips.setVisibility(View.GONE);
//            } else {
//                tvTips.setVisibility(View.VISIBLE);
//                tvTips.setText(getString(R.string.no_data));
//            }
            installedAppAdapter.setNewInstance(dockerBeans);
            if (dockerBeans == null || dockerBeans.isEmpty()) {
                View view = LayoutInflater.from(getContext()).inflate(R.layout.item_installed_empty, rvAppList, false);
                view.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        startActivityForResult(new Intent(MainApp.getApp(), AddAppActivity.class), 100);
                    }
                });
                installedAppAdapter.setEmptyView(view);
            } else {
                showUseGuide();
            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.fab_add_app) {
            startActivityForResult(new Intent(MainApp.getApp(), AddAppActivity.class), 100);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == 101 && data != null) {
            AppInfo appInfo = data.getParcelableExtra("appInfo");
            String type = data.getStringExtra("type");
            String installType = appInfo.getInstallType();
            String packageName = appInfo.getPackageName();
            int userId = getUserId(packageName);
            appInfo.setUserId(userId);

            boolean is64bit;
            if ("app".equals(installType)) {
                is64bit = AbiUtils.is64BitAbi(appInfo.getApplicationInfo());
            } else {
                is64bit = AbiUtils.is64BitAbi2(appInfo.getApplicationInfo());
            }
            if (!is64bit) {
                if (InstallExtUtils.handlerInstall(getActivity())) {
                    return;
                }
            }

            boolean isInstallSuccess;
            VAppInfo vAppInfo = VAppInfo.makeFromSys(packageName, userId);
            if ("app".equals(installType)) {
                vAppInfo.appMode = VAppInfo.MODE_APP_FROM_SYSTEM;
                vAppInfo.is64bit = is64bit;
            } else {
                vAppInfo.appMode = VAppInfo.MODE_APP_FROM_FILE;
                vAppInfo.is64bit = is64bit;

                File file = new File(appInfo.getApkPath());
                Uri uri;
                if (appInfo.getApkPath().startsWith("/data/app/")) {
                    vAppInfo.path = Uri.parse("package:" + packageName).toString();
                } else {
                    uri = FileProvider.getUriForFile(MainApp.getApp(), VCore.getConfig().getHostPackageName().concat(".provider"), file);
                    if (AbiUtils.isRunExt(vAppInfo)) {
                        MainApp.getApp().grantUriPermission(VCore.getConfig().getExtPackageName(), uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    }
                    vAppInfo.path = uri.toString();
                }
            }
            isInstallSuccess = VCore.get().installPackage(getActivity(), vAppInfo);

            if (isInstallSuccess) {
                DbManager.getInstance().addApp(
                        appInfo.getPackageName(),
                        userId,
                        appInfo.getAppName(),
                        appInfo.getAppIcon(),
                        appInfo.getInstallType(),
                        appInfo.getApkPath(),
                        is64bit
                );
                if ("app_hide".equals(type)) {
                    new CommonDialog.Builder(getActivity())
                            .setTitle("提示")
                            .setMessage("想要完全隐藏还需要卸载应用，是否现在卸载？")
                            .setNegativeButton(R.string.cancel)
                            .setPositiveButton(R.string.ensure)
                            .setCallback(new CommonDialog.Callback() {
                                @Override
                                public void onOk() {
                                    ApkUtils.uninstall(getActivity(), appInfo.getPackageName(), ApkUtils.UNINSTALL_REQUEST_CODE);
                                }
                            }).show();
                }
            } else {
                new CommonDialog.Builder(getActivity())
                        .setTitle("安装失败")
                        .setMessage("稍候重试一下")
                        .setNegativeButton(R.string.cancel)
                        .setPositiveButton(R.string.ensure)
                        .setCallback(new CommonDialog.Callback()).show();
            }
        }
    }

    private int getUserId(String packageName) {
        return DbManager.getInstance().getNextUserId(packageName);
    }

    private int lastItemDrag;

    @NotNull
    private OnItemDragListener getOnItemDragListener() {
        return new OnItemDragListener() {
            @Override
            public void onItemDragStart(RecyclerView.ViewHolder viewHolder, int pos) {
                lastItemDrag = pos;
            }

            @Override
            public void onItemDragMoving(RecyclerView.ViewHolder source, int from, RecyclerView.ViewHolder target, int to) {

            }

            @Override
            public void onItemDragEnd(RecyclerView.ViewHolder viewHolder, int pos) {
                if (lastItemDrag != pos) {
                    List<DockerBean> data = installedAppAdapter.getData();
                    new Thread(() -> DbManager.getInstance().updateOrder(data)).start();
                }
            }
        };
    }

}