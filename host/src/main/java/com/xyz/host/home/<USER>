package com.xyz.host.home;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.viewpager.widget.ViewPager;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdDislike;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.google.android.material.tabs.TabLayout;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.R;
import com.xyz.host.ads.TTAdManagerHolder;
import com.xyz.host.ads.TtFullScreenAd;
import com.xyz.host.home.adpater.ViewPagerAdapter;
import com.xyz.host.model.MapSdkConfig;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.AMapUtils;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.HomeUtils;
import com.xyz.host.utils.UIUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

import jonathanfinerty.once.Once;

public class HomeActivity extends AppCompatActivity implements View.OnClickListener {
    private final static String TAG = "HomeActivity";
    private TTNativeExpressAd mBannerAd;
    private FrameLayout mBannerContainer;

    public static void go(Context context) {
        Intent intent = new Intent(context, HomeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    protected Toolbar toolbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);
        MapSdkConfig.init();
        AMapUtils.setPkg();
        init();

        HomeUtils.tryShowTip(this);
        if (!UserAgent.getInstance().isVipUser()) {
            loadBannerAd();
            // 1个小时只展示一次
            if (!Once.beenDone(TimeUnit.HOURS, 1, "home_full_ad")) {
                String codeId = UMRemoteConfig.getInstance().getConfigValue("home_full_ads_id");
                new TtFullScreenAd().load(this, codeId);
                Once.markDone("home_full_ad");
            }
        }
        UserAgent.getInstance().update();
    }

    private void loadBannerAd() {
        String codeId = UMRemoteConfig.getInstance().getConfigValue("home_banner_ads_id");
        if (TextUtils.isEmpty(codeId)) {
            return;
        }
        mBannerContainer = findViewById(R.id.adContainer);
        TTAdNative mAdNativeLoader = TTAdManagerHolder.get().createAdNative(this);
        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(codeId)
                .setAdCount(1)
                .setExpressViewAcceptedSize(UIUtils.getScreenWidthDp(HomeActivity.this), 75)
                .build();
        mAdNativeLoader.loadBannerExpressAd(adSlot, new TTAdNative.NativeExpressAdListener() {
            @Override
            public void onError(int i, String s) {
                // android.util.Log.e("MyTest", "loadBanner:" + i + "," + s);
                mBannerContainer.removeAllViews();
            }

            @Override
            public void onNativeExpressAdLoad(List<TTNativeExpressAd> list) {
                if (list != null && !list.isEmpty()) {
                    mBannerAd = list.get(0);
                    mBannerAd.setSlideIntervalTime(30 * 1000);
                    showBannerAd();
                }
            }
        });
    }

    private void showBannerAd() {
        if (mBannerAd != null) {
            mBannerAd.setExpressInteractionListener(new TTNativeExpressAd.ExpressAdInteractionListener() {
                @Override
                public void onAdClicked(View view, int i) {

                }

                @Override
                public void onAdShow(View view, int i) {

                }

                @Override
                public void onRenderFail(View view, String s, int i) {

                }

                @Override
                public void onRenderSuccess(View view, float v, float v1) {

                }
            });
            mBannerAd.setDislikeCallback(this, new TTAdDislike.DislikeInteractionCallback() {
                @Override
                public void onShow() {
                }

                @Override
                public void onSelected(int i, String s, boolean b) {
                    mBannerContainer.removeAllViews();
                }

                @Override
                public void onCancel() {
                }
            });
            View bannerView = mBannerAd.getExpressAdView();
            if (bannerView != null) {
                mBannerContainer.removeAllViews();
                mBannerContainer.addView(bannerView);
            }
        }
    }

    private void init() {
        Toolbar mToolBar = findViewById(R.id.toolBar);
        setSupportActionBar(mToolBar);

        TextView tvVersion = findViewById(R.id.tv_version);
        tvVersion.setText(CommonUtils.getVersionName(this));

        ViewPagerAdapter homeViewpagerAdapter = new ViewPagerAdapter(getSupportFragmentManager());
        homeViewpagerAdapter.addFragment(LaunchFragment.newInstance());
        homeViewpagerAdapter.addTab(getString(R.string.tab_index));
        homeViewpagerAdapter.addFragment(MyFragment.newInstance());
        homeViewpagerAdapter.addTab(getString(R.string.tab_me));

        TabLayout tabLayout = findViewById(R.id.tabLayout);
        ViewPager viewPager = findViewById(R.id.viewPager);
        tabLayout.setupWithViewPager(viewPager);
        viewPager.setAdapter(homeViewpagerAdapter);
        viewPager.setCurrentItem(0);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {

            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });


        tabLayout.getTabAt(0).setCustomView(getTabView(R.drawable.ic_tab_index));
        tabLayout.getTabAt(1).setCustomView(getTabView(R.drawable.ic_tab_me));
    }

    private View getTabView(int iconResId) {
        View view =
                LayoutInflater.from(this).inflate(R.layout.tab_item_view, null);
        ImageView imageView = view.findViewById(R.id.imageView);
        imageView.setImageResource(iconResId);
        return view;
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.toolbar_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.menu_about) {
            showAbout();
            return true;
        } else if (id == R.id.menu_multiapp) {
            // 启动多开应用界面
            try {
                Class<?> multiAppClass = Class.forName("com.yingyu.zh.multiapp.MultiAppActivity");
                Intent intent = new Intent(this, multiAppClass);
                startActivity(intent);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void showAbout() {
        startActivity(new Intent(this, AboutActivity.class));
    }

}