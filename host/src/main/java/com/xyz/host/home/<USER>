package com.xyz.host.home;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.fun.vbox.client.ipc.VirtualLocationManager;
import com.fun.vbox.entity.VAppInfo;
import com.fun.vbox.remote.vloc.VLocation;
import com.fun.vbox.utils.PermissionCompat;
import com.gw.swipeback.SuperSwipeBackLayout;
import com.gw.swipeback.SwipeBackLayout;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.R;
import com.xyz.host.home.adpater.AppPluginAdapter;
import com.xyz.host.home.adpater.decorations.ItemOffsetDecoration;
import com.xyz.host.home.base.BaseActivity;
import com.xyz.host.model.PluginInfo;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.AppUtils;
import com.xyz.host.utils.HomeUtils;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.utils.UIUtils;

public class AppDetailActivity extends BaseActivity implements View.OnClickListener {
    private AppPluginAdapter mAdapter;
    private VAppInfo mAppInfo;
    private String mAppName;
    private GridLayoutManager mLayoutManager;
    private SuperSwipeBackLayout mSwipeBackLayout;

    public static void go(Context context, VAppInfo appInfo, String name) {
        Intent intent = new Intent(context, AppDetailActivity.class);
        intent.putExtra("appInfo", appInfo);
        intent.putExtra("appName", name);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.slide_bottom_in, R.anim.slide_bottom_out);

        // 设置沉浸式状态栏
        setupImmersiveStatusBar();

        setContentView(R.layout.layout_app_detail);

        int heightPixels = getResources()
                .getDisplayMetrics().heightPixels;

        Window win = this.getWindow();
        WindowManager.LayoutParams lp = win.getAttributes();
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = (int) (heightPixels * 0.92);
        lp.dimAmount = 0.8f;
        lp.gravity = Gravity.BOTTOM;
        win.setAttributes(lp);

        Intent intent = getIntent();
        mAppInfo = intent.getParcelableExtra("appInfo");
        mAppName = intent.getStringExtra("appName");

        initViews();
        initData();

        if (!UserAgent.getInstance().isVipUser()) {
            closeLocation();
        }

        if (mAppInfo.userId != 0) {
            if (!UserAgent.getInstance().isVipUser()) {
                VipPaymentHelper.showVipPaymentDialog(this);
                closeLocation();
                finish();
            }
        }

        mSwipeBackLayout = new SuperSwipeBackLayout(this);
        mSwipeBackLayout.attachToActivity(this);
        mSwipeBackLayout.setDirectionMode(SwipeBackLayout.FROM_TOP);
        mSwipeBackLayout.setMaskAlpha(100);

        HomeUtils.tryShowDetailTip(AppDetailActivity.this);
        StatAgent.onEvent(this, "app_more", "name", mAppName);
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.purple_700));

        // 设置状态栏文字为白色
        window.getDecorView().setSystemUiVisibility(0);
    }

    private void initViews() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(v -> finish());

        // 设置标题
        TextView titleView = findViewById(R.id.tv_title);
        if (titleView != null && mAppName != null) {
            titleView.setText(mAppName);
        }

        RecyclerView recyclerView = findViewById(R.id.plugin_recycler_view);
        mLayoutManager = new GridLayoutManager(this, 1);
        recyclerView
                .setLayoutManager(mLayoutManager);
        recyclerView.addItemDecoration(new ItemOffsetDecoration(UIUtils.dp2px(getContext(), 2)));
        mAdapter = new AppPluginAdapter(this, mAppInfo.userId, mAppInfo.packageName);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((info, position) -> {
            if (!UserAgent.getInstance().isVipUser()) {
                VipPaymentHelper.showVipPaymentDialog(this);
                closeLocation();
            } else if (PluginInfo.TYPE_POS == info.type) {
                StatAgent.onEvent(this, "plugin_click", "name", "pos");
                gotoLocationSetting();
            } else if (PluginInfo.TYPE_DEVICE == info.type) {
                SetDeviceActivity.open(AppDetailActivity.this, mAppInfo);
            }
        });
        mAdapter.setOnItemSwitchListener((pluginInfo, isChecked) -> {
            if (isChecked) {
                if (!UserAgent.getInstance().isVipUser()) {
                    VipPaymentHelper.showVipPaymentDialog(this);
                } else {
                    openLocation();
                }
            } else {
                closeLocation();
            }
        });
    }

    private void initData() {
        initPluginList();
    }

    private void gotoLocationSetting() {
        if (!PermissionCompat.checkPermissions(
                new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION},
                false)) {
            PermissionCompat.startRequestPermissions(this, false,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                            Manifest.permission.ACCESS_COARSE_LOCATION},
                    (requestCode, permissions, grantResults) -> {
                        boolean result = PermissionCompat.isRequestGranted(grantResults);
                        if (result) {
                            new Handler(getMainLooper()).post(this::goChooseLocationActivity);
                        }
                        return result;
                    });
        } else {
            goChooseLocationActivity();
        }
    }

    private void goChooseLocationActivity() {
        Intent intent = new Intent(this, SetLocationActivity.class);
        intent.putExtra("appInfo", mAppInfo);
        startActivity(intent);
    }

    private void closeLocation() {
        try {
            VirtualLocationManager.get().setMode(mAppInfo.userId, mAppInfo.packageName, VirtualLocationManager.MODE_CLOSE);
        } catch (Exception e) {
            // Ignored.
        }
    }

    private void openLocation() {
        if (!UserAgent.getInstance().isVipUser()) {
            return;
        }
        try {
            VirtualLocationManager.get()
                    .setMode(mAppInfo.userId, mAppInfo.packageName, VirtualLocationManager.MODE_USE_SELF);
        } catch (Exception e) {
            // Ignored.
        }
    }

    private void initPluginList() {
        mAdapter.clear();
        String strConfig;
        boolean isEnableVirtualPos = AppUtils.showVirtualLocate(getContext(), mAppInfo.packageName);
        strConfig = UMRemoteConfig.getInstance().getConfigValue(
                "mock_phone");
        boolean isEnableMockPhone = !TextUtils.isEmpty(strConfig) && strConfig.equals("1");
        strConfig = UMRemoteConfig.getInstance().getConfigValue(
                "mock_camera");
        boolean isEnableMockCamera = !TextUtils.isEmpty(strConfig) && strConfig.equals("1");

        if (isEnableVirtualPos) {
            PluginInfo info =
                    new PluginInfo(mAppInfo.packageName, PluginInfo.TYPE_POS, R.drawable.ic_vip_position,
                            R.color.white,
                            getString(R.string.plugin_location),
                            getString(R.string.plugin_action_location));
            info.showSwitch = true;
            mAdapter.add(info);
        }

        if (isEnableVirtualPos) {
            PluginInfo posHook = new PluginInfo(mAppInfo.packageName, PluginInfo.TYPE_POS2,
                    R.drawable.ic_strong, R.color.white,
                    getString(R.string.plugin_location2),
                    getString(R.string.plugin_action_pos));
            posHook.showSwitch = true;
            mAdapter.add(posHook);
        }

        if (isEnableMockCamera) {
            PluginInfo pluginCamera = new PluginInfo(mAppInfo.packageName, PluginInfo.TYPE_CAMERA,
                    R.drawable.ic_camera,
                    R.color.white,
                    getString(R.string.plugin_camera),
                    getString(R.string.plugin_camera));
            pluginCamera.showSwitch = true;
            mAdapter.add(pluginCamera);
        }

        if (isEnableMockPhone) {
            mAdapter.add(new PluginInfo(mAppInfo.packageName, PluginInfo.TYPE_DEVICE, R.drawable.ic_vip_phone,
                    R.color.white,
                    getString(R.string.menu_mock_phone),
                    getString(R.string.plugin_action_setting)));
        }
    }


    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {

    }

    @Override
    protected void onResume() {
        super.onResume();

        try {
            PluginInfo pluginInfo = mAdapter.getPosPluginInfo();
            if (pluginInfo != null) {
                VLocation location = VirtualLocationManager.get().getLocation(mAppInfo.userId, mAppInfo.packageName);
                if (location != null && !TextUtils.isEmpty(location.address)) {
                    pluginInfo.desc = location.address;
                    mAdapter.notifyItemChanged(0);
                }
            }
        } catch (Throwable e) {
            // Ignored.
        }
    }
}
