package com.xyz.host.home;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Paint;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.URLUtil;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.home.base.BaseActivity;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.DialogUtils;

import java.io.File;
import java.util.HashMap;

public class WebActivity extends BaseActivity {
    public static final String NEW_VERSION_URL = "https://wj.aoqi360.com/yingyu_share.html";
    public static final String PRIVACY_URL = "https://wj.aoqi360.com/yingyu_privacy.html";

    public static void go(Context context, String url) {
        go(context, url, "");
    }

    public static void go(Context context, String url, String title) {
        Intent intent = new Intent(context, WebActivity.class);
        if (!(context instanceof Activity)) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.putExtra("URL", url);
        if (!TextUtils.isEmpty(title)) {
            intent.putExtra("TITLE", title);
        }
        context.startActivity(intent);
    }

    private WebView mWebView;
    private Dialog mProgress;
    private ValueCallback<Uri[]> mFilePathCallback;
    private DownloadCompleteReceiver mReceiver;
    private String mUrl;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_webview);

        Toolbar toolbar = findViewById(R.id.toolBar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        toolbar.setNavigationOnClickListener(v -> finish());

        Intent intent = getIntent();
        mUrl = NEW_VERSION_URL;
        if (intent != null) {
            mUrl = intent.getStringExtra("URL");
            if (intent.hasExtra("TITLE")) {
                String title = intent.getStringExtra("TITLE");
                if (!TextUtils.isEmpty(title)) {
                    toolbar.setTitle(title);
                }
            }
        }

        try {
            mWebView = new WebView(this);
            FrameLayout layout = findViewById(R.id.web_container);
            layout.addView(mWebView);
            initWebView(mWebView);
        } catch (Throwable e) {
            finish();
            Uri uri = Uri.parse(mUrl);
            Intent viewIntent = new Intent(Intent.ACTION_VIEW, uri);
            startActivity(viewIntent);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReceiver != null) {
            unregisterReceiver(mReceiver);
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void initWebView(WebView webView) {
        webView.setLayerType(View.LAYER_TYPE_HARDWARE, new Paint());
        webView.setBackgroundColor(Color.TRANSPARENT);
        final WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        String appCachePath = getApplicationContext().getCacheDir().getAbsolutePath();
        webSettings.setAppCachePath(appCachePath);
        webSettings.setAppCacheEnabled(true);
        webSettings.setCacheMode((NEW_VERSION_URL.equals(mUrl) ? WebSettings.LOAD_NO_CACHE :
                WebSettings.LOAD_DEFAULT));
        webSettings.setBlockNetworkImage(true);
        webSettings.setLoadsImagesAutomatically(false);
        String ua = webSettings.getUserAgentString();
        String appUa = ua + " yingyu/" + CommonUtils.getVerCode();
        webSettings.setUserAgentString(appUa);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (url == null) {
                    return false;
                }
                if (url.startsWith("http://")
                        || url.startsWith("https://")) {
                    WebSettings webSettings = view.getSettings();
                    if (url.contains(NEW_VERSION_URL)) {
                        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
                    } else {
                        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
                    }
                    view.loadUrl(url);
                    return true;
                }
                try {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    view.getContext().startActivity(intent);
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                webSettings.setBlockNetworkImage(false);
                if (!webSettings.getLoadsImagesAutomatically()) {
                    webSettings.setLoadsImagesAutomatically(true);
                }
                super.onPageFinished(view, url);
            }
        });

        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback,
                                             FileChooserParams fileChooserParams) {
                mFilePathCallback = filePathCallback;
                chooseFile();
                return true;
            }
        });

        mWebView.setDownloadListener((url, userAgent, contentDisposition, mimeType, contentLength) -> {
            boolean isDownloadBySystem = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("h5_down_system"));
            if (isDownloadBySystem) {
                downloadBySystem(url, contentDisposition, mimeType);
            } else {
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.addCategory(Intent.CATEGORY_BROWSABLE);
                intent.setData(Uri.parse(url));
                startActivity(intent);
            }
        });

        webView.loadUrl(mUrl);

        mReceiver = new DownloadCompleteReceiver();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(DownloadManager.ACTION_DOWNLOAD_COMPLETE);
        registerReceiver(mReceiver, intentFilter);
    }

    private final HashMap<Long, File> mDownloadUrlMap = new HashMap<>();
    @SuppressWarnings("unused")
    private void downloadBySystem(String url, String contentDisposition, String mimeType) {
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.allowScanningByMediaScanner();
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);

        String fileName  = URLUtil.guessFileName(url, contentDisposition, mimeType);
        File file = new File(CommonUtils.getDownloadDir(), fileName);
        if (file.exists()) {
            boolean isDeleted = file.delete();
        }
        Uri uri = Uri.fromFile(file);
        request.setDestinationUri(uri);
        final DownloadManager downloadManager = (DownloadManager) getSystemService(DOWNLOAD_SERVICE);
        long downloadId = downloadManager.enqueue(request);
        mDownloadUrlMap.put(downloadId, file);

        if (mProgress == null) {
            mProgress = DialogUtils.createLoadingDialog(this, "请稍等...", true,
                    0);
        }
        mProgress.show();
    }

    private class DownloadCompleteReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                if (DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(intent.getAction())) {
                    CommonUtils.dialogDis(mProgress);

                    long downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
                    DownloadManager downloadManager = (DownloadManager) context.getSystemService(DOWNLOAD_SERVICE);
                    String type = downloadManager.getMimeTypeForDownloadedFile(downloadId);
                    if (TextUtils.isEmpty(type)) {
                        type = "*/*";
                    }
                    File file = mDownloadUrlMap.get(downloadId);
                    if (file == null) {
                        return;
                    }
                    Context appContext = MainApp.getApp();
                    try {
                        Intent install = new Intent(Intent.ACTION_VIEW);
                        install.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                            Uri contentUri = FileProvider.getUriForFile(
                                    appContext,
                                    appContext.getPackageName() + ".provider",
                                    file
                            );
                            install.setDataAndType(contentUri, type);
                        } else {
                            install.setDataAndType(Uri.fromFile(file), type);
                        }
                        appContext.startActivity(install);
                    } catch (Throwable e) {
                        // Ignored.
                    }
                }
            }
        }
    }

    private void chooseFile() {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                ||ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{
                    Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE}, 0);
        } else {
            chooseFileInner();
        }
    }

    private void chooseFileInner() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(intent, 100);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int result : grantResults) {
            if (result == PackageManager.PERMISSION_GRANTED) {
                chooseFileInner();
                break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mWebView != null && mWebView.canGoBack()) {
                mWebView.goBack();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100) {
            if (resultCode == RESULT_OK && null != data) {
                Uri selectedImage = data.getData();
                mFilePathCallback.onReceiveValue(new Uri[]{selectedImage});
            } else {
                //没有选择图片,返回空,不返回的话,点击过一次以后,无法再启动相册
                mFilePathCallback.onReceiveValue(new Uri[]{});
            }
        }
    }
}
