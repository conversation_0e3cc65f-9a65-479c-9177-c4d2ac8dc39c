package com.xyz.host.home;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.CSJAdError;
import com.bytedance.sdk.openadsdk.CSJSplashAd;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.fun.vbox.client.core.VCore;
import com.fun.vbox.client.ipc.VirtualLocationManager;
import com.fun.vbox.entity.VAppInfo;
import com.jaeger.library.StatusBarUtil;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.ads.RewardHandler;
import com.xyz.host.ads.TTAdManagerHolder;
import com.xyz.host.db.DbManager;
import com.xyz.host.db.DockerBean;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.utils.UIUtils;
import com.yingyu.zh.multiapp.AdIdByReviewMode;

import java.util.HashMap;
import java.util.Map;

public class ShortcutHandleActivity extends AppCompatActivity {
    private FrameLayout mSplashContainer;
    private ImageView mImageLogo;
    private TTAdNative mTTAdNative;
    private boolean mIsLaunched;
    private static final int AD_TIME_OUT = 3000;

    public static void go(Activity activity, DockerBean dockerBean) {
        Intent intent = new Intent(activity, ShortcutHandleActivity.class);
        intent.putExtra("_VBOX_|_info_", dockerBean.getAppInfo());
        activity.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        StatusBarUtil.setTranslucent(this);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shortcut);
        Intent intent = getIntent();
        if (intent == null) {
            finish();
            return;
        }

        mImageLogo = findViewById(R.id.iv_logo);
        mSplashContainer = findViewById(R.id.splash_container);
        mSplashContainer.setBackgroundColor(ContextCompat.getColor(this, R.color.black2));

        CommonUtils.checkProxySet(() -> {
            Toast.makeText(ShortcutHandleActivity.this, "继续使用需要关闭WIFI代理", Toast.LENGTH_SHORT).show();
            finish();
        });

        VAppInfo appInfo = null;
        if (intent.hasExtra("_VBOX_|_info_")) {
            appInfo = intent.getParcelableExtra("_VBOX_|_info_");
        } else {
            String pkg = intent.getStringExtra("_VBOX_|_pkg_");
            int userId = intent.getIntExtra("_VBOX_|_user_id_", 0);
            int appMode = intent.getIntExtra("_VBOX_|_mode_", VAppInfo.MODE_APP_FROM_SYSTEM);
            String path = intent.getStringExtra("_VBOX_|_path_");
            int is64bit = intent.getIntExtra("_VBOX_|_64bit_", 0);
            appInfo = new VAppInfo(pkg, path, userId, appMode, is64bit == 1);
        }

        final Map<String, Object> params = new HashMap<>();
        if (appInfo != null) {
            params.put("pkg", appInfo.packageName);
        }

        try {
            ApplicationInfo applicationInfo =
                    MainApp.getApp().getPackageManager().getApplicationInfo(appInfo.packageName, 0);
            PackageManager pm = MainApp.getApp().getPackageManager();
            Drawable drawable = applicationInfo.loadIcon(pm);
            mImageLogo.setImageDrawable(drawable);
            CharSequence appName = applicationInfo.loadLabel(pm);
            params.put("name", appName.toString());
        } catch (PackageManager.NameNotFoundException e) {
            // Ignored.
        }

        if (!UserAgent.getInstance().isVipUser()) {
            loadSplashAd();
            VirtualLocationManager.get().setMode(
                    appInfo.userId, appInfo.packageName, VirtualLocationManager.MODE_CLOSE);
        }

        VAppInfo finalAppInfo = appInfo;
        mImageLogo.post(new Runnable() {
            @Override
            public void run() {
                if (!UserAgent.getInstance().isVipUser()) {
                    RewardHandler mRewardHandler = new RewardHandler();
                    mRewardHandler.init();

                    String value = UMRemoteConfig.getInstance().getConfigValue("free_app_count");
                    int freeAppCount = 1;
                    try {
                        freeAppCount = Integer.parseInt(value);
                    } catch (Throwable e) {
                        // Ignored.
                    }
                    int allCount = 1;
                    try {
                        allCount = DbManager.getInstance().getAppAll().size();
                    } catch (Throwable e) {
                        // Ignored.
                    }
                    if (allCount > freeAppCount || finalAppInfo.userId != 0) {
                        if (mRewardHandler.handleReward(ShortcutHandleActivity.this, new Runnable() {
                            @Override
                            public void run() {
                                if (mIsLaunched) {
                                    return;
                                }
                                StatAgent.onEvent(ShortcutHandleActivity.this, "desk_launch", params);
                                VCore.get().launchApp(ShortcutHandleActivity.this, finalAppInfo, null);
                                mIsLaunched = true;
                            }
                        })) {
                            return;
                        }
                    }
                }
                StatAgent.onEvent(ShortcutHandleActivity.this, "desk_launch", params);
                VCore.get().launchApp(ShortcutHandleActivity.this, finalAppInfo, null);
                mIsLaunched = true;
            }
        });
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mIsLaunched) {
            mImageLogo.postDelayed(new Runnable() {
                @Override
                public void run() {
                    finish();
                }
            }, 2000);
        }
    }

    /**
     * 加载开屏广告
     */
    private void loadSplashAd() {
        AdSlot adSlot = null;
        String adID = AdIdByReviewMode.getAdId("launch_ads_id");
        if (TextUtils.isEmpty(adID)) {
            return;
        }

        DisplayMetrics dm = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getRealMetrics(dm);
        float splashWidthDp = UIUtils.getScreenWidthDp(this);
        int splashWidthPx = UIUtils.getScreenWidthInPx(this);
        int screenHeightPx = UIUtils.getScreenHeight(this);
        float screenHeightDp = UIUtils.px2dip(this, screenHeightPx);
        float splashHeightDp;
        int splashHeightPx;

        splashHeightDp = screenHeightDp;
        splashHeightPx = screenHeightPx;

        try {
            mTTAdNative = TTAdManagerHolder.get().createAdNative(this);
            adSlot = new AdSlot.Builder()
                    .setCodeId(adID)
                    .setExpressViewAcceptedSize(splashWidthDp, splashHeightDp) // 单位是dp
                    .setImageAcceptedSize(splashWidthPx, splashHeightPx) // 单位是px
                    .build();
        } catch (Exception e) {
            mTTAdNative = null;
            adSlot = null;
        }

        if (mTTAdNative == null || adSlot == null) {
            return;
        }
        //step4:请求广告，调用开屏广告异步请求接口，对请求回调的广告作渲染处理
        mTTAdNative.loadSplashAd(adSlot, new TTAdNative.CSJSplashAdListener() {
            @Override
            public void onSplashLoadSuccess(CSJSplashAd csjSplashAd) {

            }

            @Override
            public void onSplashLoadFail(CSJAdError csjAdError) {
                // Log.d("MyTest", "onSplashLoadFail:" + csjAdError.getCode() + "," + csjAdError.getMsg());
            }

            @Override
            public void onSplashRenderSuccess(CSJSplashAd ad) {
                if (ad == null) {
                    return;
                }

                try {
                    //获取SplashView
                    View view = ad.getSplashView();
                    if (view != null && mSplashContainer != null && !ShortcutHandleActivity.this.isFinishing()) {
                        mImageLogo.setVisibility(View.GONE);
                        mSplashContainer.setVisibility(View.VISIBLE);
                        mSplashContainer.removeAllViews();
                        //把SplashView 添加到ViewGroup中,注意开屏广告view：width >=70%屏幕宽；height >=50%屏幕高
                        mSplashContainer.addView(view);
                    } else {
                        mImageLogo.setVisibility(View.VISIBLE);
                        mSplashContainer.setVisibility(View.GONE);
                    }
                } catch (Exception e) {
                    // Ignored.
                }
            }

            @Override
            public void onSplashRenderFail(CSJSplashAd csjSplashAd, CSJAdError csjAdError) {

            }
        }, AD_TIME_OUT);
    }
}