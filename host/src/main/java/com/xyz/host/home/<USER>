package com.xyz.host.home;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.umeng.analytics.MobclickAgent;
import com.xyz.host.R;
import com.xyz.host.user.engine.UserAgent;

/**
 * 挽留弹窗 - 根据用户登录状态显示不同内容
 * 未登录：提醒注册送12小时VIP体验
 * 已登录：提醒邀请好友获得体验券
 */
public class RetentionDialogFragment extends BottomSheetDialogFragment implements View.OnClickListener {
    
    private static final String TAG = "RetentionDialog";
    
    // UI组件
    private ImageView ivIcon;
    private TextView tvTitle;
    private TextView tvDescription;
    private TextView btnPrimary;
    private TextView btnCancel;
    
    // 登录状态
    private boolean isUserLoggedIn;
    
    public static RetentionDialogFragment newInstance() {
        return new RetentionDialogFragment();
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_retention, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupContent();
        
        // 统计弹窗显示
        MobclickAgent.onEvent(getContext(), "retention_dialog_show");
    }
    
    private void initViews(View view) {
        // 关闭按钮
        view.findViewById(R.id.btn_close).setOnClickListener(v -> {
            MobclickAgent.onEvent(getContext(), "retention_dialog_close");
            dismiss();
        });
        
        // 内容组件
        ivIcon = view.findViewById(R.id.iv_icon);
        tvTitle = view.findViewById(R.id.tv_title);
        tvDescription = view.findViewById(R.id.tv_description);
        btnPrimary = view.findViewById(R.id.btn_primary);
        btnCancel = view.findViewById(R.id.btn_cancel);
        
        // 按钮点击事件
        btnPrimary.setOnClickListener(this);
        btnCancel.setOnClickListener(this);
    }
    
    private void setupContent() {
        // 检查用户登录状态
        isUserLoggedIn = UserAgent.getInstance().isLogin();
        
        if (isUserLoggedIn) {
            // 已登录用户 - 邀请好友
            setupLoggedInContent();
        } else {
            // 未登录用户 - 注册福利
            setupNotLoggedInContent();
        }
    }
    
    /**
     * 设置未登录用户的内容
     */
    private void setupNotLoggedInContent() {
        tvTitle.setText("新用户福利");
        tvDescription.setText("注册即送12小时VIP体验\n立即享受无限多开等特权功能");
        btnPrimary.setText("立即注册");
        btnCancel.setText("稍后再说");
        
        // 可以使用不同的图标
        ivIcon.setImageResource(R.drawable.ic_invite_redeem);
    }
    
    /**
     * 设置已登录用户的内容
     */
    private void setupLoggedInContent() {
        tvTitle.setText("邀请好友获得奖励");
        tvDescription.setText("每邀请1人成功注册\n双方各得1天VIP体验券");
        btnPrimary.setText("立即邀请");
        btnCancel.setText("稍后再说");
        
        // 使用邀请相关图标
        ivIcon.setImageResource(R.drawable.ic_invite_redeem);
    }
    
    // 登录结果处理
    private final ActivityResultLauncher<Intent> mLoginLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (UserAgent.getInstance().isLogin()) {
                        // 登录成功，可以选择是否自动跳转到邀请页面
                        MobclickAgent.onEvent(getContext(), "retention_login_success");
                        // 关闭当前弹窗
                        dismiss();
                        // 可以选择是否自动打开邀请页面
                        // openInvitePage();
                    }
                }
            });
    
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            handlePrimaryAction();
        } else if (v.getId() == R.id.btn_cancel) {
            handleCancelAction();
        }
    }
    
    /**
     * 处理主要操作按钮点击
     */
    private void handlePrimaryAction() {
        if (isUserLoggedIn) {
            // 已登录 - 跳转到邀请页面
            MobclickAgent.onEvent(getContext(), "retention_invite_click");
            openInvitePage();
        } else {
            // 未登录 - 跳转到登录页面
            MobclickAgent.onEvent(getContext(), "retention_register_click");
            showLogin();
        }
    }
    
    /**
     * 处理取消按钮点击
     */
    private void handleCancelAction() {
        MobclickAgent.onEvent(getContext(), "retention_cancel_click");
        dismiss();
    }
    
    /**
     * 显示登录页面
     */
    private void showLogin() {
        if (getContext() != null) {
            Intent intent = new Intent(getContext(), LoginActivity.class);
            mLoginLauncher.launch(intent);
        }
    }
    
    /**
     * 打开邀请页面
     */
    private void openInvitePage() {
        if (getContext() != null) {
            com.yingyu.zh.multiapp.InviteRedeemActivity.start(getContext());
            // 关闭当前弹窗
            dismiss();
        }
    }
}
