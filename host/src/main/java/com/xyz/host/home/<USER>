package com.xyz.host.home;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

/**
 * 挽留弹窗帮助类
 * 提供便捷的弹窗显示方法
 */
public class RetentionDialogHelper {
    
    private static final String DIALOG_TAG = "RetentionDialog";
    
    /**
     * 显示挽留弹窗
     * @param activity 调用的Activity，必须继承自FragmentActivity
     */
    public static void showRetentionDialog(FragmentActivity activity) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        
        // 检查是否已经有弹窗在显示
        RetentionDialogFragment existingDialog = 
            (RetentionDialogFragment) fragmentManager.findFragmentByTag(DIALOG_TAG);
        
        if (existingDialog != null && existingDialog.isAdded()) {
            return; // 已经有弹窗在显示，不重复显示
        }
        
        // 创建并显示新的弹窗
        RetentionDialogFragment dialog = RetentionDialogFragment.newInstance();
        dialog.show(fragmentManager, DIALOG_TAG);
    }
    
    /**
     * 检查是否有挽留弹窗正在显示
     * @param activity 调用的Activity
     * @return true表示有弹窗正在显示
     */
    public static boolean isRetentionDialogShowing(FragmentActivity activity) {
        if (activity == null || activity.isFinishing()) {
            return false;
        }
        
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        RetentionDialogFragment existingDialog = 
            (RetentionDialogFragment) fragmentManager.findFragmentByTag(DIALOG_TAG);
        
        return existingDialog != null && existingDialog.isAdded();
    }
    
    /**
     * 关闭挽留弹窗（如果正在显示）
     * @param activity 调用的Activity
     */
    public static void dismissRetentionDialog(FragmentActivity activity) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        RetentionDialogFragment existingDialog = 
            (RetentionDialogFragment) fragmentManager.findFragmentByTag(DIALOG_TAG);
        
        if (existingDialog != null && existingDialog.isAdded()) {
            existingDialog.dismiss();
        }
    }
}
