package com.xyz.host.home;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

/**
 * VIP支付弹窗帮助类
 * 用于在任何Activity中快速调用VIP支付弹窗
 */
public class VipPaymentHelper {
    
    private static final String DIALOG_TAG = "VipPaymentDialog";
    
    /**
     * 显示VIP支付弹窗
     * @param activity 调用的Activity，必须继承自FragmentActivity
     */
    public static void showVipPaymentDialog(FragmentActivity activity) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        
        // 检查是否已经有弹窗在显示
        VipPaymentDialogFragment existingDialog = 
            (VipPaymentDialogFragment) fragmentManager.findFragmentByTag(DIALOG_TAG);
        
        if (existingDialog != null && existingDialog.isAdded()) {
            return; // 已经有弹窗在显示，不重复显示
        }
        
        // 创建并显示新的弹窗
        VipPaymentDialogFragment dialog = VipPaymentDialogFragment.newInstance();
        dialog.show(fragmentManager, DIALOG_TAG);
    }
    
    /**
     * 隐藏VIP支付弹窗
     * @param activity 调用的Activity
     */
    public static void hideVipPaymentDialog(FragmentActivity activity) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        
        FragmentManager fragmentManager = activity.getSupportFragmentManager();
        VipPaymentDialogFragment dialog = 
            (VipPaymentDialogFragment) fragmentManager.findFragmentByTag(DIALOG_TAG);
        
        if (dialog != null && dialog.isAdded()) {
            dialog.dismiss();
        }
    }
}
