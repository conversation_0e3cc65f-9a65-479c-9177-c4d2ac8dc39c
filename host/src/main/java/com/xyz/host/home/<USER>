package com.xyz.host.home;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;

import com.fun.vbox.client.core.VCore;
import com.fun.vbox.client.ipc.VDeviceManager;
import com.fun.vbox.entity.VAppInfo;
import com.fun.vbox.remote.VDeviceConfig;
import com.xyz.host.R;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.home.base.BaseActivity;
import com.xyz.host.home.view.EditableSpinner;
import com.xyz.host.model.BrandConstant;
import com.xyz.host.model.BrandItem;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Random;

public class SetDeviceActivity extends BaseActivity implements View.OnClickListener {
    public static void open(Activity activity, VAppInfo appInfo) {
        Intent intent = new Intent(activity, SetDeviceActivity.class);
        intent.putExtra("appInfo", appInfo);
        activity.startActivity(intent);
    }

    private VAppInfo mAppInfo;
    private VDeviceConfig mDeviceConfig;
    private TelephonyManager mTelephonyManager;
    private WifiManager mWifiManager;
    private EditText edt_androidId, edt_imei, edt_imsi, edt_mac;
    private EditText edt_name, edt_device, edt_board, edt_display, edt_id, edt_serial, edt_manufacturer, edt_fingerprint;
    private EditableSpinner edt_brand, edt_model;
    private List<String> mListBrand;
    private ArrayAdapter<String> mModelAdapter;
    private ArrayAdapter<String> mBrandAdapter;
    private LinkedHashMap<String, ArrayList<BrandItem>> mListBrandModel;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_set_device);
        Toolbar toolbar = findViewById(R.id.toolBar);
        toolbar.setTitle(R.string.device_title);
        setSupportActionBar(toolbar);
        enableBackHome();

        edt_androidId = findViewById(R.id.edt_androidId);
        edt_imei = findViewById(R.id.edt_imei);
        edt_imsi = findViewById(R.id.edt_imsi);
        edt_mac = findViewById(R.id.edt_mac);

        edt_brand = findViewById(R.id.edt_brand);
        edt_model = findViewById(R.id.edt_model);
        edt_name = findViewById(R.id.edt_name);
        edt_device = findViewById(R.id.edt_device);
        edt_board = findViewById(R.id.edt_board);
        edt_display = findViewById(R.id.edt_display);
        edt_id = findViewById(R.id.edt_id);
        edt_serial = findViewById(R.id.edt_serial);
        edt_manufacturer = findViewById(R.id.edt_manufacturer);
        edt_fingerprint = findViewById(R.id.edt_fingerprint);
        mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        mTelephonyManager = (TelephonyManager) getSystemService(TELEPHONY_SERVICE);
        if (getIntent() != null) {
            mAppInfo = getIntent().getParcelableExtra("appInfo");
        }
        setTitle("");

        mListBrand = new ArrayList<>();
        mListBrandModel = BrandConstant.get(this);
        for (LinkedHashMap.Entry<String, ArrayList<BrandItem>> entry : mListBrandModel.entrySet()) {
            String key = entry.getKey();
            if ("自定义".equals(key)) {
                continue;
            }
            mListBrand.add(key);
        }

        mDeviceConfig = VDeviceManager.get().getDeviceConfig(mAppInfo.userId);

        mBrandAdapter = new ArrayAdapter<>(this, R.layout.item_black_text);
        mBrandAdapter.add(Build.BRAND);
        mBrandAdapter.addAll(mListBrand);

        edt_brand.setAdapter(mBrandAdapter);
        edt_brand.setOnItemClickListener(position -> {
            String brand = getValue(edt_brand);
            ArrayList<BrandItem> arrayList = mListBrandModel.get(brand);
            if (arrayList != null) {
                mModelAdapter.clear();
                mModelAdapter.add(Build.MODEL);
                for (BrandItem item : arrayList) {
                    mModelAdapter.add(item.getModel());
                }
            }
            mModelAdapter.notifyDataSetChanged();
            setValue(edt_model, mModelAdapter.getItem(0), Build.MODEL);
        });

        mModelAdapter = new ArrayAdapter<>(this, R.layout.item_black_text);
        mModelAdapter.add(Build.MODEL);
        edt_model.setAdapter(mModelAdapter);

        updateConfig();

        findViewById(R.id.device_save_tv).setOnClickListener(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mAppInfo = intent.getParcelableExtra("appInfo");
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.device_menu, menu);
        return true;
    }

    private void killApp() {
        if (TextUtils.isEmpty(mAppInfo.packageName)) {

        } else {
            VCore.get().forceStop(this, mAppInfo);
        }
    }

    private void reset() {
        mDeviceConfig.clear();
        mDeviceConfig.setProp("BRAND", Build.BRAND);
        mDeviceConfig.setProp("MODEL", Build.MODEL);
        mDeviceConfig.setProp("PRODUCT", Build.PRODUCT);
        mDeviceConfig.setProp("DEVICE", Build.DEVICE);
        mDeviceConfig.setProp("BOARD", Build.BOARD);
        mDeviceConfig.setProp("DISPLAY", Build.DISPLAY);
        mDeviceConfig.setProp("ID", Build.ID);
        mDeviceConfig.setProp("MANUFACTURER", Build.MANUFACTURER);
        mDeviceConfig.setProp("FINGERPRINT", Build.FINGERPRINT);
    }

    private String getValue(EditText text) {
        return text.getText().toString().trim();
    }

    private String getValue(EditableSpinner text) {
        return text.getSelectedItem().trim();
    }

    private void setValue(EditText text, String value, String defValue) {
        if (TextUtils.isEmpty(value)) {
            text.setText(defValue);
            return;
        }
        text.setText(value);
    }

    private String setValue(EditableSpinner text, String value, String defValue) {
        String config = value;
        if (TextUtils.isEmpty(value)) {
            config = defValue;
        }
        text.setText(config);
        return config;
    }

    private void fillConfig() {
        if (mDeviceConfig == null) {
            return;
        }
        mDeviceConfig.setProp("BRAND", getValue(edt_brand));
        mDeviceConfig.setProp("MODEL", getValue(edt_model));
        mDeviceConfig.setProp("PRODUCT", getValue(edt_name));
        mDeviceConfig.setProp("DEVICE", getValue(edt_device));
        mDeviceConfig.setProp("BOARD", getValue(edt_board));
        mDeviceConfig.setProp("DISPLAY", getValue(edt_display));
        mDeviceConfig.setProp("ID", getValue(edt_id));
        mDeviceConfig.setProp("MANUFACTURER", getValue(edt_manufacturer));
        mDeviceConfig.setProp("FINGERPRINT", getValue(edt_fingerprint));

        mDeviceConfig.serial = getValue(edt_serial);
        mDeviceConfig.deviceId = getValue(edt_imei);
        mDeviceConfig.iccId = getValue(edt_imsi);
        mDeviceConfig.wifiMac = getValue(edt_mac);
        mDeviceConfig.androidId = getValue(edt_androidId);
    }

    @SuppressLint("HardwareIds")
    private void updateConfig() {
        if (mDeviceConfig == null) {
            return;
        }
        setValue(edt_brand, mDeviceConfig.getProp("BRAND"), Build.BRAND);
        setValue(edt_model, mDeviceConfig.getProp("MODEL"), Build.MODEL);
        setValue(edt_name, mDeviceConfig.getProp("PRODUCT"), Build.PRODUCT);
        setValue(edt_device, mDeviceConfig.getProp("DEVICE"), Build.DEVICE);
        setValue(edt_board, mDeviceConfig.getProp("BOARD"), Build.BOARD);
        setValue(edt_display, mDeviceConfig.getProp("DISPLAY"), Build.DISPLAY);
        setValue(edt_id, mDeviceConfig.getProp("ID"), Build.ID);
        setValue(edt_manufacturer, mDeviceConfig.getProp("MANUFACTURER"), Build.MANUFACTURER);
        setValue(edt_fingerprint, mDeviceConfig.getProp("FINGERPRINT"), Build.FINGERPRINT);

        setValue(edt_serial, mDeviceConfig.serial, Build.SERIAL);
        setValue(edt_mac, mDeviceConfig.wifiMac, getDefaultWifiMac());
        setValue(edt_androidId, mDeviceConfig.androidId, Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            if (checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.READ_PHONE_STATE}, 0);
                return;
            }
            try {
                /**
                 * {@link https://source.android.com/docs/core/connect/device-identifiers?hl=zh-cn}
                 */
                //Build.VERSION_CODES.Q 及以后，getDeviceId getSimSerialNumber 获取不到数据
                setValue(edt_imei, mDeviceConfig.deviceId, mTelephonyManager.getDeviceId());
                setValue(edt_imsi, mDeviceConfig.iccId, mTelephonyManager.getSimSerialNumber());
            } catch (Exception exception) {

            }
        }
    }

    @SuppressLint("HardwareIds")
    private String getDefaultWifiMac() {
        String[] files = {"/sys/class/net/wlan0/address", "/sys/class/net/eth0/address", "/sys/class/net/wifi/address"};
        @SuppressLint("MissingPermission")
        String mac = mWifiManager.getConnectionInfo().getMacAddress();
        if (TextUtils.isEmpty(mac)) {
            for (String file : files) {
                try {
                    mac = readFileAsString(file);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (!TextUtils.isEmpty(mac)) {
                    break;
                }
            }
        }
        return mac;
    }

    private String readFileAsString(String filePath)
            throws IOException {
        StringBuilder sb = new StringBuilder(1000);
        BufferedReader reader = new BufferedReader(new FileReader(filePath));
        char[] buf = new char[1024];
        int numRead = 0;
        while ((numRead = reader.read(buf)) != -1) {
            String readData = String.valueOf(buf, 0, numRead);
            sb.append(readData);
        }
        reader.close();
        return sb.toString().trim();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        for (int result : grantResults) {
            if (result == PackageManager.PERMISSION_GRANTED) {
                updateConfig();
                break;
            }
        }
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_random_id: {
                mDeviceConfig = VDeviceManager.get().random();
                String brand = getValue(edt_brand);
                mDeviceConfig.setProp("BRAND", brand);
                String model = getValue(edt_model);
                mDeviceConfig.setProp("MODEL", model);
                updateConfig();
            }
            break;
            case R.id.action_random: {
                randomData();
            }
            break;
            case R.id.action_reset:
                new CommonDialog.Builder(SetDeviceActivity.this)
                        .setMessage(R.string.dlg_reset_device)
                        .setPositiveButton(android.R.string.ok)
                        .setNegativeButton(android.R.string.cancel)
                        .setCallback(new CommonDialog.Callback() {
                            @Override
                            public void onOk() {
                                if (mDeviceConfig != null) {
                                    mDeviceConfig.enable = false;
                                    reset();
                                }
                                VDeviceManager.get().updateDeviceConfig(mAppInfo.userId, mDeviceConfig);
                                killApp();
                                updateConfig();
                            }
                        })
                        .show();

                break;
            default:
                return super.onOptionsItemSelected(item);
        }
        return true;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.device_save_tv) {
            saveConfig();
        }
    }

    private void saveConfig() {
        if (mDeviceConfig != null) {
            mDeviceConfig.enable = true;
        }
        fillConfig();
        updateConfig();
        VDeviceManager.get().updateDeviceConfig(mAppInfo.userId, mDeviceConfig);
        killApp();
        Toast.makeText(this, R.string.save_success, Toast.LENGTH_SHORT).show();
        finish();
    }

    private void randomData() {
        mDeviceConfig = VDeviceManager.get().random();
        int index = getRandom(1, mListBrand.size());
        String brand = mListBrand.get(index);
        mDeviceConfig.setProp("BRAND", brand);

        ArrayList<BrandItem> arrayList = mListBrandModel.get(brand);
        if (arrayList != null) {
            mModelAdapter.clear();
            for (BrandItem item : arrayList) {
                mModelAdapter.add(item.getModel());
            }
            mModelAdapter.notifyDataSetChanged();
        }

        index = getRandom(1, mModelAdapter.getCount());
        String model = mModelAdapter.getItem(index);
        mDeviceConfig.setProp("MODEL", model);

        updateConfig();
    }

    private int getRandom(int from, int to) {
        if (from < to)
            return from + new Random(System.currentTimeMillis()).nextInt(Math.abs(to - from));
        return from - new Random(System.currentTimeMillis()).nextInt(Math.abs(to - from));
    }
}
