package com.xyz.host.home;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.content.DialogInterface;
import android.view.KeyEvent;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.umeng.analytics.MobclickAgent;
import com.xyz.host.R;
import com.xyz.host.user.engine.UserAgent;

/**
 * 挽留弹窗 - 根据用户登录状态显示不同内容
 * 未登录：提醒注册送12小时VIP体验
 * 已登录：提醒邀请好友获得体验券
 */
public class RetentionDialogFragment extends BottomSheetDialogFragment implements View.OnClickListener {
    
    private static final String TAG = "RetentionDialog";
    
    // UI组件
    private ImageView ivIcon;
    private TextView tvTitle;
    private TextView tvDescription;
    private TextView btnPrimary;
    
    // 登录状态
    private boolean isUserLoggedIn;

    // 弹窗关闭回调接口
    public interface OnDismissListener {
        void onDismiss();
    }

    // 返回键处理回调接口
    public interface OnBackPressedListener {
        boolean onBackPressed(); // 返回true表示已处理，false表示继续默认行为
    }

    private OnDismissListener onDismissListener;
    private OnBackPressedListener onBackPressedListener;
    
    public static RetentionDialogFragment newInstance() {
        return new RetentionDialogFragment();
    }

    /**
     * 设置弹窗关闭监听器
     */
    public void setOnDismissListener(OnDismissListener listener) {
        this.onDismissListener = listener;
    }

    /**
     * 设置返回键处理监听器
     */
    public void setOnBackPressedListener(OnBackPressedListener listener) {
        this.onBackPressedListener = listener;
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_retention, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupContent();

        // 设置按键监听器
        if (getDialog() != null) {
            getDialog().setOnKeyListener(new DialogInterface.OnKeyListener() {
                @Override
                public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                        // 处理返回键
                        if (onBackPressedListener != null) {
                            return onBackPressedListener.onBackPressed();
                        }
                    }
                    return false;
                }
            });
        }

        // 统计弹窗显示
        MobclickAgent.onEvent(getContext(), "retention_dialog_show");
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        // 设置透明背景，避免白色直角边
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            // 设置窗口标志，确保背景透明
            dialog.getWindow().setFlags(
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            );
        }

        // 设置弹窗从底部弹出的行为
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置底部sheet的背景为透明，确保圆角效果
                bottomSheet.setBackgroundResource(android.R.color.transparent);
            }
        });
        return dialog;
    }

    private void initViews(View view) {
        // 关闭按钮
        view.findViewById(R.id.btn_close).setOnClickListener(v -> {
            MobclickAgent.onEvent(getContext(), "retention_dialog_close");
            dismiss();
        });
        
        // 内容组件
        ivIcon = view.findViewById(R.id.iv_icon);
        tvTitle = view.findViewById(R.id.tv_title);
        tvDescription = view.findViewById(R.id.tv_description);
        btnPrimary = view.findViewById(R.id.btn_primary);

        // 按钮点击事件
        btnPrimary.setOnClickListener(this);
    }
    
    private void setupContent() {
        // 检查用户登录状态
        isUserLoggedIn = UserAgent.getInstance().isLogin();
        
        if (isUserLoggedIn) {
            // 已登录用户 - 邀请好友
            setupLoggedInContent();
        } else {
            // 未登录用户 - 注册福利
            setupNotLoggedInContent();
        }
    }
    
    /**
     * 设置未登录用户的内容
     */
    private void setupNotLoggedInContent() {
        tvTitle.setText("新用户福利");
        tvDescription.setText("注册即送12小时VIP体验\n立即享受无限多开等特权功能");
        btnPrimary.setText("立即注册");

        // 可以使用不同的图标
        ivIcon.setImageResource(R.drawable.ic_invite_redeem);
    }
    
    /**
     * 设置已登录用户的内容
     */
    private void setupLoggedInContent() {
        tvTitle.setText("邀请好友获得奖励");
        tvDescription.setText("每邀请1人成功注册\n双方各得1天VIP体验券");
        btnPrimary.setText("立即邀请");

        // 使用邀请相关图标
        ivIcon.setImageResource(R.drawable.ic_invite_redeem);
    }
    
    // 登录结果处理
    private final ActivityResultLauncher<Intent> mLoginLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (UserAgent.getInstance().isLogin()) {
                        // 登录成功，可以选择是否自动跳转到邀请页面
                        MobclickAgent.onEvent(getContext(), "retention_login_success");
                        // 关闭当前弹窗
                        dismiss();
                        // 可以选择是否自动打开邀请页面
                        // openInvitePage();
                    }
                }
            });
    
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.btn_primary) {
            handlePrimaryAction();
        }
    }
    
    /**
     * 处理主要操作按钮点击
     */
    private void handlePrimaryAction() {
        if (isUserLoggedIn) {
            // 已登录 - 跳转到邀请页面
            MobclickAgent.onEvent(getContext(), "retention_invite_click");
            openInvitePage();
        } else {
            // 未登录 - 跳转到登录页面
            MobclickAgent.onEvent(getContext(), "retention_register_click");
            showLogin();
        }
    }
    

    
    /**
     * 显示登录页面
     */
    private void showLogin() {
        if (getContext() != null) {
            Intent intent = new Intent(getContext(), LoginActivity.class);
            mLoginLauncher.launch(intent);
        }
    }
    
    /**
     * 打开邀请页面
     */
    private void openInvitePage() {
        if (getContext() != null) {
            com.yingyu.zh.multiapp.InviteRedeemActivity.start(getContext());
            // 关闭当前弹窗
            dismiss();
        }
    }

    @Override
    public void onDismiss(@NonNull android.content.DialogInterface dialog) {
        super.onDismiss(dialog);
        // 通知Activity弹窗已关闭
        if (onDismissListener != null) {
            onDismissListener.onDismiss();
        }
    }

}
