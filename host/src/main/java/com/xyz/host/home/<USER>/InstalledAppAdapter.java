package com.xyz.host.home.adpater;

import android.graphics.BitmapFactory;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.module.DraggableModule;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.xyz.host.R;
import com.xyz.host.db.DockerBean;
import com.xyz.host.home.view.DownloadLoadingView;
import com.xyz.host.home.view.labelview.LabelCardView;

import java.util.List;

public class InstalledAppAdapter extends BaseQuickAdapter<DockerBean, BaseViewHolder> implements DraggableModule {
    private OnMoreClickListener mOnMoreClickListener;

    public InstalledAppAdapter(List<DockerBean> data) {
        super(R.layout.item_installed_app, data);
    }

    public void setMoreClickListener(OnMoreClickListener listener) {
        this.mOnMoreClickListener = listener;
    }

    @Override
    protected void convert(BaseViewHolder holder, DockerBean bean) {
        byte[] in = bean.getAppIcon();
        if (in != null && in.length > 0) {
            holder.setImageBitmap(R.id.iv_app_icon, BitmapFactory.decodeByteArray(in, 0, in.length));
        }
        holder.setText(R.id.tv_app_name, bean.getAppName());
        LabelCardView lcv = holder.getView(R.id.lcv_items);
        if (bean.getUserId() > 0) {
            lcv.setLabelText(bean.getUserId() + "");
        } else {
            lcv.setLabelText(null);
        }
        DownloadLoadingView loadingView = holder.getView(R.id.item_loading_view);
        if (bean.getInstallState() == 1) {
            loadingView.setVisibility(View.VISIBLE);
            loadingView.setProgress(bean.getInstallProgress());
        } else {
            loadingView.setVisibility(View.GONE);
        }
        holder.setGone(R.id.iv_isopened, bean.isOpened());
        holder.getView(R.id.item_app_more_iv).setOnClickListener(v -> {
            if (mOnMoreClickListener != null) {
                mOnMoreClickListener.onMoreClick(v, bean);
            }
        });
    }

    public interface OnMoreClickListener {
        void onMoreClick(View view, DockerBean model);
    }

    @Override
    protected void convert(BaseViewHolder holder, DockerBean item, List<?> payloads) {
        super.convert(holder, item, payloads);
        DownloadLoadingView loadingView = holder.getView(R.id.item_loading_view);
        if (payloads != null && !payloads.isEmpty()) {
            DockerBean bean = (DockerBean) payloads.get(0);
            if (bean.getInstallState() == 1) {
                loadingView.setProgress(bean.getInstallProgress());
            } else {
                loadingView.setVisibility(View.GONE);
            }
            holder.setGone(R.id.iv_isopened, bean.isOpened());
        }
    }
}
