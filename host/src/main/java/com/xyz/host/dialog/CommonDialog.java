package com.xyz.host.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.xyz.host.R;

public class CommonDialog extends AppCompatDialog implements View.OnClickListener {
   private final AlertParams mParams;

   public CommonDialog(AlertParams params) {
      this(params, R.style.VBDialogTheme);
   }

   public CommonDialog(AlertParams params, int theme) {
      super(params.mContext, theme);
      mParams = params;
      setContentView(R.layout.dlg_common);

      fullWindow();
      setCancelable(mParams.mCancelable);

      TextView title = findViewById(R.id.title);
      if (title != null) {
         if (!TextUtils.isEmpty(mParams.mTitle)) {
            title.setText(mParams.mTitle);
         }
      }
      TextView msg = findViewById(R.id.message);
      if (msg != null) {
         if (!TextUtils.isEmpty(mParams.mMessage)) {
            msg.setText(mParams.mMessage);
         }
      }

      TextView btnOk = findViewById(R.id.ok_button);
      TextView btnCancel = findViewById(R.id.cancel_button);
      View lineCancel = findViewById(R.id.cancel_line);
      CheckBox checkBox = findViewById(R.id.checkbox);

      if (checkBox != null) {
         if (mParams.mCheckCallback != null) {
            if (!TextUtils.isEmpty(mParams.mCheckText)) {
               checkBox.setText(mParams.mCheckText);
            }
            checkBox.setVisibility(View.VISIBLE);
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
               @Override
               public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                  mParams.mCheckCallback.onCheckedChanged(compoundButton, b);
               }
            });
         } else {
            checkBox.setVisibility(View.GONE);
         }
      }

      if (btnOk != null) {
         if (!TextUtils.isEmpty(mParams.mPositiveButtonText)) {
            btnOk.setText(mParams.mPositiveButtonText);
         }
         btnOk.setOnClickListener(this);
      }
      if (btnCancel != null) {
         if (!TextUtils.isEmpty(mParams.mNegativeButtonText)) {
            btnCancel.setText(mParams.mNegativeButtonText);
            btnCancel.setVisibility(View.VISIBLE);
            if (lineCancel != null) {
               lineCancel.setVisibility(View.VISIBLE);
            }
            btnCancel.setOnClickListener(this);
         } else {
            btnCancel.setVisibility(View.GONE);
            if (lineCancel != null) {
               lineCancel.setVisibility(View.GONE);
            }
         }
      }
   }

   private void fullWindow() {
      if (getWindow() != null) {
         getWindow().setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
      }
   }

   @Override
   public void onClick(View v) {
      if (v.getId() == R.id.cancel_button) {
         if (mParams != null && mParams.mCallback != null) {
            mParams.mCallback.onCancel();
         }
         if (mParams != null && mParams.mIsClickCancelNoDismiss) {
            return;
         }
         dismiss();
      } else if (v.getId() == R.id.ok_button) {
         if (mParams != null && mParams.mCallback != null) {
            mParams.mCallback.onOk();
         }
         if (mParams != null && mParams.mIsClickOkNoDismiss) {
            return;
         }
         dismiss();
      }
   }

   public static class AlertParams {
      public final Context mContext;
      public boolean mCancelable;
      public boolean mIsClickOkNoDismiss;
      public boolean mIsClickCancelNoDismiss;
      public CharSequence mTitle;
      public CharSequence mMessage;
      public CharSequence mPositiveButtonText;
      public CharSequence mNegativeButtonText;
      public CharSequence mCheckText;
      public Callback mCallback;
      public CompoundButton.OnCheckedChangeListener mCheckCallback;

      public AlertParams(Context context) {
         this.mContext = context;
      }
   }

   public static class Callback {
      public void onCancel() {

      }

      public void onOk() {

      }
   }

   public static class Builder {
      private final AlertParams mParams;

      public Builder(Context context) {
         mParams = new AlertParams(context);
      }

      public Builder setPositiveButton(CharSequence positiveButton) {
         mParams.mPositiveButtonText = positiveButton;
         return this;
      }

      public Builder setPositiveButton(int positiveButtonId) {
         mParams.mPositiveButtonText = mParams.mContext.getString(positiveButtonId);
         return this;
      }

      public Builder setNegativeButton(CharSequence negativeButton) {
         mParams.mNegativeButtonText = negativeButton;
         return this;
      }

      public Builder setNegativeButton(int resId) {
         mParams.mNegativeButtonText = mParams.mContext.getString(resId);
         return this;
      }

      public Builder setTitle(CharSequence title) {
         mParams.mTitle = title;
         return this;
      }

      public Builder setTitle(int titleId) {
         mParams.mTitle = mParams.mContext.getString(titleId);
         return this;
      }

      public Builder setMessage(CharSequence message) {
         mParams.mMessage = message;
         return this;
      }

      public Builder setMessage(int messageId) {
         mParams.mMessage = mParams.mContext.getString(messageId);
         return this;
      }

      public Builder setCancelable(boolean flag) {
         mParams.mCancelable = flag;
         return this;
      }

      public Builder setIsClickOkNoDismiss(boolean isClickOkNoDismiss){
         mParams.mIsClickOkNoDismiss = isClickOkNoDismiss;
         return this;
      }

      public Builder setIsClickCancelNoDismiss(boolean isClickCancelNoDismiss){
         mParams.mIsClickCancelNoDismiss = isClickCancelNoDismiss;
         return this;
      }

      public Builder setCallback(Callback callback) {
         mParams.mCallback = callback;
         return this;
      }

      public Builder setShowCheck(
              String message, CompoundButton.OnCheckedChangeListener callback) {
         mParams.mCheckText = message;
         mParams.mCheckCallback = callback;
         return this;
      }

      public Builder setShowCheck(CompoundButton.OnCheckedChangeListener callback) {
         mParams.mCheckCallback = callback;
         return this;
      }

      public CommonDialog show() {
         final CommonDialog dialog = new CommonDialog(mParams);
         dialog.show();
         return dialog;
      }
   }
}
