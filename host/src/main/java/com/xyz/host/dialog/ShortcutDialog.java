package com.xyz.host.dialog;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatDialog;

import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.db.DockerBean;
import com.xyz.host.utils.ShortcutUtils;

public class ShortcutDialog extends AppCompatDialog implements View.OnClickListener {
    private ImageView mCloseIv;
    private TextView mOkTv;
    private Context mContext;
    private EditText mEditText;
    private String mName;
    private DockerBean mAppInfo;
    private Bitmap mIcon;

    public ShortcutDialog(Context context, DockerBean info) {
        super(context, R.style.VBDialogTheme);
        setContentView(R.layout.layout_custom_shortcut);
        this.mContext = context;
        this.mAppInfo = info;
        this.mName = info.getAppName();
        byte[] in = info.getAppIcon();
        if (in != null && in.length > 0) {
            this.mIcon = BitmapFactory.decodeByteArray(in, 0, in.length);
        }

        initView();
        initData();

        if (getWindow() != null) {
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        setCanceledOnTouchOutside(false);
    }


    private void initData() {
        mEditText.setText(mName);
    }

    private void initView() {
        mCloseIv = findViewById(R.id.native_close_iv);
        mOkTv = findViewById(R.id.native_ok);
        mCloseIv.setOnClickListener(this);
        mOkTv.setOnClickListener(this);
        mEditText = findViewById(R.id.cut_name_et);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.native_close_iv) {
            dismiss();
        } else if (v.getId() == R.id.native_ok) {
            handleCreateShortcutInner();
        }
    }


    private void handleCreateShortcutInner() {
        ShortcutUtils.createShortcut(mAppInfo, new ShortcutUtils.OnEmitShortcutListener() {

            @Override
            public Bitmap getIcon() {
                return mIcon;
            }

            @Override
            public String getName() {
                return mEditText.getText().toString();
            }
        });
        dismiss();

        boolean showShortcutGuide = MainApp.getSharedPrefs().getBoolean("shortcut_guide", true);
        if (showShortcutGuide) {
           new ShortcutPermissionDialog(mContext).show();
        }
    }



}
