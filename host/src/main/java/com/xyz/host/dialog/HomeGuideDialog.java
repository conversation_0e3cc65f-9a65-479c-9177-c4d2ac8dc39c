package com.xyz.host.dialog;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.app.AppCompatDialog;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.xyz.host.R;
import com.xyz.host.home.view.OverlayWithHoleFrameLayout;

public class HomeGuideDialog extends AppCompatDialog implements View.OnClickListener {

    private Context mContext;
    private View.OnClickListener mListener;
    private Rect overlayHoleRect;

    public HomeGuideDialog(Context context, Rect overlayHoleRect) {
        this(context, R.style.TransparentTheme, overlayHoleRect);
    }

    public HomeGuideDialog(Context context, int theme, Rect overlayHoleRect) {
        super(context, theme);
        setContentView(R.layout.layout_home_guide);
        this.overlayHoleRect = overlayHoleRect;
        mContext = context;
        fullWindow();
        initView();
    }

    public void setCallback(View.OnClickListener listener) {
        this.mListener = listener;
    }

    private void initView() {
        OverlayWithHoleFrameLayout guideLayout = findViewById(R.id.guideLayout);
        guideLayout.setOverlayColor(Color.parseColor("#cc000000"));
        guideLayout.setOverlayBorder( mContext.getResources().getDisplayMetrics().density,
                Color.parseColor("#ff6851ff"));
        float density = mContext.getResources().getDisplayMetrics().density;
        int topNoStatusBar = (int) (overlayHoleRect.top - 24*density);
        int bottomNoStatusBar = (int) (overlayHoleRect.bottom - 24*density);
        guideLayout.setOverlayHole(
                overlayHoleRect.left,
                topNoStatusBar,
                overlayHoleRect.right,
                bottomNoStatusBar,
                3 * density);
        guideLayout.setVisibility(View.VISIBLE);
        //tip
        View tipView = findViewById(R.id.tip_tv);
        ConstraintLayout constraintLayout = findViewById(R.id.cs_homeguide);
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(constraintLayout);
        constraintSet.connect(tipView.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, (int) (overlayHoleRect.left - 18*density));
        constraintSet.connect(tipView.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, (int) (topNoStatusBar + 5*density));
        constraintSet.applyTo(constraintLayout);

        findViewById(R.id.skip_tv).setOnClickListener(this);
    }

    private void fullWindow() {
        if (getWindow() != null) {
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
    }


    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.skip_tv) {
            dismiss();
            if (mListener != null) {
                mListener.onClick(v);
            }
        }
    }
}
