package com.xyz.host.db;

import android.content.Context;
import android.graphics.Bitmap;

import androidx.lifecycle.LiveData;
import androidx.room.Room;
import androidx.sqlite.db.SimpleSQLiteQuery;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;

public class DbManager {
    private static DbManager instance;

    private DockerDataBase dataBase;

    public static DbManager getInstance() {
        if (instance == null) {
            synchronized (DbManager.class) {
                if (instance == null) {
                    instance = new DbManager();
                }
            }
        }
        return instance;
    }

    public void init(Context context) {
        dataBase = Room.databaseBuilder(context, DockerDataBase.class, "magic")
                .allowMainThreadQueries()
                .build();
    }

    private DockerDao getDockerDao() {
        return dataBase.getDockerDao();
    }

    public LocInfoDao getLocInfoDao() {
        return dataBase.getLocInfoDao();
    }

    public DockerBean addApp(String packageName, int userId, String appName, Bitmap icon, String installType, String apkPath, boolean is64bit) {
        DockerDao dockerDao = getDockerDao();
        int nextOrderId = dockerDao.getNextOrderId();
        byte[] appIcon = bitmap2byte(icon);
        String bitApp = is64bit ? "64" : "32";
        long installTime = System.currentTimeMillis();

        DockerBean dockerBean = new DockerBean(userId, appName, appIcon, packageName, apkPath, nextOrderId, bitApp, installType, installTime);
        dockerDao.insertOne(dockerBean);
        return dockerBean;
    }

    private static byte[] bitmap2byte(Bitmap bitmap) {
        if (bitmap == null) {
            return new byte[0]; // 返回空字节数组而不是null
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos);
        return baos.toByteArray();
    }

    public int getNextUserId(String packageName) {
        return getDockerDao().getNextUserId(packageName);
    }

    public List<String> getGroupByPkg() {
        return getDockerDao().getGroupByPkg();
    }

    public List<DockerBean> getAppAll() {
        return getDockerDao().getAll();
    }

    public LiveData<List<DockerBean>> getAllObserve(Integer userId) {
        if (userId == null) {
            return getDockerDao().getAllObserve();
        } else {
            return getDockerDao().getAllObserve(userId);
        }
    }

    public void updateOpenByPkg(DockerBean bean) {
        if (!bean.isOpened()) {
            bean.setOpened(true);
            getDockerDao().updateOpenByPkg(bean.getPackageName(), bean.getUserId(), true);
        }
    }

    public void updateOrder(List<DockerBean> data) {
        int[] arr = new int[data.size()];
        for (int i = 0; i < data.size(); i++) {
            arr[i] = data.get(i).getAppOrder();
        }
        Arrays.sort(arr);
        StringBuilder sb = new StringBuilder();
        sb.append("update DockerBean set app_order = CASE id");
        for (int i = 0; i < data.size(); i++) {
            DockerBean dockerBean = data.get(i);
            sb.append(" WHEN ").append(dockerBean.getId()).append(" THEN ").append(arr[i]);
        }
        sb.append(" end");
        getDockerDao().updateOrderById(new SimpleSQLiteQuery(sb.toString()));
    }

    public void updateLocOrder(List<LocInfo> data) {
        int[] arr = new int[data.size()];
        for (int i = 0; i < data.size(); i++) {
            arr[i] = data.get(i).getLocOrder();
        }
        Arrays.sort(arr);
        StringBuilder sb = new StringBuilder();
        sb.append("update LocInfo set loc_order = CASE id");
        for (int i = 0; i < data.size(); i++) {
            LocInfo dockerBean = data.get(i);
            sb.append(" WHEN ").append(dockerBean.getId()).append(" THEN ").append(arr[i]);
        }
        sb.append(" end");
        getLocInfoDao().updateOrderById(new SimpleSQLiteQuery(sb.toString()));
    }

    public void deleteOne(int userId, String packageName) {
        getDockerDao().deleteOne(userId, packageName);
    }

    public List<DockerBean> getListByPkg(String packageName) {
        return getDockerDao().getListByPkg(packageName);
    }

    public void deleteByPkg(String packageName) {
        getDockerDao().deleteByPkg(packageName);
    }

    public void deleteByBit(String appBit) {
        getDockerDao().deleteByBit(appBit);
    }

    public List<Integer> getUsers() {
        return getDockerDao().getUsers();
    }

    public void updateAppName(DockerBean bean){
        getDockerDao().updateAppNameByPkg(bean.getPackageName(), bean.getUserId(), bean.getAppName());
    }
}
