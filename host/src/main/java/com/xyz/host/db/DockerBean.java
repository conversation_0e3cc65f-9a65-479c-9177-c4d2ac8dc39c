package com.xyz.host.db;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverter;
import androidx.room.TypeConverters;

import com.fun.vbox.entity.VAppInfo;
import com.xyz.host.MainApp;

@Entity(indices = {@Index(value = {"user_id", "package_name"})})
public class DockerBean {
    @PrimaryKey(autoGenerate = true)
    int id;
    @ColumnInfo(name = "user_id")
    private int userId;
    @ColumnInfo(name = "app_name")
    private String appName;
    @ColumnInfo(name = "app_icon")
    private byte[] appIcon;
    @ColumnInfo(name = "package_name")
    private String packageName;
    @ColumnInfo(name = "apk_path")
    private String apkPath;
    @ColumnInfo(name = "app_order")
    private int appOrder;
    @ColumnInfo(name = "app_bit")
    private String appBit; //32/64
    @ColumnInfo(name = "install_type")
    private String installType;//app/file
    @ColumnInfo(name = "install_time")
    private long installTime;
    @ColumnInfo(name = "is_opened")
    private boolean isOpened = false;
    @ColumnInfo(name = "is_outside")
    private boolean isOutside = false;
    @ColumnInfo(name = "outside_pkg")
    private String outside_pkg; //外部分身包名
    @ColumnInfo(name = "last_update_Time")
    private long lastUpdateTime;//分身主应用更新时间
    @ColumnInfo(name = "app_version_code")
    private long appVersionCode;//分身主应用版本code
    @ColumnInfo(name = "extra")
    @TypeConverters(ExtraDataConverters.class)
    private ExtraData extra;
    @Ignore
    private int installState; //1正在安装
    @Ignore
    private int installProgress;
    @Ignore
    private VAppInfo appInfo;

    public static class ExtraDataConverters {
        @TypeConverter
        public static ExtraData fromJson(String json) {
            // 使用Gson将Json字符串转换为自定义的对象类型
            if (json == null) {
                return null;
            } else {
                return MainApp.getGson().fromJson(json, ExtraData.class);
            }
        }

        @TypeConverter
        public static String toJson(ExtraData extraObject) {
            // 使用Gson将自定义的对象类型转换为Json字符串
            if (extraObject == null) {
                return null;
            } else {
                return MainApp.getGson().toJson(extraObject);
            }
        }
    }

    public DockerBean(int userId, String appName, byte[] appIcon, String packageName, int installState) {
        this.userId = userId;
        this.appName = appName;
        this.appIcon = appIcon;
        this.packageName = packageName;
        this.installState = installState;
    }

    public DockerBean(int userId, String appName, byte[] appIcon, String packageName, String apkPath, int appOrder, String appBit, String installType, long installTime) {
        this.userId = userId;
        this.appName = appName;
        this.appIcon = appIcon;
        this.packageName = packageName;
        this.apkPath = apkPath;
        this.appOrder = appOrder;
        this.appBit = appBit;
        this.installType = installType;
        this.installTime = installTime;
    }

    public VAppInfo getAppInfo() {
        if (this.appInfo != null) {
            return this.appInfo;
        }
        this.appInfo = VAppInfo.makeFromSys(packageName, userId);
        if ("app".equals(installType)) {
            appInfo.appMode = VAppInfo.MODE_APP_FROM_SYSTEM;
        } else {
            appInfo.appMode = VAppInfo.MODE_APP_FROM_FILE;
        }
        appInfo.path = apkPath;
        appInfo.is64bit = "64".equals(appBit);
        return appInfo;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public byte[] getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(byte[] appIcon) {
        this.appIcon = appIcon;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getApkPath() {
        return apkPath;
    }

    public void setApkPath(String apkPath) {
        this.apkPath = apkPath;
    }

    public int getAppOrder() {
        return appOrder;
    }

    public void setAppOrder(int appOrder) {
        this.appOrder = appOrder;
    }

    public String getAppBit() {
        return appBit;
    }

    public void setAppBit(String appBit) {
        this.appBit = appBit;
    }

    public String getInstallType() {
        return installType;
    }

    public void setInstallType(String installType) {
        this.installType = installType;
    }

    public long getInstallTime() {
        return installTime;
    }

    public void setInstallTime(long installTime) {
        this.installTime = installTime;
    }

    public boolean isOpened() {
        return isOpened;
    }

    public void setOpened(boolean opened) {
        isOpened = opened;
    }

    public boolean isOutside() {
        return isOutside;
    }

    public void setOutside(boolean outside) {
        isOutside = outside;
    }

    public String getOutside_pkg() {
        return outside_pkg;
    }

    public void setOutside_pkg(String outside_pkg) {
        this.outside_pkg = outside_pkg;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public long getAppVersionCode() {
        return appVersionCode;
    }

    public void setAppVersionCode(long appVersionCode) {
        this.appVersionCode = appVersionCode;
    }

    public int getInstallState() {
        return installState;
    }

    public void setInstallState(int installState) {
        this.installState = installState;
    }

    public int getInstallProgress() {
        return installProgress;
    }

    public void setInstallProgress(int installProgress) {
        this.installProgress = installProgress;
    }

    public void setExtra(ExtraData extra) {
        this.extra = extra;
    }

    public ExtraData getExtra() {
        return extra;
    }
}
