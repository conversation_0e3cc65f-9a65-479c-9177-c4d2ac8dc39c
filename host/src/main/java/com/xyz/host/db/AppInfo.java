package com.xyz.host.db;

import android.content.pm.ApplicationInfo;
import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;

public class AppInfo implements Parcelable {
    private String packageName;
    private int userId;
    private String apkPath;
    private String installType;
    private Bitmap appIcon;
    private String appName;
    private String appBit;
    private ApplicationInfo applicationInfo;

    public AppInfo() {
    }

    public AppInfo(String packageName, int userId, Bitmap appIcon, String appName, String appBit) {
        this.packageName = packageName;
        this.userId = userId;
        this.appIcon = appIcon;
        this.appName = appName;
        this.appBit = appBit;
    }

    protected AppInfo(Parcel in) {
        packageName = in.readString();
        userId = in.readInt();
        apkPath = in.readString();
        installType = in.readString();
        appName = in.readString();
        appBit  = in.readString();
        appIcon = (Bitmap) in.readParcelable(getClass().getClassLoader());
        applicationInfo = (ApplicationInfo) in.readParcelable(getClass().getClassLoader());

    }

    public static final Creator<AppInfo> CREATOR = new Creator<AppInfo>() {
        @Override
        public AppInfo createFromParcel(Parcel in) {
            return new AppInfo(in);
        }

        @Override
        public AppInfo[] newArray(int size) {
            return new AppInfo[size];
        }
    };

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getApkPath() {
        return apkPath;
    }

    public void setApkPath(String apkPath) {
        this.apkPath = apkPath;
    }

    public String getInstallType() {
        return installType;
    }

    public void setInstallType(String installType) {
        this.installType = installType;
    }

    public Bitmap getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(Bitmap appIcon) {
        this.appIcon = appIcon;
    }

    public ApplicationInfo getApplicationInfo() {
        return applicationInfo;
    }

    public void setApplicationInfo(ApplicationInfo applicationInfo) {
        this.applicationInfo = applicationInfo;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppBit() {
        return appBit;
    }

    public void setAppBit(String appBit) {
        this.appBit = appBit;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(packageName);
        dest.writeInt(userId);
        dest.writeString(apkPath);
        dest.writeString(installType);
        dest.writeString(appName);
        dest.writeString(appBit);
        dest.writeParcelable(appIcon, flags);
        dest.writeParcelable(applicationInfo, flags);
    }
}
