package com.xyz.host.db;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverter;
import androidx.room.TypeConverters;

import com.fun.vbox.remote.vloc.VCell;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

@Entity(indices = {@Index(value = {"id"})})
public class LocInfo {
    @PrimaryKey(autoGenerate = true)
    int id;
    @ColumnInfo(name = "type")
    private int type;
    @ColumnInfo(name = "mcc")
    private int mcc;
    @ColumnInfo(name = "mnc")
    private int mnc;
    @ColumnInfo(name = "psc")
    private int psc;
    @ColumnInfo(name = "lac")
    private int lac;
    @ColumnInfo(name = "cid")
    private int cid;
    @ColumnInfo(name = "base_station_id")
    private int baseStationId;
    @ColumnInfo(name = "system_id")
    private int systemId;
    @ColumnInfo(name = "network_id")
    private int networkId;

    @ColumnInfo(name = "latitude")
    private double latitude = 0.0D;
    @ColumnInfo(name = "longitude")
    private double longitude = 0.0D;
    @ColumnInfo(name = "address")
    private String address;

    @ColumnInfo(name = "bssid")
    private String bssid;

    @ColumnInfo(name = "list_bssid")
    @TypeConverters(StringListConverter.class)
    private List<String> listBssid;

    @ColumnInfo(name = "name")
    private String name;

    @ColumnInfo(name = "loc_order")
    private int locOrder = 0;

    public static class StringListConverter {
        @TypeConverter
        public List<String> convertToEntityProperty(String databaseValue) {
            if (databaseValue == null) {
                return new ArrayList<>();
            }
            try {
                JSONArray array = new JSONArray(databaseValue);
                ArrayList<String> ret = new ArrayList<>();
                for (int i = 0; i < array.length(); i++) {
                    ret.add(array.getString(i));
                }
                return ret;
            }
            catch (Exception e) {
                return new ArrayList<>();
            }
        }

        @TypeConverter
        public String convertToDatabaseValue(List<String> entityProperty) {
            try {
                if (entityProperty == null) {
                    return null;
                }
                return new JSONArray(entityProperty).toString();
            }
            catch (Exception e) {
                return null;
            }
        }
    }

    public LocInfo() {

    }

    public VCell getVCell() {
        VCell vCell = new VCell();
        vCell.type = type;
        vCell.mcc = mcc;
        vCell.mnc = mnc;
        vCell.psc = psc;
        vCell.lac = lac;
        vCell.cid = cid;
        vCell.baseStationId = baseStationId;
        vCell.systemId = systemId;
        vCell.networkId = networkId;
        return vCell;
    }

    public LocInfo(LocInfo info) {
        type = info.type;
        mcc = info.mcc;
        mnc = info.mnc;
        psc = info.psc;
        lac = info.lac;
        cid = info.cid;
        baseStationId = info.baseStationId;
        systemId = info.systemId;
        networkId = info.networkId;
        latitude = info.latitude;
        longitude = info.longitude;
        address = info.address;
        name = info.name;
        bssid = info.bssid;
        listBssid = info.listBssid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getMcc() {
        return mcc;
    }

    public void setMcc(int mcc) {
        this.mcc = mcc;
    }

    public int getMnc() {
        return mnc;
    }

    public void setMnc(int mnc) {
        this.mnc = mnc;
    }

    public int getPsc() {
        return psc;
    }

    public void setPsc(int psc) {
        this.psc = psc;
    }

    public int getLac() {
        return lac;
    }

    public void setLac(int lac) {
        this.lac = lac;
    }

    public int getCid() {
        return cid;
    }

    public void setCid(int cid) {
        this.cid = cid;
    }

    public int getBaseStationId() {
        return baseStationId;
    }

    public void setBaseStationId(int baseStationId) {
        this.baseStationId = baseStationId;
    }

    public int getSystemId() {
        return systemId;
    }

    public void setSystemId(int systemId) {
        this.systemId = systemId;
    }

    public int getNetworkId() {
        return networkId;
    }

    public void setNetworkId(int networkId) {
        this.networkId = networkId;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public List<String> getListBssid() {
        return listBssid;
    }

    public void setListBssid(List<String> listBssid) {
        this.listBssid = listBssid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLocOrder() {
        return locOrder;
    }

    public void setLocOrder(int locOrder) {
        this.locOrder = locOrder;
    }
}
