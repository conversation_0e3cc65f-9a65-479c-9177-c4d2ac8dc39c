package com.xyz.host.db;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.RawQuery;
import androidx.sqlite.db.SupportSQLiteQuery;

import java.util.List;

@Dao
public interface DockerDao {
    @Insert
    void insertOne(DockerBean bean);

    @Query("delete from DockerBean where user_id = :userId and package_name = :packageName")
    void deleteOne(int userId, String packageName);

    @Query("delete from DockerBean where package_name = :packageName")
    void deleteByPkg(String packageName);

    @Query("delete from DockerBean where app_bit = :appBit")
    void deleteByBit(String appBit);

    @Query("update DockerBean set is_opened = :isOpened where package_name = :packageName and user_id = :userId")
    void updateOpenByPkg(String packageName, int userId, boolean isOpened);

    @Query("update DockerBean set app_name = :appName where package_name = :packageName and user_id = :userId")
    void updateAppNameByPkg(String packageName, int userId, String appName);

    @RawQuery
    List<Object> updateOrderById(SupportSQLiteQuery sql);

    @Query("SELECT * FROM DockerBean where package_name = :packageName")
    List<DockerBean> getListByPkg(String packageName);

    @Query("SELECT MAX(user_id) + 1 FROM DockerBean where package_name = :packageName")
    int getNextUserId(String packageName);

    //sqllite不允许非主键自增
    @Query("SELECT MAX(id) + 1 FROM DockerBean")
    int getNextOrderId();

    @Query("SELECT * FROM DockerBean ORDER BY app_order ASC")
    List<DockerBean> getAll();

    @Query("SELECT * FROM DockerBean ORDER BY app_order ASC")
    LiveData<List<DockerBean>> getAllObserve();

    @Query("SELECT * FROM DockerBean where user_id = :userId ORDER BY app_order ASC")
    LiveData<List<DockerBean>> getAllObserve(int userId);

    @Query("SELECT package_name FROM DockerBean where install_type=\"app\" GROUP BY package_name")
    List<String> getGroupByPkg();

    @Query("SELECT user_id FROM DockerBean GROUP BY user_id")
    List<Integer> getUsers();
}
