package com.xyz.host.db;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.RawQuery;
import androidx.room.Update;
import androidx.sqlite.db.SupportSQLiteQuery;

import java.util.List;

@Dao
public interface LocInfoDao {
    @Insert
    void insertOne(LocInfo bean);

    @Delete
    void deleteById(LocInfo bean);

    @Update
    void updateById(LocInfo bean);

    @Query("SELECT * FROM LocInfo where id = :id")
    LocInfo getLocInfo(int id);

    @Query("SELECT * FROM LocInfo ORDER BY loc_order ASC")
    List<LocInfo> getAll();

    @Query("SELECT * FROM LocInfo ORDER BY loc_order ASC")
    LiveData<List<LocInfo>> getAllObserve();

    @Query("SELECT MAX(id) + 1 FROM LocInfo")
    int getNextOrderId();

    @RawQuery
    List<Object> updateOrderById(SupportSQLiteQuery sql);
}
