package com.xyz.host.user.engine;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.model.UserInfo;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.utils.EncryptionUtils;
import com.xyz.host.utils.GsonUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.Map;

public class UserAgent {
    @SuppressLint("StaticFieldLeak")
    private static volatile UserAgent sInstance;
    private UserInfo mUserInfo;

    public static UserAgent getInstance() {
        if (sInstance == null) {
            synchronized (UserAgent.class) {
                if (sInstance == null) {
                    sInstance = new UserAgent();
                }
            }
        }
        return sInstance;
    }

    private UserAgent() {

    }

    public void init() {
        String userInfo = AppSharePref.get().getString(AppSharePref.KEY_USER_INFO);
        try {
            String key = EncryptionUtils.decrypt(userInfo);
            if (!TextUtils.isEmpty(key)) {
                userInfo = key;
            }
        } catch (Exception e) {
            //
        }
        mUserInfo = GsonUtils.gson2Bean(userInfo, UserInfo.class);
    }

    public void update() {
        if (mUserInfo == null) {
            return;
        }
        String updateConfig = UMRemoteConfig.getInstance().getConfigValue("user_up_gap");
        long updateInterval = 10;
        try {
            if (!TextUtils.isEmpty(updateConfig)) {
                updateInterval = Long.parseLong(updateConfig);
            }
        } catch (Throwable e) {
            // Ignored.
        }
        long curTime = System.currentTimeMillis();
        // 当前时间小于上次用户返回时间，这一般说明用户改小了时间，直接刷新
        if (curTime < mUserInfo.responseTime) {
            updateUserInfo();
            return;
        }

        long diffTime = curTime - mUserInfo.responseTime;
        if (diffTime > updateInterval * 60000L) {
            updateUserInfo();
        }
    }

    private void updateUserInfo() {
        new ApiServiceDelegate().user(getMobile(), new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {

            }

            @Override
            public void onSuccess(String result) {
                UserInfo info = getUserInfo();
                if (info == null) {
                    info = new UserInfo();
                }
                EventBus.getDefault().post(info);
            }

            @Override
            public void onFail(String error) {

            }
        });
    }

    public void setMobile(String mobile) {
        AppSharePref.get().putString("mobile", mobile);
    }

    public String getMobile() {
        return AppSharePref.get().getString("mobile", "");
    }

    public void setHeadUrl(String url) {
        AppSharePref.get().putString("head_url", url);
    }

    public String getHeadUrl() {
        return AppSharePref.get().getString("head_url", "");
    }

    public void setNick(String nick) {
        AppSharePref.get().putString("nick", nick);
    }

    public String getNick() {
        return AppSharePref.get().getString("nick", "");
    }

    public boolean isLogin() {
        return mUserInfo != null;
    }

    public void cleanUserInfo() {
        AppSharePref.get().putString(AppSharePref.KEY_USER_INFO, "");
        setNick("");
        setHeadUrl("");
    }

    public void setUserInfo(UserInfo userInfo) {
        mUserInfo = userInfo;
    }

    @Nullable
    public UserInfo getUserInfo() {
        return mUserInfo;
    }

    public boolean isVipUser() {
        if (mUserInfo == null) {
            return false;
        }
        return mUserInfo.isVip && (mUserInfo.expireTime >= mUserInfo.responseTime);
    }
}