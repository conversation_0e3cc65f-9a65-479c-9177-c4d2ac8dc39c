package com.xyz.host.user.service;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.Headers;
import retrofit2.http.POST;

public interface ApiService {
    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_login")
    Call<String> login(@HeaderMap Map<String, String> headers, @Body String body);

    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_user")
    Call<String> user(@HeaderMap Map<String, String> headers, @Body String body);


    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_buy")
    Call<String> buy(@HeaderMap Map<String, String> headers, @Body String body);

    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_invite_code")
    Call<String> getInviteCode(@HeaderMap Map<String, String> headers, @Body String body);

    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_use_invite")
    Call<String> useInviteCode(@HeaderMap Map<String, String> headers, @Body String body);

    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_tickets")
    Call<String> getTickets(@HeaderMap Map<String, String> headers, @Body String body);

    @Headers({
            "Cache-Control: no-cache",
            "Content-Type: application/json; charset=utf-8",
            "Accept: application/json; charset=utf-8"
    })
    @POST("api/yingyu_use_ticket")
    Call<String> useTicket(@HeaderMap Map<String, String> headers, @Body String body);
}
