package com.xyz.host.user.engine;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.content.pm.SigningInfo;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.model.UserInfo;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.user.converter.StringConverterFactory;
import com.xyz.host.user.service.ApiService;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.EncryptionUtils;
import com.xyz.host.utils.GsonUtils;

import org.json.JSONObject;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

public class ApiServiceDelegate {
    private static final String BASE_URL = "https://wj.aoqi360.com";
    public static final String KEY_X_AUTH_TOKEN = "X-Auth-Token";

    public static int PAY_ALI = 0;
    public static int PAY_WECHAT = 1;

    private final Context mContext;
    private List<String> mUrlList;
    private int mUrlIndex = 0;
    private int mTryTimes = 0;
    private final Random random = new Random(System.currentTimeMillis());

    public ApiServiceDelegate() {
        mContext = MainApp.getApp();
        String apiProxy = UMRemoteConfig.getInstance().getConfigValue("api_url");
        mUrlList = GsonUtils.gson2List(apiProxy, String.class);
        if (mUrlList == null || mUrlList.isEmpty()) {
            mUrlList = new ArrayList<>();
            mUrlList.add(BASE_URL);
        } else if (mUrlList.size() > 1){
            mUrlIndex = new Random().nextInt(mUrlList.size());
        }
    }

    public ApiService getAPIService() {
        OkHttpClient.Builder builder = NetworkClient.getClientBuilder(true);
        return getRetrofitBuilder(mUrlList.get(mUrlIndex), builder.build())
                .build().create(ApiService.class);
    }

    public Retrofit.Builder getRetrofitBuilder(String url, OkHttpClient client) {
        return new Retrofit.Builder()
                .baseUrl(url)
                .addConverterFactory(new StringConverterFactory())
                .client(client);
    }

    private void moveToNextUrl() {
        if (++mUrlIndex < mUrlList.size()) {
            return;
        }
        mUrlIndex = 0;
    }

    public void user(String openid, final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", openid);
            params.put("app_ver_code", versionCode);
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().user(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                onRegisterResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    public void login(String openid, String nick, final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", openid);
            params.put("app_ver_code", versionCode);
            if (!TextUtils.isEmpty(nick)) {
                params.put("nick", nick);
            }
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().login(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                onRegisterResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    private void onRegisterResponse(Response<String> response, ApiCallback callback) {
        if (response.isSuccessful() && response.body() != null) {
            String body = response.body();
            String encryptKey = "";
            String decryptKey = "";
            try {
                JSONObject jsonObject = new JSONObject(body);
                encryptKey = jsonObject.getString("key");
                decryptKey = EncryptionUtils.decrypt(encryptKey);
            } catch (Exception e) {
                //
            }
            UserInfo userInfo = GsonUtils.gson2Bean(decryptKey, UserInfo.class);

            // mobile为空，代表应该是被踢了
            if (userInfo == null || TextUtils.isEmpty(userInfo.mobile)) {
                UserAgent.getInstance().setUserInfo(null);
                UserAgent.getInstance().cleanUserInfo();
            } else {
                UserAgent.getInstance().setUserInfo(userInfo);
                AppSharePref.get().putString(AppSharePref.KEY_USER_INFO, encryptKey);
            }
            if (callback != null) {
                callback.onSuccess(body);
            }
        } else {
            String message = "";
            if (response.errorBody() != null) {
                try {
                    message = response.errorBody().string();
                    JSONObject object = new JSONObject(message);
                    message = object.optString("message");
                } catch (Exception e) {
                    // Ignored.
                }
            }
            if (callback != null) {
                callback.onFail(message);
            }
        }
    }

    public Map<String, String> getHeader(String versionCode, String nonce) {
        Map<String, String> header = new HashMap<>();
        String token = generateHeaderToken(mContext, versionCode, nonce);
        if (token != null) {
            header.put(KEY_X_AUTH_TOKEN, token);
        }
        return header;
    }

    public void buy(int payType, String type, ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);
        JSONObject json = new JSONObject();
        String params = "";
        try {
            json.put("mobile", UserAgent.getInstance().getMobile());
            json.put("type", type);
            json.put("pay_type", payType);
            json.put("app_ver_code", versionCode);
            json.put("nonce", nonce);
            params = json.toString();
        } catch (Throwable e) {
            // Ignored.
        }
        if (TextUtils.isEmpty(params)) {
            return;
        }
        if (callback != null) {
            callback.onParams(header, params);
        }

        Call<String> request = getAPIService().buy(header, params);
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(Call<String> call, Response<String> response) {
                if (response.isSuccessful() && response.body() != null) {
                    if (callback != null) {
                        callback.onSuccess(response.body());
                    }
                } else {
                    String message = "";
                    if (response.errorBody() != null) {
                        try {
                            message = response.errorBody().string();
                            // Log.e("MyTest", "error:" + message);
                            JSONObject object = new JSONObject(message);
                            message = object.optString("message");
                        } catch (Exception e) {
                            // Ignored.
                        }
                    }
                    if (callback != null) {
                        callback.onFail(message);
                    }
                }
            }

            @Override
            public void onFailure(Call<String> call, Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    /**
     * 获取邀请码
     */
    public void getInviteCode(final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", UserAgent.getInstance().getMobile());
            params.put("app_ver_code", versionCode);
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            if (callback != null) {
                callback.onFail("参数构造失败");
            }
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().getInviteCode(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                handleInviteResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    /**
     * 使用邀请码
     */
    public void useInviteCode(String inviteCode, final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", UserAgent.getInstance().getMobile());
            params.put("invite_code", inviteCode);
            params.put("app_ver_code", versionCode);
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            if (callback != null) {
                callback.onFail("参数构造失败");
            }
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().useInviteCode(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                handleInviteResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    /**
     * 获取体验券列表
     */
    public void getTickets(final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", UserAgent.getInstance().getMobile());
            params.put("app_ver_code", versionCode);
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            if (callback != null) {
                callback.onFail("参数构造失败");
            }
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().getTickets(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                handleInviteResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    /**
     * 使用体验券
     */
    public void useTicket(int ticketId, final ApiCallback callback) {
        String versionCode = String.valueOf(CommonUtils.getVerCode());
        String nonce = String.valueOf(random.nextInt(10000000));
        Map<String, String> header = getHeader(versionCode, nonce);

        JSONObject params = new JSONObject();
        try {
            params.put("mobile", UserAgent.getInstance().getMobile());
            params.put("ticket_id", ticketId);
            params.put("app_ver_code", versionCode);
            String uuid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
            if (TextUtils.isEmpty(uuid)) {
                uuid = UUID.randomUUID().toString();
            }
            params.put("aid", uuid);
            params.put("nonce", nonce);
        } catch (Throwable ignored) {
            if (callback != null) {
                callback.onFail("参数构造失败");
            }
            return;
        }

        if (callback != null) {
            callback.onParams(header, params.toString());
        }

        final Call<String> request = getAPIService().useTicket(header, params.toString());
        request.enqueue(new Callback<String>() {
            @Override
            public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                handleInviteResponse(response, callback);
            }

            @Override
            public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                if (callback != null) {
                    callback.onFail(t.getMessage());
                }
            }
        });
    }

    /**
     * 处理邀请相关接口的响应
     */
    private void handleInviteResponse(Response<String> response, ApiCallback callback) {
        if (response.isSuccessful() && response.body() != null) {
            if (callback != null) {
                callback.onSuccess(response.body());
            }
        } else {
            String message = "";
            if (response.errorBody() != null) {
                try {
                    message = response.errorBody().string();
                    JSONObject object = new JSONObject(message);
                    message = object.optString("message");
                } catch (Exception e) {
                    // Ignored.
                }
            }
            if (callback != null) {
                callback.onFail(message);
            }
        }
    }

    public static String generateHeaderToken(Context context, String versionCode, String nonce) {
        StringBuilder builder = new StringBuilder();
        try {
            builder.append("apk_sign")
                    .append(getAppSignature(context))
                    .append("app_ver_code").append(versionCode)
                    .append("nonce").append(nonce)
                    .append("auth_appkey").append("MuYmxvY2twcm94eXYZbnByb29ubGluZY");
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(builder.toString().getBytes());
            return bytesToHex(md.digest());
        } catch (Exception e) {
            return null;
        }
    }

    public static String bytesToHex(byte[] bytes) {
        char[] hexArray = "0123456789abcdef".toCharArray();
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String getAppSignature(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            return getSignatureApi28(context);
        } else {
            return getSignature(context);
        }
    }

    @SuppressWarnings("deprecation")
    @SuppressLint("PackageManagerGetSignatures")
    private static String getSignature(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            for (Signature signature : packageInfo.signatures) {
                MessageDigest md = MessageDigest.getInstance("SHA-1");
                md.update(signature.toByteArray());
                String currentSignature = Base64.encodeToString(md.digest(), Base64.DEFAULT);
                if (!TextUtils.isEmpty(currentSignature)) {
                    return currentSignature.trim();
                }
            }
        } catch (Throwable e) {
            //
        }
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.P)
    private static String getSignatureApi28(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNING_CERTIFICATES);
            SigningInfo info = packageInfo.signingInfo;
            Signature[] signatures;
            if (info.hasMultipleSigners()) {
                signatures = info.getApkContentsSigners();
            } else {
                signatures = info.getSigningCertificateHistory();
            }
            for (Signature signature : signatures) {
                MessageDigest md = MessageDigest.getInstance("SHA-1");
                md.update(signature.toByteArray());
                String currentSignature = Base64.encodeToString(md.digest(), Base64.DEFAULT);
                if (!TextUtils.isEmpty(currentSignature)) {
                    return currentSignature.trim();
                }
            }
        } catch (Throwable e) {
            //
        }
        return null;
    }
}