package com.xyz.host.user.engine;

import com.xyz.host.utils.CommonUtils;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class ProxyCheckInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        if (CommonUtils.isKillProxySet()) {
            if (CommonUtils.isProxySet().first) {
                // android.util.Log.e("MyTest", "proxy!!!");
                throw new IOException("Request Error 101");
            }
        }

        // 没有代理设置，继续处理请求
        Request request = chain.request();
        return chain.proceed(request);
    }
}
