package com.xyz.host.ads;

import android.app.Activity;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentActivity;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.BuildConfig;
import com.xyz.host.R;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.home.VipPaymentHelper;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.utils.NetworkUtils;

import java.util.concurrent.TimeUnit;

public class RewardHandler {
    private int mRewardMin = 30;
    private int mTryLoadRewardCount = 0;
    private RewardVideo mRewardVideo;

    private long getRewardTime() {
        long time = AppSharePref.get().getLong("expire_time", 0);
        if (time != 0) {
            long currentTime = System.currentTimeMillis();
            return time - currentTime;
        } else {
            return 0;
        }
    }

    public void init() {
        mRewardVideo = new RewardVideo();
    }

    public boolean handleReward(FragmentActivity activity, Runnable runnable) {
        String rewardTip = "";
        try {
            String strValue = UMRemoteConfig.getInstance().getConfigValue("reward_min");
            mRewardMin = Integer.parseInt(strValue);
            rewardTip = "奖励" + strValue + "分钟";
        } catch (Throwable e) {
            // Ignored.
        }

        boolean isShowAd = getRewardTime() <= 0;
        int tryMax = 10;
        try {
            tryMax = Integer.parseInt(UMRemoteConfig.getInstance().getConfigValue("reward_try_max"));
        } catch (Throwable e) {
            // Ignored.
        }
        if (isShowAd) {
            if (BuildConfig.DEBUG) {
                tryMax = Integer.MAX_VALUE;
            }
            String finalRewardTip = rewardTip;
            int finalTryMax = tryMax;
            int finalRewardMin = mRewardMin;
            new CommonDialog.Builder(activity)
                    .setTitle("提示")
                    .setMessage("观看激励广告奖励" + mRewardMin + "分钟免费使用时间")
                    .setNegativeButton("看广告")
                    .setPositiveButton("注册会员免广告")
                    .setCallback(new CommonDialog.Callback() {
                        @Override
                        public void onOk() {
                            super.onCancel();
                            VipPaymentHelper.showVipPaymentDialog(activity);
                        }

                        @Override
                        public void onCancel() {
                            mRewardVideo.loadAd(activity, new RewardVideo.IRewardVideoResult() {
                                @Override
                                public void onError(int code, String message) {
                                    if (!NetworkUtils.isNetworkAvailable()) {
                                        new CommonDialog.Builder(activity)
                                                .setTitle("提示")
                                                .setMessage("没有联网，需要检查一下")
                                                .setNegativeButton(R.string.cancel)
                                                .setPositiveButton(R.string.ensure)
                                                .setCallback(new CommonDialog.Callback()).show();
                                        return;
                                    }
                                    if (++mTryLoadRewardCount > finalTryMax) {
                                        rewardResult(true);
                                    } else {
                                        final RewardVideo.IRewardVideoResult callback = this;
                                        new CommonDialog.Builder(activity)
                                                .setTitle("提示")
                                                .setMessage("加载失败，稍候重试一下")
                                                .setNegativeButton(R.string.cancel)
                                                .setPositiveButton(R.string.ensure)
                                                .setCallback(new CommonDialog.Callback() {
                                                    @Override
                                                    public void onOk() {
                                                        mRewardVideo.loadAd(activity, callback);
                                                    }
                                                }).show();
                                    }
                                }

                                @Override
                                public void onAdResult(boolean isRewardVerify) {
                                    rewardResult(isRewardVerify);
                                }

                                private void rewardResult(boolean isRewardVerify) {
                                    if (!isRewardVerify) {
                                        return;
                                    }
                                    mTryLoadRewardCount = 0;
                                    long currentTime = System.currentTimeMillis();
                                    long expireTime = currentTime + TimeUnit.MINUTES.toMillis(finalRewardMin);
                                    AppSharePref.get().putLong("expire_time", expireTime);
                                    if (!TextUtils.isEmpty(finalRewardTip)) {
                                        Toast.makeText(activity, finalRewardTip, Toast.LENGTH_SHORT).show();
                                    }
                                    if (runnable != null) {
                                        runnable.run();
                                    }
                                }

                                @Override
                                public void onAdAgainResult(boolean isRewardVerify) {
                                    rewardResult(isRewardVerify);
                                }
                            });
                        }
                    }).show();
            return true;
        }
        return false;
    }
}
