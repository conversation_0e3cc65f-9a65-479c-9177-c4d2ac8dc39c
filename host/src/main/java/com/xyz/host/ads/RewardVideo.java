package com.xyz.host.ads;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdLoadType;
import com.bytedance.sdk.openadsdk.TTAdManager;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAppDownloadListener;
import com.bytedance.sdk.openadsdk.TTRewardVideoAd;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.yingyu.zh.multiapp.AdIdByReviewMode;

public class RewardVideo {
    private static final String TAG = "RewardVideo";
    private final TTAdNative mTTAdNative;
    private TTRewardVideoAd mTTRewardVideoAd;
    private boolean mRewardVerify;
    private boolean mRewardAgainVerify;

    public interface IRewardVideoResult {
        void onAdResult(boolean isRewardVerify);
        void onAdAgainResult(boolean isRewardAgainVerify);
        void onError(int code, String message);
    }

    public RewardVideo() {
        TTAdManager ttAdManager = TTAdManagerHolder.get();
        mTTAdNative = ttAdManager.createAdNative(MainApp.getApp());
    }

    public void loadAd(Activity activity, String adsId, final IRewardVideoResult iRewardVideoResult) {
        if (TextUtils.isEmpty(adsId) || adsId.equals("0")) {
            if (iRewardVideoResult != null) {
                iRewardVideoResult.onAdResult(true);
            }
            return;
        }

        AdSlot adSlot = new AdSlot.Builder()
                .setAdLoadType(TTAdLoadType.LOAD)
                .setCodeId(adsId)
                .build();

        mTTAdNative.loadRewardVideoAd(adSlot, new TTAdNative.RewardVideoAdListener() {
            @Override
            public void onError(int code, String message) {
                // Log.e("MyTest", "code:" + code + "," + message);
                if (iRewardVideoResult != null) {
                    iRewardVideoResult.onError(code, message);
                }
            }

            //视频广告加载后，视频资源缓存到本地的回调，在此回调后，播放本地视频，流畅不阻塞。
            @Override
            public void onRewardVideoCached() {

            }

            @Override
            public void onRewardVideoCached(TTRewardVideoAd ad) {

            }

            //视频广告的素材加载完毕，比如视频url等，在此回调后，可以播放在线视频，网络不好可能出现加载缓冲，影响体验。
            @Override
            public void onRewardVideoAdLoad(TTRewardVideoAd ad) {
                mTTRewardVideoAd = ad;
                mTTRewardVideoAd.setRewardAdInteractionListener(new TTRewardVideoAd.RewardAdInteractionListener() {
                    @Override
                    public void onAdShow() {

                    }

                    @Override
                    public void onAdVideoBarClick() {

                    }

                    @Override
                    public void onAdClose() {
                        if (iRewardVideoResult != null) {
                            iRewardVideoResult.onAdResult(mRewardVerify);
                        }
                    }

                    //视频播放完成回调
                    @Override
                    public void onVideoComplete() {

                    }

                    @Override
                    public void onVideoError() {

                    }

                    //视频播放完成后，奖励验证回调，rewardVerify：是否有效，rewardAmount：奖励梳理，rewardName：奖励名称
                    @Override
                    public void onRewardVerify(boolean rewardVerify, int rewardAmount, String rewardName, int errorCode, String errorMsg) {

                    }

                    @Override
                    public void onRewardArrived(boolean isRewardValid, int i, Bundle bundle) {
                        mRewardVerify = isRewardValid;
                    }

                    @Override
                    public void onSkippedVideo() {

                    }
                });
                mTTRewardVideoAd.setRewardPlayAgainInteractionListener(new TTRewardVideoAd.RewardAdInteractionListener() {
                    @Override
                    public void onAdShow() {

                    }

                    @Override
                    public void onAdVideoBarClick() {

                    }

                    @Override
                    public void onAdClose() {
                        if (iRewardVideoResult != null) {
                            iRewardVideoResult.onAdAgainResult(mRewardAgainVerify);
                        }
                    }

                    //视频播放完成回调
                    @Override
                    public void onVideoComplete() {

                    }

                    @Override
                    public void onVideoError() {
                    }

                    //视频播放完成后，奖励验证回调，rewardVerify：是否有效，rewardAmount：奖励梳理，rewardName：奖励名称
                    @Override
                    public void onRewardVerify(boolean rewardVerify, int rewardAmount, String rewardName, int errorCode, String errorMsg) {

                    }

                    @Override
                    public void onRewardArrived(boolean isRewardValid, int i, Bundle bundle) {
                        mRewardAgainVerify = isRewardValid;
                    }

                    @Override
                    public void onSkippedVideo() {

                    }
                });
                mTTRewardVideoAd.setDownloadListener(new TTAppDownloadListener() {
                    @Override
                    public void onIdle() {

                    }

                    @Override
                    public void onDownloadActive(long totalBytes, long currBytes, String fileName, String appName) {

                    }

                    @Override
                    public void onDownloadPaused(long totalBytes, long currBytes, String fileName, String appName) {

                    }

                    @Override
                    public void onDownloadFailed(long totalBytes, long currBytes, String fileName, String appName) {

                    }

                    @Override
                    public void onDownloadFinished(long totalBytes, String fileName, String appName) {

                    }

                    @Override
                    public void onInstalled(String fileName, String appName) {

                    }
                });
                if (mTTRewardVideoAd != null) {
                    mTTRewardVideoAd.showRewardVideoAd(activity);
                }
            }
        });
    }

    public void loadAd(Activity activity, final IRewardVideoResult iRewardVideoResult) {
        String codeId = AdIdByReviewMode.getAdId("reward_ads_id");
        loadAd(activity, codeId, iRewardVideoResult);
    }
}
