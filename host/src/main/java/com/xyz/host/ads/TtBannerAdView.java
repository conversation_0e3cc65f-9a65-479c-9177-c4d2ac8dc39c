package com.xyz.host.ads;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdDislike;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.UIUtils;

import java.lang.ref.WeakReference;
import java.util.List;

public class TtBannerAdView extends FrameLayout {
    private TTNativeExpressAd mBannerAd;
    private WeakReference<Activity> mWeakAct;

    public TtBannerAdView(Context context) {
        this(context, null);
    }

    public TtBannerAdView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TtBannerAdView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setVisibility(GONE);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mBannerAd != null) {
            mBannerAd.destroy();
        }
    }

    public void load(Activity activity) {
        String codeId = UMRemoteConfig.getInstance().getConfigValue("app_list_banner_code");
        if (TextUtils.isEmpty(codeId)) {
            return;
        }
        load(activity, codeId);
    }

    public void load(Activity activity, String codeId) {
        if (TextUtils.isEmpty(codeId)) {
            return;
        }

        mWeakAct = new WeakReference<>(activity);
        TTAdNative mAdNativeLoader = TTAdManagerHolder.get().createAdNative(getContext());
        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(codeId)
                .setAdCount(1)
                .setExpressViewAcceptedSize(UIUtils.getScreenWidthDp(getContext()), 75)
                .build();
        mAdNativeLoader.loadBannerExpressAd(adSlot, new TTAdNative.NativeExpressAdListener() {
            @Override
            public void onError(int i, String s) {
                // android.util.Log.e("MyTest", "haha:" + i + "," + s + ", codeId=" + codeId);
                removeAllViews();
            }

            @Override
            public void onNativeExpressAdLoad(List<TTNativeExpressAd> list) {
                if (list != null && !list.isEmpty()) {
                    mBannerAd = list.get(0);
                    mBannerAd.setSlideIntervalTime(30 * 1000);
                    show();
                }
            }
        });
    }

    private void show() {
        if (mBannerAd != null) {
            if (mWeakAct == null) {
                return;
            }
            Activity activity = mWeakAct.get();
            if (!CommonUtils.isLiveActivity(activity)) {
                return;
            }
            mBannerAd.setExpressInteractionListener(new TTNativeExpressAd.ExpressAdInteractionListener() {
                @Override
                public void onAdClicked(View view, int i) {

                }

                @Override
                public void onAdShow(View view, int i) {

                }

                @Override
                public void onRenderFail(View view, String s, int i) {

                }

                @Override
                public void onRenderSuccess(View view, float v, float v1) {

                }
            });
            mBannerAd.setDislikeCallback(activity, new TTAdDislike.DislikeInteractionCallback() {
                @Override
                public void onShow() {
                }

                @Override
                public void onSelected(int i, String s, boolean b) {
                    removeAllViews();
                }

                @Override
                public void onCancel() {
                }
            });
            // mBannerAd.render();
            View bannerView = mBannerAd.getExpressAdView();
            if (bannerView != null) {
                setVisibility(VISIBLE);
                removeAllViews();
                addView(bannerView);
            }
        }
    }
}
