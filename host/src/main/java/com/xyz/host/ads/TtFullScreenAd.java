package com.xyz.host.ads;

import android.app.Activity;
import android.text.TextUtils;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdConstant;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
import com.bytedance.sdk.openadsdk.mediation.ad.MediationAdSlot;
import com.fun.vbox.utils.VLog;
import com.xyz.host.utils.CommonUtils;

import java.lang.ref.WeakReference;

public class TtFullScreenAd {
    private static final String TAG = "TtFullScreenAd";
    private TTFullScreenVideoAd mTTFullScreenVideoAd;
    private WeakReference<Activity> mWeakAct;

    public interface ICallback {
        void onResult(int code);
    }

    public void load(Activity activity, String codeId) {
        load(activity, codeId, null);
    }

    // 错误码链接 https://www.csjplatform.com/supportcenter/5421
    public void load(Activity activity, String codeId, ICallback callback) {
        if (TextUtils.isEmpty(codeId)) {
            if (callback != null) {
                callback.onResult(40004);
            }
            return;
        }
        mWeakAct = new WeakReference<>(activity);

        TTAdNative adNativeLoader = TTAdManagerHolder.get().createAdNative(activity);
        AdSlot adslot = new AdSlot.Builder()
                .setCodeId(codeId)
                .setOrientation(TTAdConstant.ORIENTATION_VERTICAL)//设置横竖屏方向
                .setMediationAdSlot(new MediationAdSlot.Builder()
                        .setMuted(true)//是否静音
                        .setVolume(0.7f)//设置音量
                        .setBidNotify(true)//竞价结果通知
                        .build())
                .build();
        adNativeLoader.loadFullScreenVideoAd(adslot, new TTAdNative.FullScreenVideoAdListener() {
            public void onError(int code, String message) {
                VLog.w(TAG, "InterstitialFull onError code = " + code + " msg = " + message);
                if (callback != null) {
                    callback.onResult(code);
                }
            }

            public void onFullScreenVideoAdLoad(TTFullScreenVideoAd ad) {
                VLog.w(TAG, "InterstitialFull onFullScreenVideoLoaded");
                mTTFullScreenVideoAd = ad;
                show(callback);
            }

            public void onFullScreenVideoCached() {
                VLog.w(TAG, "InterstitialFull onFullScreenVideoCached");
            }

            public void onFullScreenVideoCached(TTFullScreenVideoAd ad) {
                VLog.w(TAG, "InterstitialFull onFullScreenVideoCached");
                mTTFullScreenVideoAd = ad;
                show(callback);
            }
        });
    }

    private void show(ICallback callback) {
        if (this.mTTFullScreenVideoAd == null) {
            if (callback != null) {
                callback.onResult(-3);
            }
            return;
        }
        this.mTTFullScreenVideoAd.setFullScreenVideoAdInteractionListener(new TTFullScreenVideoAd.FullScreenVideoAdInteractionListener() {
            public void onAdShow() {
                VLog.w(TAG, "InterstitialFull onAdShow");
            }

            public void onAdVideoBarClick() {
                VLog.w(TAG, "InterstitialFull onAdVideoBarClick");
            }

            public void onAdClose() {
                VLog.w(TAG, "InterstitialFull onAdClose");
                if (callback != null) {
                    callback.onResult(0);
                }
            }

            public void onVideoComplete() {
                VLog.w(TAG, "InterstitialFull onVideoComplete");
            }

            public void onSkippedVideo() {
                VLog.w(TAG, "InterstitialFull onSkippedVideo");
            }
        });

        if (mWeakAct == null) {
            if (callback != null) {
                callback.onResult(-1);
            }
            return;
        }
        Activity activity = mWeakAct.get();
        if (!CommonUtils.isLiveActivity(activity)) {
            if (callback != null) {
                callback.onResult(-1);
            }
            return;
        }
        this.mTTFullScreenVideoAd.showFullScreenVideoAd(activity);
    }
}
