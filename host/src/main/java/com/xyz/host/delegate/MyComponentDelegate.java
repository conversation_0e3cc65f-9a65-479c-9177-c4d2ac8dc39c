package com.xyz.host.delegate;

import android.app.Activity;
import android.app.Application;

import com.fun.vbox.client.core.VCore;

public class MyComponentDelegate extends VCore.ActivityLifecycleCallback {
    @Override
    public void onActivityOnCreate(Activity activity, int i) {
        super.onActivityOnCreate(activity, i);
        // android.util.Log.e("MyTest", "onActivityOnCreate:" + activity.getClass().getName());
    }

    @Override
    public void onApplicationOnCreate(Application application, int i) {
        super.onApplicationOnCreate(application, i);
    }
}
