package com.xyz.host.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.Html;
import android.text.TextUtils;
import android.util.Patterns;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.MainApp;
import com.xyz.host.home.WebActivity;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.model.TipsInfo;
import com.xyz.host.sp.AppSharePref;

import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;

import io.reactivex.android.schedulers.AndroidSchedulers;

public class HomeUtils {
    public static boolean tryShowDetailTip(Activity activity) {
        return tryShowDetailTip(activity, false);
    }

    public static boolean tryShowDetailTip(Activity activity, boolean forceShow) {
        String strConfig = UMRemoteConfig.getInstance().getConfigValue(AppSharePref.TIP_DETAIL_CONFIG);
        TipsInfo tipsInfo = MainApp.getGson().fromJson(strConfig, TipsInfo.class);
        if (tipsInfo == null) {
            return false;
        }
        tipsInfo.isShowEveryBoot = forceShow;
        return tryShowTip(activity, tipsInfo, AppSharePref.TIP_DETAIL_CONTENT, 0);
    }

    public static void tryShowTip(Activity activity) {
        String strTipConf = UMRemoteConfig.getInstance().getConfigValue("tip_config");
        if (TextUtils.isEmpty(strTipConf)) {
            return;
        }
        TipsInfo tipsInfo = null;
        try {
            tipsInfo = MainApp.getGson().fromJson(strTipConf, TipsInfo.class);
        } catch (Throwable e) {
            // Ignored.
        }
        if (tipsInfo == null) {
            return;
        }
        tryShowTip(activity, tipsInfo, AppSharePref.TIP_CONTENT, 1);
    }

    public static boolean tryShowTip(Activity activity, TipsInfo tipsInfo, String msgKey, long delay) {
        boolean bRet = false;
        String strText = AppSharePref.get().getString(msgKey);
        String strRemoteTitle = tipsInfo.title;
        String strRemoteText = tipsInfo.msg;
        String strRemoteBtnText = tipsInfo.btn;
        String strRemoteBtnNoText = tipsInfo.btnNo;
        String strRemoteUrl = tipsInfo.url;
        boolean isH5OpenInApp = tipsInfo.isH5OpenInApp;
        boolean isCancelable = tipsInfo.isCancelable;
        boolean isShowEveryBoot = tipsInfo.isShowEveryBoot;
        boolean isClickOkNoDismiss = tipsInfo.isClickOkNoDismiss;
        long minVerCode = tipsInfo.minVerCode;
        long maxVerCode = tipsInfo.maxVerCode;

        long curVerCode = CommonUtils.getVerCode();
        if (curVerCode < minVerCode) {
            return false;
        }
        if (maxVerCode > 0 && curVerCode > maxVerCode) {
            return false;
        }

        boolean isShowBtnNo = !TextUtils.isEmpty(strRemoteBtnNoText);
        if (!TextUtils.isEmpty(strRemoteText)
                && !TextUtils.isEmpty(strRemoteTitle)
                && !TextUtils.isEmpty(strRemoteBtnText)) {
            if (!strText.equals(strRemoteText) || isShowEveryBoot) {
                bRet = true;
                Runnable runnable = () -> {
                    if (!activity.isFinishing() && !activity.isDestroyed()) {
                        if (!isShowBtnNo) {
                            AppSharePref.get().putString(msgKey, strRemoteText);
                        }
                        new CommonDialog.Builder(activity)
                                .setTitle(Html.fromHtml(strRemoteTitle))
                                .setMessage(Html.fromHtml(strRemoteText))
                                .setCancelable(isCancelable)
                                .setIsClickOkNoDismiss(isClickOkNoDismiss)
                                .setPositiveButton(strRemoteBtnText)
                                .setNegativeButton(strRemoteBtnNoText)
                                .setCallback(new CommonDialog.Callback() {
                                    @Override
                                    public void onCancel() {
                                        super.onCancel();
                                        activity.finish();
                                    }

                                    @Override
                                    public void onOk() {
                                        if (isShowBtnNo) {
                                            AppSharePref.get().putString(msgKey, strRemoteText);
                                        }
                                        if (!TextUtils.isEmpty(strRemoteUrl)) {
                                            try {
                                                Intent intent = Intent.parseUri(strRemoteUrl, 0);
                                                activity.startActivity(intent);
                                                return;
                                            } catch (Throwable e) {
                                                // Ignored.
                                            }

                                            Matcher matcher = Patterns.WEB_URL.matcher(strRemoteUrl);
                                            if (matcher.find()) {
                                                Uri uri = Uri.parse(matcher.group());
                                                if (isH5OpenInApp) {
                                                    WebActivity.go(activity, uri.toString());
                                                } else {
                                                    Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                                                    activity.startActivity(intent);
                                                }
                                            }
                                        }
                                    }
                                }).show();
                    }
                };
                if (delay > 0) {
                    AndroidSchedulers.mainThread().scheduleDirect(runnable, delay, TimeUnit.SECONDS);
                } else {
                    AndroidSchedulers.mainThread().scheduleDirect(runnable);
                }
            }
        }
        return bRet;
    }
}
