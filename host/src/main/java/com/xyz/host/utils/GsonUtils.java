package com.xyz.host.utils;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.xyz.host.MainApp;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

public class GsonUtils {

    public static String getGsonString(Object object) {
        if (object == null) {
            return "";
        }
        try {
            return MainApp.getGson().toJson(object);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static <T> T gson2Bean(String gsonString, Class<T> cls) {
        if (TextUtils.isEmpty(gsonString)) {
            return null;
        }
        try {
            return MainApp.getGson().fromJson(gsonString, cls);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static <T> List<T> gson2List(String gsonString, Class<T> cls) {
        if (TextUtils.isEmpty(gsonString)) {
            return null;
        }
        try {
            return MainApp.getGson().fromJson(gsonString, new ListOfJson<T>(cls));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public static void saveBean2Json(Object object, String path) {
        if (object == null) {
            return;
        }
        Gson gson = MainApp.getGson();
        FileWriter writer = null;
        try {
            writer = new FileWriter(path);
            gson.toJson(object, writer);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static <T> T loadJson2Bean(String path, Class<T> cls) {
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        Gson gson = MainApp.getGson();
        FileReader reader = null;
        try {
            reader = new FileReader(path);
            return gson.fromJson(reader, cls);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    public static <T> List<T> loadJson2List(String path, Class<T> cls) {
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        Gson gson = MainApp.getGson();
        FileReader reader = null;
        try {
            reader = new FileReader(path);
            return gson.fromJson(reader, new ListOfJson<T>(cls));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
