package com.xyz.host.utils;

import android.app.Activity;
import android.content.pm.PackageInfo;

import com.fun.vbox.client.core.VCore;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.dialog.CommonDialog;

import io.reactivex.android.schedulers.AndroidSchedulers;

public class InstallExtUtils {
    public static final int BIT_32_VERSION = 100;

    public static boolean handlerInstall(Activity activity) {
        if (shouldUpdateExtPackage()) {
            installInner(activity);
            return true;
        }
        return false;
    }

    private static boolean shouldUpdateExtPackage() {
        PackageInfo packageInfo;
        try {
            packageInfo = MainApp.getApp().getPackageManager().getPackageInfo(VCore.getConfig().getExtPackageName(), 0);
        } catch (Throwable e) {
            return true;
        }
        return BIT_32_VERSION > packageInfo.versionCode;
    }

    private static void installInner(Activity activity) {
        String dlgTip = activity.getString(R.string.tip_32);
        AndroidSchedulers.mainThread().scheduleDirect(() -> new CommonDialog.Builder(activity)
                .setTitle(R.string.notice)
                .setMessage(dlgTip)
                .setCancelable(true)
                .setPositiveButton(R.string.ensure).setCallback(new CommonDialog.Callback() {
                    @Override
                    public void onOk() {
                        CommonUtils.install32Bit();
                    }
                }).show());
    }


}
