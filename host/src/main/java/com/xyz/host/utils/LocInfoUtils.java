package com.xyz.host.utils;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.CellLocation;
import android.telephony.TelephonyManager;
import android.telephony.cdma.CdmaCellLocation;
import android.telephony.gsm.GsmCellLocation;

import androidx.core.app.ActivityCompat;

import com.xyz.host.MainApp;
import com.xyz.host.db.LocInfo;

import java.util.ArrayList;
import java.util.List;

public class LocInfoUtils {
    public static void initBaseStationInfo(LocInfo locInfo) {
        TelephonyManager telephonyManager =
                (TelephonyManager) MainApp.getApp()
                        .getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager == null) {
            return;
        }
        if (ActivityCompat
                .checkSelfPermission(MainApp.getApp(), Manifest.permission.ACCESS_FINE_LOCATION) !=
                PackageManager.PERMISSION_GRANTED && ActivityCompat
                .checkSelfPermission(MainApp.getApp(), Manifest.permission.ACCESS_COARSE_LOCATION) !=
                PackageManager.PERMISSION_GRANTED) {
            return;
        }

        CellLocation cellLocation = telephonyManager.getCellLocation();
        // todo 卡死
        // locInfo.setType(telephonyManager.getNetworkType());
        if (cellLocation instanceof CdmaCellLocation) {
            CdmaCellLocation cdmaCellLocation = (CdmaCellLocation) cellLocation;
            locInfo.setBaseStationId(cdmaCellLocation.getBaseStationId());
            locInfo.setNetworkId(cdmaCellLocation.getNetworkId());
            locInfo.setSystemId(cdmaCellLocation.getSystemId());
        } else if (cellLocation instanceof GsmCellLocation) {
            GsmCellLocation gsmCellLocation = (GsmCellLocation) cellLocation;
            locInfo.setCid(gsmCellLocation.getCid());
            locInfo.setLac(gsmCellLocation.getLac());
            locInfo.setPsc(gsmCellLocation.getPsc());
        }
        String networkOperator = telephonyManager.getNetworkOperator();
        if ((networkOperator != null ? networkOperator.length() : 0) >= 5) {
            String substring = networkOperator.substring(0, 3);
            locInfo.setMcc(Integer.parseInt(substring));
            String substring2 = networkOperator.substring(3);
            locInfo.setMnc(Integer.parseInt(substring2));
        }
    }

    public static void initWifiInfo(LocInfo locInfo) {
        WifiManager wifiManager =
                (WifiManager) MainApp.getApp().getApplicationContext()
                        .getSystemService(Context.WIFI_SERVICE);
        if (wifiManager != null) {
            List<ScanResult> list = wifiManager.getScanResults();
            ArrayList<String> arrayList = new ArrayList<>(10);
            for (ScanResult scanResult : list) {
                arrayList.add(scanResult.BSSID);
            }
            locInfo.setListBssid(arrayList);
            WifiInfo connectionInfo = wifiManager.getConnectionInfo();

            locInfo.setBssid((connectionInfo != null ? connectionInfo.getBSSID() : null));
        }
    }
}
