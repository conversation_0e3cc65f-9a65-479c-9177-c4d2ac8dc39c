package com.xyz.host.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.content.FileProvider;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

public class ApkUtils {
    private static final String TAG = "ApkUtils";
    public static final int UNINSTALL_REQUEST_CODE = 123;

    public static void updateOriginFile(Context context, PackageInfo info, File originFile) throws IOException {
        if (info != null) {
            int sysApkVer = info.versionCode;
            String sysApkPath = info.applicationInfo.publicSourceDir;
            File sysApkFile = new File(sysApkPath);

            if (!originFile.exists()) {
                copyFile(sysApkFile, originFile);
            } else {
                PackageInfo apkInfo = getApkPkgInfo(context, originFile);
                if (apkInfo.versionCode != sysApkVer) {
                    copyFile(sysApkFile, originFile);
                }
            }
        }
    }

    public static PackageInfo getApkPkgInfo(Context context, File apkFile) {
        PackageManager pm = context.getPackageManager();
        return pm.getPackageArchiveInfo(apkFile.getAbsolutePath(), PackageManager.GET_META_DATA);
    }

    public static File getTmpApkDir(Context context) {
        File dir = new File(context.getExternalFilesDir(""), "pack");
        if (!dir.exists()) {
            dir.mkdir();
        }
        return dir;
    }

    public static File getApkDir(Context context, String pkg) {
        File dir = new File(context.getExternalFilesDir(""), "pack");
        if (!dir.exists()) {
            dir.mkdir();
        }
        File pkgDir = new File(dir, pkg);
        if (!pkgDir.exists()) {
            pkgDir.mkdir();
        }
        return pkgDir;
    }

    public static File getPatchedFile(Context context, String pkg) {
        File patchedFile = new File(getApkDir(context, pkg), "patched.apk");
        return patchedFile;
    }

    public static File getOriginFile(Context context, String pkg) {
        File originFile = new File(getApkDir(context, pkg), "origin.apk");
        return originFile;
    }

    public static void install(Context context, File apkFile) {
        try {
            Intent install = new Intent(Intent.ACTION_VIEW);
            install.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                Uri contentUri = FileProvider.getUriForFile(
                        context,
                        context.getPackageName() + ".provider",
                        apkFile
                );
                install.setDataAndType(contentUri, "application/vnd.android.package-archive");
            } else {
                install.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
            }
            context.startActivity(install);
        } catch (Exception e) {
            Log.e(TAG, "", e);
        }
    }

    public static void copyFile(File source, File target) throws IOException {
        FileInputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            inputStream = new FileInputStream(source);
            outputStream = new FileOutputStream(target);
            FileChannel iChannel = inputStream.getChannel();
            FileChannel oChannel = outputStream.getChannel();

            ByteBuffer buffer = ByteBuffer.allocate(1024);
            while (true) {
                buffer.clear();
                int r = iChannel.read(buffer);
                if (r == -1)
                    break;
                buffer.limit(buffer.position());
                buffer.position(0);
                oChannel.write(buffer);
            }
        } finally {
            closeQuietly(inputStream);
            closeQuietly(outputStream);
        }
    }

    private static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {
            }
        }
    }

    public static void uninstall(Activity context, String pkgName, int requestCode) {
        try {
            Intent intent = new Intent(Intent.ACTION_UNINSTALL_PACKAGE);
            intent.setData(Uri.fromParts("package", pkgName, null));
            intent.putExtra(Intent.EXTRA_RETURN_RESULT, true);
            intent.putExtra("android.intent.extra.UNINSTALL_ALL_USERS", true);
            context.startActivityForResult(intent, requestCode);
        } catch (Exception e) {
            try {
                Uri uri = Uri.fromParts("package", pkgName, null);
                Intent intent = new Intent(Intent.ACTION_DELETE, uri);
                context.startActivityForResult(intent, requestCode);
            } catch (Exception ex) {
                Log.e(TAG, "", e);
            }
        }
    }
}
