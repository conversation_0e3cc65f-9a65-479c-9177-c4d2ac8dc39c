package com.xyz.host.utils;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.drawable.Icon;
import android.os.Build;
import android.text.TextUtils;

import com.fun.vbox.entity.VAppInfo;
import com.xyz.host.MainApp;
import com.xyz.host.db.DockerBean;

import java.util.Collections;
import java.util.List;

public class ShortcutUtils {
    public interface OnEmitShortcutListener {
        Bitmap getIcon();
        String getName();
    }

    public static boolean createShortcut(DockerBean info, OnEmitShortcutListener listener) {
        if (info == null || listener == null) {
            return false;
        }
        Context context = MainApp.getApp();
        String packageName = info.getPackageName();
        int userId = info.getUserId();

        String name = listener.getName();
        Bitmap icon = listener.getIcon();
        if (TextUtils.isEmpty(name) || icon == null) {
            return false;
        }

        Intent shortcutIntent = wrapperShortcutIntent(info);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ShortcutInfo likeShortcut;
            likeShortcut = new ShortcutInfo.Builder(context, packageName + "@" + userId)
                    .setLongLabel(name)
                    .setShortLabel(name)
                    .setIcon(Icon.createWithBitmap(icon))
                    .setIntent(shortcutIntent)
                    .build();
            ShortcutManager shortcutManager = context.getSystemService(ShortcutManager.class);
            if (shortcutManager != null) {
                try {
                    List<ShortcutInfo> existingShortcuts = shortcutManager.getPinnedShortcuts();
                    boolean shortcutExists = false;
                    for (ShortcutInfo shortcutInfo : existingShortcuts) {
                        if (shortcutInfo.getId().equals(packageName + "@" + userId)) {
                            shortcutExists = true;
                            break;
                        }
                    }
                    if (shortcutExists) {
                        shortcutManager.updateShortcuts(Collections.singletonList(likeShortcut));
                    } else {
                        shortcutManager.requestPinShortcut(likeShortcut,
                                PendingIntent.getActivity(context, packageName.hashCode() + userId, shortcutIntent,
                                        PendingIntent.FLAG_UPDATE_CURRENT).getIntentSender());
                    }
                } catch (Throwable e) {
                    return false;
                }
            }
        } else {
            Intent addIntent = new Intent();
            addIntent.putExtra(Intent.EXTRA_SHORTCUT_INTENT, shortcutIntent);
            addIntent.putExtra(Intent.EXTRA_SHORTCUT_NAME, name);
            addIntent.putExtra(Intent.EXTRA_SHORTCUT_ICON, wrapIcon(icon, 256, 256));
            addIntent.setAction("com.android.launcher.action.INSTALL_SHORTCUT");
            try {
                context.sendBroadcast(addIntent);
            } catch (Throwable e) {
                return false;
            }
        }
        return true;
    }

    private static Intent wrapperShortcutIntent(DockerBean dockerBean) {
        Intent shortcutIntent = new Intent();
        shortcutIntent.addCategory(Intent.CATEGORY_DEFAULT);
        shortcutIntent.setAction(MainApp.getApp().getPackageName() + ".vbox.action.shortcut");
        shortcutIntent.setPackage(MainApp.getApp().getPackageName());
        VAppInfo info = dockerBean.getAppInfo();
        shortcutIntent.putExtra("_VBOX_|_pkg_", info.packageName);
        shortcutIntent.putExtra("_VBOX_|_user_id_", info.userId);
        shortcutIntent.putExtra("_VBOX_|_mode_", info.appMode);
        shortcutIntent.putExtra("_VBOX_|_path_", info.path);
        shortcutIntent.putExtra("_VBOX_|_64bit_", info.is64bit ? 1 : 0);
        return shortcutIntent;
    }

    private static Bitmap wrapIcon(Bitmap bitmap, int newWidth, int newHeight) {
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        if (width < newWidth || height < newHeight) {
            return bitmap;
        }
        float scaleWidth = ((float) newWidth) / (float)width;
        float scaleHeight = ((float) newHeight) / (float)height;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
    }
}
