package com.xyz.host.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.fun.vbox.utils.VLog;
import com.xyz.host.MainApp;

public class NetworkUtils {
    private static final String TAG = "NetworkUtils";
    private static NetworkInfo sNetworkInfo = null;

    public static boolean isNetworkAvailable() {
        NetworkInfo ni = getActiveNetwork();
        if (ni != null && (ni.isConnected() || (ni.isAvailable() && ni.isConnectedOrConnecting()))) {
            return true;
        } else {
            String info = null;
            if (ni != null) {
                info = "network type = " + ni.getType() + ", "
                        + (ni.isAvailable() ? "available" : "inavailable")
                        + ", " + (ni.isConnected() ? "" : "not") + " connected"
                        + ", " + (ni.isConnectedOrConnecting() ? "" : "not") + " isConnectedOrConnecting";
            } else {
                info = "no active network";
            }
            VLog.w(TAG, "isNetworkAvailable network info" + info);
            return false;
        }
    }

    public static NetworkInfo getActiveNetwork() {
        NetworkInfo tmpNetworkInfo = sNetworkInfo;
        if (tmpNetworkInfo == null) {
            tmpNetworkInfo = getActiveNetworkFromService();
        } else if (tmpNetworkInfo.isAvailable() && tmpNetworkInfo.isConnectedOrConnecting()) {
            return tmpNetworkInfo;
        } else {
            tmpNetworkInfo = getActiveNetworkFromService();
        }
        sNetworkInfo = tmpNetworkInfo;
        return tmpNetworkInfo;
    }

    public static NetworkInfo getActiveNetworkFromService() {
        try {
            ConnectivityManager cm = (ConnectivityManager) MainApp.getApp().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null) {
                return cm.getActiveNetworkInfo();
            }
        } catch (Throwable e) {
            VLog.e("NetworkUtils", "error on getActiveNetworkInfo " + e);
        }
        return null;
    }

}
