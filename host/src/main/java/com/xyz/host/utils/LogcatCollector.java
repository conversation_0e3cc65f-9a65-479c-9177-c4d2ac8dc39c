package com.xyz.host.utils;

import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.Nullable;

import com.fun.vbox.utils.BuildCompat;
import com.fun.vbox.utils.SystemPropertiesCompat;
import com.umeng.commonsdk.UMConfigure;
import com.xyz.host.MainApp;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Locale;

public class LogcatCollector {

    private static final String TAG = "LogcatCollector";

    /**
     * 收集多少毫秒之前的logcat日志, 并写入文件
     *
     * @param durationInMillis logcat时长
     * @return Logcat文件
     */
    @Nullable
    public static File collectLogCat(long durationInMillis) {
        String fileName = getLogDirPath() + File.separator + "logcat.txt";
        return collectLogCat(fileName, durationInMillis);
    }

    private static String getLogDirPath() {
        File logDir = MainApp.getApp().getExternalCacheDir();
        if (logDir == null) {
            logDir = MainApp.getApp().getCacheDir();
        }
        if (!logDir.exists()) {
            logDir.mkdir();
        }
        return logDir.getAbsolutePath();
    }

    /**
     * 收集多少毫秒之前的logcat日志, 并写入文件
     *
     * @param filePath         文件路径
     * @param durationInMillis logcat时长
     * @return Logcat文件
     */
    @Nullable
    public static File collectLogCat(String filePath, long durationInMillis) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
            boolean created = file.createNewFile();
            if (created) {
                StringBuilder content = getLogCatSinceTime(System.currentTimeMillis() - durationInMillis);
                writeToFile(filePath, content.toString());
            } else {
                throw new RuntimeException("create logcat file failed");
            }
            return file;
        } catch (Throwable e) {
            Log.e(TAG, "collectLogCat failed: ", e);
            return null;
        }
    }

    /**
     * logcat -b main -b system -b events
     * @param startMillis
     * @return
     */
    private static StringBuilder getLogCatSinceTime(long startMillis) {
        SimpleDateFormat format = new SimpleDateFormat("MM-dd HH:mm:ss.mmm", Locale.CHINA);
        String time = format.format(startMillis);
        Process logcat;
        final StringBuilder log = new StringBuilder();
        String imId = UMConfigure.getUMIDString(MainApp.getApp());
        log.append("imid:").append(imId).append("\r\n");
        String aid = Settings.Secure.getString(MainApp.getApp().getContentResolver(), Settings.Secure.ANDROID_ID);
        log.append("aid:").append(aid).append("\r\n");
        log.append("am:").append(AMapUtils.getPkg()).append("\r\n");
        log.append("版本:").append(CommonUtils.getVerCode()).append("\r\n");
        log.append("手机品牌:").append(Build.BRAND).append("\r\n");
        log.append("手机型号:").append(Build.MODEL).append("\r\n");
        log.append("安卓版本:").append(Build.VERSION.RELEASE).append("\r\n");
        log.append("安卓API:").append(Build.VERSION.SDK_INT).append("\r\n");
        if (BuildCompat.isOhos()) {
            log.append("鸿蒙版本:").append(
                    SystemPropertiesCompat.get("hw_sc.build.platform.version", "")).append("\r\n");
        }
        try {
            String[] cmd = new String[]{"logcat", "*:I", "-t", time};
            logcat = Runtime.getRuntime().exec(cmd);
            BufferedReader br = new BufferedReader(new InputStreamReader(logcat.getInputStream()), 4 * 1024);
            String line;
            String separator = System.getProperty("line.separator");
            while ((line = br.readLine()) != null) {
                log.append(encodeContent(line));
                log.append(separator);
            }
            br.close();
        } catch (Exception e) {
            Log.e(TAG, "getLogCatSinceTime failed", e);
        }
        return log;
    }

    public static String encodeContent(String raw) {
        return raw;
    }

    private static void writeToFile(String fileName, String content) throws IOException {
        PrintWriter pw = new PrintWriter(new FileWriter(fileName));
        pw.write(content);
        pw.close();
    }
}
