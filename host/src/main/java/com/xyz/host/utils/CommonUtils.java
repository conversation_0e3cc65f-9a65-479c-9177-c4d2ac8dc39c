package com.xyz.host.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaScannerConnection;
import android.net.ConnectivityManager;
import android.net.LinkProperties;
import android.net.ProxyInfo;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Pair;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import androidx.core.content.FileProvider;

import com.fun.vbox.utils.FileUtils;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.BuildConfig;
import com.xyz.host.MainApp;
import com.xyz.host.R;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.Locale;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

public class CommonUtils {

    //辅助包 包名
    public static final String LOG_TAG = "DPTest";

    public static byte[] bitmap2byte(Bitmap bitmap) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, baos);
        return baos.toByteArray();
    }

    public static void checkProxySet(Runnable callback) {
        boolean isCheckProxySet = isCheckProxySet();
        if (isCheckProxySet) {
            Pair<Boolean, ProxyInfo> pair = isProxySet();
            if (pair.first) {
                StatAgent.onEvent(MainApp.getApp(), "check_proxy_set", "host", pair.second.getHost());
                boolean isKillProxySet = isKillProxySet();
                if (isKillProxySet) {
                    if (callback != null) {
                        callback.run();
                    }
                }
            }
        }
    }

    private static Boolean sIsKillProxySet = null;

    public static boolean isKillProxySet() {
        if (sIsKillProxySet == null) {
            String strKillProxySet = UMRemoteConfig.getInstance().getConfigValue("kill_proxy_set");
            sIsKillProxySet = Boolean.parseBoolean(strKillProxySet);
        }
        return sIsKillProxySet;
    }

    private static Boolean sIsProxySet = null;

    public static boolean isCheckProxySet() {
        if (sIsProxySet == null) {
            String strCheckProxySet = UMRemoteConfig.getInstance().getConfigValue("check_proxy_set");
            sIsProxySet = Boolean.parseBoolean(strCheckProxySet);
        }
        return sIsProxySet;
    }

    public static Pair<Boolean, ProxyInfo> isProxySet() {
        ConnectivityManager connectivityManager = (ConnectivityManager) MainApp.getApp().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            LinkProperties linkProperties = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                linkProperties = connectivityManager.getLinkProperties(connectivityManager.getActiveNetwork());
            }
            if (linkProperties != null) {
                ProxyInfo proxyInfo = linkProperties.getHttpProxy();
                if (proxyInfo != null) {
                    return new Pair<>(true, proxyInfo);
                }
            }
        }
        return new Pair<>(false, null);
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public static int dip2px(Context context, Float dpValue) {
        float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    public static void dialogDis(Dialog dialog) {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    public static void activityFinish(Activity activity) {
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed()) {
            activity.finish();
        }
    }

    public static boolean isLiveActivity(Activity activity) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return false;
        }
        return true;
    }

    public static String getFormatSize(long size) {
        //这里限定了输入 1T以内 不包括1T
        if (size <= 0 || size >= 1024 * 1024 * 1024) {
            return "0K";
        }

        //这里可能出现转换异常
        double dSize = 0;
        try {
            dSize = size;
        } catch (Exception e) {
            e.printStackTrace();
        }

        //除数因子
        double divideBasic = 1024;

        if (size < 1024) { //1kb以内
            if (size < 1000) {
                return String.valueOf(size + "B");
            } else { //大于1000B,转化为kb,基于用户习惯
                return String.format("%.2f", dSize / divideBasic) + "K";
            }
        } else if (size < 1024 * 1024) { //1M以内
            if (size < 1024 * 1000) {
                return String.format("%.2f", dSize / divideBasic) + "K";
            } else {//大于1000Kb,转化为M
                return String.format("%.2f", dSize / divideBasic / divideBasic) + "M";
            }
        } else { //1TB以内
            if (size < 1024 * 1024 * 1000) {
                return String.format("%.2f", dSize / divideBasic / divideBasic) + "M";
            } else {//大于1000Mb,转化为T
                return String.format("%.2f", dSize / divideBasic / divideBasic / divideBasic) + "T";
            }
        }
    }

    public static void closeQuietly(Closeable c) {
        if (c != null) {
            try {
                c.close();
            } catch (Throwable e) {
                e.getMessage();
            }
        }
    }

    public static Bitmap drawable2Bitmap(Drawable drawable) {
        try {
            if (drawable == null) {
                return null;
            }
            if (drawable instanceof BitmapDrawable) {
                return ((BitmapDrawable) drawable).getBitmap();
            }
            int width = drawable.getIntrinsicWidth();
            width = width > 0 ? width : 1;
            int height = drawable.getIntrinsicHeight();
            height = height > 0 ? height : 1;

            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);

            return bitmap;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 复制文件
     *
     * @param context
     * @param fileName 复制的文件名
     * @param path     保存的目录路径
     * @return
     */
    public static Uri copyAssetsFile(Context context, String fileName, String path) {
        try {
            InputStream mInputStream = context.getAssets().open(fileName);
            File file = new File(path);
            if (!file.exists()) {
                file.mkdir();
            }
            File mFile = new File(path + File.separator + "temp.apk");
            if (!mFile.exists()) {
                mFile.createNewFile();
            }
            FileOutputStream mFileOutputStream = new FileOutputStream(mFile);
            byte[] mbyte = new byte[1024];
            int i = 0;
            while ((i = mInputStream.read(mbyte)) > 0) {
                mFileOutputStream.write(mbyte, 0, i);
            }
            mInputStream.close();
            mFileOutputStream.close();
            Uri uri = null;
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    uri = FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID + ".provider", mFile);
                } else {
                    uri = Uri.fromFile(mFile);
                }
            } catch (ActivityNotFoundException anfe) {
                anfe.printStackTrace();
            }
            MediaScannerConnection.scanFile(context, new String[]{mFile.getAbsolutePath()}, null, null);
            return uri;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 安装apk
     *
     * @param context
     * @param uri     apk存放的路径
     */
    public static void openApk(Context context, Uri uri) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        if (Build.VERSION.SDK_INT >= 24) {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        } else {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.setDataAndType(uri, "application/vnd.android.package-archive");
        context.startActivity(intent);
    }

    /**
     * 手机是32位
     *
     * @return true 是
     */
    public static boolean onlySupported32() {
        String[] supported64BitAbis = Build.SUPPORTED_64_BIT_ABIS;
        return !(supported64BitAbis != null && supported64BitAbis.length > 0);
    }

    public static double doubleFor8(double num) {
        return Double.parseDouble(doubleFor8String(num));
    }

    public static String doubleFor8String(double num) {
        DecimalFormat formater = new DecimalFormat();
        formater.setMaximumFractionDigits(8);
        return formater.format(num);
    }

    public static final void closeSilently(Closeable c) {
        if (c != null) {
            try {
                c.close();
            } catch (Throwable e) {
                e.getMessage();
            }
        }
    }

    public static String getMetaData(Context context, String name) {
        try {
            ApplicationInfo ai = context.getPackageManager().getApplicationInfo(context.getPackageName(),
                    PackageManager.GET_META_DATA);
            return String.valueOf(ai.metaData.get(name));
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return "common";
    }

    public static int getVerCode() {
        PackageManager pm = MainApp.getApp().getPackageManager();
        try {
            PackageInfo info = pm.getPackageInfo(MainApp.getApp().getPackageName(), 0);
            return info.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            // Ignored.
        }
        return -1;
    }

    public static File getDownloadDir() {
        File downDir = MainApp.getApp().getExternalFilesDir("download");
        if (downDir == null) {
            downDir = MainApp.getApp().getCacheDir();
        }
        if (!downDir.exists()) {
            downDir.mkdir();
        }
        return downDir;
    }

    public static void install32Bit() {
        Schedulers.io().scheduleDirect(() -> {
            File file = new File(MainApp.getApp().getFilesDir(), "arm_32.apk");
            try {
                FileUtils.unZip4Asset("x1.sav",
                        MainApp.getApp().getFilesDir().getAbsolutePath());
                install(file);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static void install(File apkFile) {
        Context context = MainApp.getApp();
        File tempApk = new File(getDownloadDir(), apkFile.getName());
        try {
            FileUtils.copyFile(apkFile, tempApk);
            AndroidSchedulers.mainThread().scheduleDirect(() -> {
                Intent install = new Intent(Intent.ACTION_VIEW);
                install.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    Uri contentUri = FileProvider.getUriForFile(
                            context,
                            context.getPackageName() + ".provider",
                            tempApk
                    );
                    install.setDataAndType(contentUri, "application/vnd.android.package-archive");
                } else {
                    install.setDataAndType(Uri.fromFile(tempApk), "application/vnd.android.package-archive");
                }
                context.startActivity(install);
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 隐藏软键盘(只适用于Activity，不适用于Fragment)
     */
    public static void hideSoftKeyboard(Activity activity) {
        View view = activity.getCurrentFocus();
        if (view != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

    /**
     * 隐藏软键盘(可用于Activity，Fragment)
     */
    public static void hideSoftKeyboard(Context context, View focusView) {
        if (focusView == null) return;

        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Activity.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(focusView.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }

    public static String getVersionName(Context context) {
        try {
            return context.getPackageManager().getPackageInfo(context.getPackageName(),
                    PackageManager.GET_CONFIGURATIONS).versionName;
        } catch (Exception e) {
            return "1.0.0";
        }
    }

    public static void share(Activity activity) {
        String shareContent = UMRemoteConfig.getInstance().getConfigValue("share_content");
        if (TextUtils.isEmpty(shareContent)) {
            shareContent = String.format(Locale.getDefault(), "%s\n%s",
                    activity.getString(R.string.share_title),
                    "https://wj.aoqi360.com/yingyu_share.html");
        }

        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_SUBJECT, activity.getString(R.string.share_title));
        intent.putExtra(Intent.EXTRA_TEXT, shareContent);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.putExtra("sms_body", shareContent);
        intent.setType("text/plain");

        activity.startActivity(Intent.createChooser(intent, activity.getString(R.string.invite_friends)));
    }

    public static void shareWithInvite(Activity activity, String inviteCode) {
        String shareContent = UMRemoteConfig.getInstance().getConfigValue("share_content");
        if (TextUtils.isEmpty(shareContent)) {
            shareContent = activity
                    .getString(R.string.share_title, "https://wj.aoqi360.com/yingyu_share.html", inviteCode);
        }

        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_SUBJECT, activity.getString(R.string.share_title));
        intent.putExtra(Intent.EXTRA_TEXT, shareContent);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.putExtra("sms_body", shareContent);
        intent.setType("text/plain");

        activity.startActivity(Intent.createChooser(intent, activity.getString(R.string.invite_friends)));
    }

}
