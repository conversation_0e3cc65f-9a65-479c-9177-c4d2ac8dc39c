package com.xyz.host.utils;

import android.app.Notification;
import android.app.Service;

import com.xyz.host.MainApp;
import com.xyz.host.sp.AppSharePref;

public class ReplaceDaemonServiceSF {

    public static void startForeground(Service service, int i, Notification notification) {
        try {
            service.startForeground(i, notification);
            if(isHuaWeiChannel()){
                service.stopForeground(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isHuaWeiChannel() {
        return AppSharePref.CHANNEL_HUAWEI.equals(CommonUtils.getMetaData(MainApp.getApp(), AppSharePref.UMENG_CHANNEL));
    }

}
