package com.xyz.host.utils;

import android.content.Context;
import android.content.ContextWrapper;
import android.text.TextUtils;

import com.xyz.host.MainApp;
import com.xyz.host.model.MapSdkConfig;

public class AMapUtils {
    public static void setPkg() {
        String pkg = MapSdkConfig.getConfig().p;
        if (TextUtils.isEmpty(pkg)) {
            return;
        }
        com.loc.l.a(pkg);
        MyContext myContext = new MyContext(MainApp.getApp(), pkg);
        com.amap.api.mapcore2d.cq.c(myContext);
        com.amap.api.col.s.bi.c(myContext);
    }

    private static class MyContext extends ContextWrapper {
        private final String mPkg;

        public MyContext(Context base, String pkg) {
            super(base);
            mPkg = pkg;
        }

        @Override
        public String getPackageName() {
            return mPkg;
        }
    }

    public static String getApiKey() {
        return MapSdkConfig.getConfig().a;
    }

    public static String getSignAndPkg(Context context) {
        return MapSdkConfig.getConfig().s;
    }

    public static String getPkg() {
        return MapSdkConfig.getConfig().p;
    }
}
