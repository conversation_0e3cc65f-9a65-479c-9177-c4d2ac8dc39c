package com.xyz.host.utils;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Context;
import android.net.wifi.WifiInfo;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Base64;

import com.xyz.host.MainApp;
import com.xyz.host.sp.AppSharePref;

import java.net.NetworkInterface;
import java.net.SocketException;

public class TelephonyCache {
    private static final String TAG = "TelephonyCache";

    private static final String ANDROID_ID_ERROR = "0000000000000000";

    private volatile static String sDeviceId;
    private volatile static String sSubscriberId;
    private volatile static String sAndroidId;
    private volatile static String sSimSerialNumber;
    private volatile static String sImei;
    private volatile static boolean sHasGetSubscriberId;

    private static final Object sMacLock = new Object();
    private volatile static String sMacAddress;
    private volatile static byte[] sHardwareAddress;
    private static final Object sBSSIDLock = new Object();
    private volatile static String sBSSID;

    public static synchronized void init() {
        try {
            TelephonyManager sTelephonyManager =
                    (TelephonyManager) MainApp.getApp()
                            .getSystemService(Context.TELEPHONY_SERVICE);
            setDeviceId(sTelephonyManager);
            setSubscriberId(sTelephonyManager);
            setSimSerialNumber(sTelephonyManager);
            setImei(sTelephonyManager);
            ContentResolver contentResolver
                    = MainApp.getApp().getContentResolver();
            setAndroidId(contentResolver, android.provider.Settings.Secure.ANDROID_ID);
        } catch (Throwable e) {
            // Ignored.
        }
    }

    public static String getDeviceId(TelephonyManager telephonyManager) {
        if (TextUtils.isEmpty(sDeviceId)) {
            synchronized (TelephonyCache.class) {
                if (TextUtils.isEmpty(sDeviceId)) {
                    setDeviceId(telephonyManager);
                }
            }
        }
        return sDeviceId;
    }

    public static String getSubscriberId(TelephonyManager telephonyManager) {
        if (!sHasGetSubscriberId && TextUtils.isEmpty(sSubscriberId)) {
            synchronized (TelephonyCache.class) {
                if (!sHasGetSubscriberId && TextUtils.isEmpty(sSubscriberId)) {
                    setSubscriberId(telephonyManager);
                }
            }
        }
        return sSubscriberId;
    }

    public static String getString(ContentResolver resolver, String name) {
        if (!android.provider.Settings.Secure.ANDROID_ID.equals(name)) {
            return android.provider.Settings.Secure.getString(
                    resolver,
                    name);
        }
        if (TextUtils.isEmpty(sAndroidId)) {
            synchronized (TelephonyCache.class) {
                if (TextUtils.isEmpty(sAndroidId)) {
                    setAndroidId(resolver, name);
                }
            }
        }
        return sAndroidId;
    }

    public static String getSimSerialNumber(TelephonyManager telephonyManager) {
        if (TextUtils.isEmpty(sSimSerialNumber)) {
            synchronized (TelephonyCache.class) {
                if (TextUtils.isEmpty(sSimSerialNumber)) {
                    setSimSerialNumber(telephonyManager);
                }
            }
        }
        return sSimSerialNumber;
    }

    public static String getImei(TelephonyManager telephonyManager) {
        if (TextUtils.isEmpty(sImei)) {
            synchronized (TelephonyCache.class) {
                if (TextUtils.isEmpty(sImei)) {
                    setImei(telephonyManager);
                }
            }
        }
        return sImei;
    }

    public static String getMacAddress(WifiInfo info) {
        if (TextUtils.isEmpty(sMacAddress)) {
            synchronized (sMacLock) {
                if (TextUtils.isEmpty(sMacAddress)) {
                    setMacAddress(info);
                }
            }
        }
        return sMacAddress;
    }

    public static byte[] getHardwareAddress(
            NetworkInterface networkInterface) throws SocketException {
        if (sHardwareAddress == null) {
            synchronized (sMacLock) {
                if (sHardwareAddress == null) {
                    setMacAddress(networkInterface);
                }
            }
        }
        return sHardwareAddress;
    }

    public static String getBSSID(WifiInfo info) {
        if (TextUtils.isEmpty(sBSSID)) {
            synchronized (sBSSIDLock) {
                if (TextUtils.isEmpty(sBSSID)) {
                    sBSSID = info.getBSSID();
                }
            }
        }
        return sBSSID;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    // 特殊处理一些反射获取IMEI及IMSI

    // 处理com/umeng/commonsdk/statistics/common/DeviceConfig
    // public static java.lang.String getImeiNew(android.content.Context r7)
    public static String getImei(Context context) {
        return sImei == null ? "" : sImei;
    }

    // 处理com/umeng/commonsdk/statistics/common/DeviceConfig
    // public static java.lang.String getImeiNew(android.content.Context r7)
    public static String getImei() {
        return sImei == null ? "0" : sImei;
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String d(Object obj)
    public static String getSubscriberId(Object obj1, Object obj2) {
        return sSubscriberId == null ? "" : sSubscriberId;
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String d(Object obj, int i2)
    public static String getSubscriberId(Object obj1, Object obj2, int a) {
        return sSubscriberId == null ? "" : sSubscriberId;
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String a(Object obj)
    // private String c(Object obj)
    public static String getImei(Object obj1, Object obj2) {
        return sImei == null ? "" : sImei;
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String a(Object obj, int i2)
    public static String getImei(Object obj1, Object obj2, int a) {
        return sImei == null ? "" : sImei;
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String i()
    public static String getMacAddress4Mob(Object who) {
        return "";
    }

    // 处理com/mob/tools/utils/DeviceHelper
    // private String j()
    public static String getMacAddress4Mob2(Object who) {
        return sMacAddress == null ? "" : sMacAddress;
    }

    // 处理com/cmic/sso/sdk/a/b
    // private Object a(Object obj, String str, Object[] objArr, Class[] clsArr)
    public static Object invoke(Object who, Object obj, String str, Object[] objArr, Class<?>[] clsArr) {
        if (!TextUtils.isEmpty(str) && str.equals("getSubscriberId")) {
            return sSubscriberId == null ? "" : sSubscriberId;
        }
        try {
            Class<?> cls = Class.forName(obj.getClass().getName());
            return (objArr == null || clsArr == null)
                    ? cls.getMethod(str, new Class[0]).invoke(obj)
                    : cls.getMethod(str, clsArr).invoke(obj, objArr);
        } catch (Exception e) {
            return "";
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////

    @SuppressLint("MissingPermission")
    private static void setImei(TelephonyManager sTelephonyManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            sImei = AppSharePref.get().getString("vbox_prv_si", "");
            if (TextUtils.isEmpty(sImei)) {
                sImei = sTelephonyManager.getImei();
                if (!TextUtils.isEmpty(sImei)) {
                    AppSharePref.get().putString("vbox_prv_si", sImei);
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private static void setSimSerialNumber(TelephonyManager sTelephonyManager) {
        sSimSerialNumber = AppSharePref.get().getString("vbox_prv_sn", "");
        if (TextUtils.isEmpty(sSimSerialNumber)) {
            sSimSerialNumber = sTelephonyManager.getSimSerialNumber();
            if (!TextUtils.isEmpty(sSimSerialNumber)) {
                AppSharePref.get().putString("vbox_prv_sn", sSimSerialNumber);
            }
        }
    }

    @SuppressLint("MissingPermission")
    private static void setSubscriberId(TelephonyManager sTelephonyManager) {
        sSubscriberId = AppSharePref.get().getString("vbox_prv_is", "");
        if (TextUtils.isEmpty(sSubscriberId)) {
            sSubscriberId = sTelephonyManager.getSubscriberId();
            sHasGetSubscriberId = true;
            if (TextUtils.isEmpty(sSubscriberId)) {
                AppSharePref.get().putString("vbox_prv_is", "0");
            } else {
                AppSharePref.get().putString("vbox_prv_is", sSubscriberId);
            }
        }
    }

    @SuppressLint("MissingPermission")
    private static void setDeviceId(TelephonyManager sTelephonyManager) {
        sDeviceId = AppSharePref.get().getString("vbox_prv_did", "");
        if (TextUtils.isEmpty(sDeviceId)) {
            sDeviceId = sTelephonyManager.getDeviceId();
            if (!TextUtils.isEmpty(sDeviceId)) {
                AppSharePref.get().putString("vbox_prv_did", sDeviceId);
            }
        }
    }

    private static void setAndroidId(ContentResolver resolver, String name) {
        sAndroidId = AppSharePref.get().getString("vbox_prv_aid", "");
        if (TextUtils.isEmpty(sAndroidId) || ANDROID_ID_ERROR.equals(sAndroidId)) {
            sAndroidId = android.provider.Settings.Secure.getString(
                    resolver,
                    name);
            if (!TextUtils.isEmpty(sAndroidId) && !ANDROID_ID_ERROR.equals(sAndroidId)) {
                AppSharePref.get().putString("vbox_prv_aid", sAndroidId);
            }
        }
    }

    @SuppressLint("MissingPermission")
    private static void setMacAddress(WifiInfo wifiInfo) {
        sMacAddress = AppSharePref.get().getString("vbox_prv_mac", "");
        if (TextUtils.isEmpty(sMacAddress)) {
            sMacAddress = wifiInfo.getMacAddress();
            if (!TextUtils.isEmpty(sMacAddress)) {
                AppSharePref.get().putString("vbox_prv_mac", sMacAddress);
            }
        }
    }

    private static void setMacAddress(NetworkInterface networkInterface) throws SocketException {
        String mac2 = AppSharePref.get().getString("vbox_prv_mac2", "");
        if (TextUtils.isEmpty(mac2)) {
            sHardwareAddress = networkInterface.getHardwareAddress();
            String strMac = new String(Base64.encode(sHardwareAddress, Base64.DEFAULT));
            if (!TextUtils.isEmpty(strMac)) {
                AppSharePref.get().putString("vbox_prv_mac2", strMac);
            }
        } else {
            sHardwareAddress = Base64.decode(mac2.getBytes(), Base64.DEFAULT);
        }
    }
}
