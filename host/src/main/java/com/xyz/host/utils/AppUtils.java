package com.xyz.host.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.fun.vbox.client.core.VCore;
import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.model.VirtualPosCfg;
import com.xyz.host.sp.AppSharePref;
import com.xyz.host.user.engine.UserAgent;

public class AppUtils {

    public static boolean showVirtualLocate(Context context, String pkName) {
        if (VCore.get().isDebuggable()) {
            return true;
        }

        String configName = AppSharePref.CONFIG_FUNCTION;
        SharedPreferences sharedPrefs = context.getSharedPreferences("vbox", Context.MODE_MULTI_PROCESS);
        if (sharedPrefs.getBoolean("loc_flag", false)) {
            configName = AppSharePref.CONFIG_FUNCTION_EXT;
        }
        String channel = CommonUtils.getMetaData(context, "UMENG_CHANNEL");
        if ("yingyongbao".equals(channel)) {
            configName = AppSharePref.CONFIG_FUNCTION_EXT;
        }

        String strConfig = UMRemoteConfig.getInstance().getConfigValue(configName);
        VirtualPosCfg virtualPosition = GsonUtils.gson2Bean(strConfig, VirtualPosCfg.class);
        boolean isTrueVip = UserAgent.getInstance().isVipUser();
        if (virtualPosition == null) {
            return isTrueVip;
        } else {
            boolean exclude = virtualPosition.exclude.contains(pkName);
            if (exclude) {
                return false;
            } else if (isTrueVip) {
                return virtualPosition.vip;
            } else {
                return virtualPosition.def;
            }
        }
    }
}
