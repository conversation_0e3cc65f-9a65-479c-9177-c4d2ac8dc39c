package com.yingyu.zh.multiapp.model;

import android.graphics.Bitmap;
import com.xyz.host.db.DockerBean;

/**
 * 多开应用项数据模型
 */
public class AppItem {
    private String appName;
    private String packageName;
    private Bitmap appIcon;
    private boolean isNew; // 是否为新应用（显示绿点）
    private String instanceName; // 实例名称，如"小红书1"、"抖音2"
    private DockerBean dockerBean; // 关联的DockerBean对象，用于启动应用

    public AppItem(String appName, String packageName, Bitmap appIcon, boolean isNew, String instanceName) {
        this.appName = appName;
        this.packageName = packageName;
        this.appIcon = appIcon;
        this.isNew = isNew;
        this.instanceName = instanceName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Bitmap getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(Bitmap appIcon) {
        this.appIcon = appIcon;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean isNew) {
        this.isNew = isNew;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public DockerBean getDockerBean() {
        return dockerBean;
    }

    public void setDockerBean(DockerBean dockerBean) {
        this.dockerBean = dockerBean;
    }
}