package com.yingyu.zh.multiapp.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 体验券接口响应数据模型
 */
@Keep
public class TicketsResponse {
    @SerializedName("tickets")
    private List<CouponItem> tickets;
    
    @SerializedName("total_count")
    private int totalCount;
    
    @SerializedName("expired_cleaned")
    private int expiredCleaned;

    public List<CouponItem> getTickets() {
        return tickets;
    }

    public void setTickets(List<CouponItem> tickets) {
        this.tickets = tickets;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getExpiredCleaned() {
        return expiredCleaned;
    }

    public void setExpiredCleaned(int expiredCleaned) {
        this.expiredCleaned = expiredCleaned;
    }
}
