package com.yingyu.zh.multiapp;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.model.PayEvent;
import com.xyz.host.user.engine.UserAgent;
import com.yingyu.zh.multiapp.adapter.CouponAdapter;
import com.yingyu.zh.multiapp.model.CouponItem;
import com.yingyu.zh.multiapp.model.TicketsResponse;
import com.yingyu.zh.multiapp.model.UseTicketResponse;
import com.xyz.host.BuildConfig;
import com.xyz.host.R;
import com.xyz.host.user.engine.ApiCallback;
import com.xyz.host.user.engine.ApiServiceDelegate;
import com.xyz.host.utils.GsonUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 优惠券列表页面
 */
public class CouponListActivity extends AppCompatActivity implements View.OnClickListener {

    private RecyclerView rvCouponList;
    private LinearLayout llEmptyState;
    private LinearLayout llLoadingState;
    private TextView tvBottomTip;
    private CouponAdapter couponAdapter;
    private List<CouponItem> couponList;

    public static void start(Context context) {
        Intent intent = new Intent(context, CouponListActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置沉浸式状态栏
        setupImmersiveStatusBar();

        setContentView(R.layout.activity_coupon_list);

        initViews();
        initData();
        loadCouponsFromServer();
    }

    /**
     * 设置沉浸式状态栏
     */
    private void setupImmersiveStatusBar() {
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(ContextCompat.getColor(this, R.color.purple_700));
        
        // 设置状态栏文字为白色
        window.getDecorView().setSystemUiVisibility(0);
    }

    private void initViews() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(this);

        // 初始化视图
        rvCouponList = findViewById(R.id.rv_coupon_list);
        llEmptyState = findViewById(R.id.ll_empty_state);
        llLoadingState = findViewById(R.id.ll_loading_state);
        tvBottomTip = findViewById(R.id.tv_bottom_tip);
    }

    private void initData() {
        // 初始化优惠券列表
        couponList = new ArrayList<>();

        // 设置RecyclerView
        rvCouponList.setLayoutManager(new LinearLayoutManager(this));
        couponAdapter = new CouponAdapter(this, couponList);
        couponAdapter.setOnCouponClickListener(this::onCouponClick);
        couponAdapter.setOnCouponUseListener(this::onCouponUse);
        rvCouponList.setAdapter(couponAdapter);
    }

    /**
     * 从服务器加载优惠券数据
     */
    private void loadCouponsFromServer() {
        showLoadingState();

        new ApiServiceDelegate().getTickets(new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里记录请求参数用于调试
            }

            @Override
            public void onSuccess(String result) {
                try {
                    // 使用Gson直接反序列化整个响应
                    TicketsResponse response = GsonUtils.gson2Bean(result, TicketsResponse.class);

                    List<CouponItem> newCouponList = new ArrayList<>();
                    if (response != null && response.getTickets() != null) {
                        newCouponList = response.getTickets();
                    }

                    updateCouponList(newCouponList);

                } catch (Exception e) {
                    showEmptyState();
                    Toast.makeText(CouponListActivity.this, "数据解析失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFail(String error) {
                // 在开发阶段，如果API调用失败，显示测试数据
                if (BuildConfig.DEBUG) {
                    Toast.makeText(CouponListActivity.this, "API调用失败，显示测试数据", Toast.LENGTH_SHORT).show();
                    List<CouponItem> testData = createTestData();
                    updateCouponList(testData);
                } else {
                    showEmptyState();
                    Toast.makeText(CouponListActivity.this, "加载失败: " + error, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 创建测试数据（仅用于开发调试）
     */
    private List<CouponItem> createTestData() {
        // 使用JSON字符串模拟真实API响应，测试Gson反序列化
        String testJson = "{\n" +
                "  \"tickets\": [\n" +
                "    {\n" +
                "      \"ticket_id\": 123,\n" +
                "      \"ticket_uuid\": \"abc123-def456-ghi789\",\n" +
                "      \"type_id\": \"day\",\n" +
                "      \"type_name\": \"1天体验券\",\n" +
                "      \"duration_hours\": 24,\n" +
                "      \"source\": \"invite_reward\",\n" +
                "      \"source_display\": \"被邀请奖励\",\n" +
                "      \"expire_time\": \"2025-08-15 10:30:00\",\n" +
                "      \"is_expired\": false\n" +
                "    },\n" +
                "    {\n" +
                "      \"ticket_id\": 124,\n" +
                "      \"ticket_uuid\": \"def789-ghi012-jkl345\",\n" +
                "      \"type_id\": \"day\",\n" +
                "      \"type_name\": \"1天体验券\",\n" +
                "      \"duration_hours\": 24,\n" +
                "      \"source\": \"inviter_reward\",\n" +
                "      \"source_display\": \"邀请他人奖励\",\n" +
                "      \"expire_time\": \"2025-08-20 15:45:00\",\n" +
                "      \"is_expired\": false\n" +
                "    },\n" +
                "    {\n" +
                "      \"ticket_id\": 125,\n" +
                "      \"ticket_uuid\": \"ghi345-jkl678-mno901\",\n" +
                "      \"type_id\": \"hour\",\n" +
                "      \"type_name\": \"1小时体验券\",\n" +
                "      \"duration_hours\": 1,\n" +
                "      \"source\": \"admin\",\n" +
                "      \"source_display\": \"管理员发放\",\n" +
                "      \"expire_time\": \"2025-08-25 09:15:00\",\n" +
                "      \"is_expired\": false\n" +
                "    }\n" +
                "  ],\n" +
                "  \"total_count\": 3,\n" +
                "  \"expired_cleaned\": 1\n" +
                "}";

        try {
            TicketsResponse response = GsonUtils.gson2Bean(testJson, TicketsResponse.class);
            if (response != null && response.getTickets() != null) {
                // 设置第二个优惠券为已使用状态（模拟本地状态）
                List<CouponItem> tickets = response.getTickets();
                if (tickets.size() > 1) {
                    tickets.get(1).setUsed(true);
                }
                return tickets;
            }
        } catch (Exception e) {
            Toast.makeText(this, "测试数据解析失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }

        return new ArrayList<>();
    }

    /**
     * 更新优惠券列表
     */
    private void updateCouponList(List<CouponItem> newCouponList) {
        couponList.clear();
        couponList.addAll(newCouponList);
        couponAdapter.updateData(couponList);

        if (couponList.isEmpty()) {
            showEmptyState();
        } else {
            showCouponList();
            // 显示底部提示
            tvBottomTip.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        llLoadingState.setVisibility(View.VISIBLE);
        llEmptyState.setVisibility(View.GONE);
        rvCouponList.setVisibility(View.GONE);
        tvBottomTip.setVisibility(View.GONE);
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        llLoadingState.setVisibility(View.GONE);
        llEmptyState.setVisibility(View.VISIBLE);
        rvCouponList.setVisibility(View.GONE);
        tvBottomTip.setVisibility(View.GONE);
    }

    /**
     * 显示优惠券列表
     */
    private void showCouponList() {
        llLoadingState.setVisibility(View.GONE);
        llEmptyState.setVisibility(View.GONE);
        rvCouponList.setVisibility(View.VISIBLE);
    }

    /**
     * 优惠券点击事件
     */
    private void onCouponClick(CouponItem coupon, int position) {
        // 点击整个优惠券项目的处理逻辑（如果需要的话）
        // 目前暂时不处理
    }

    /**
     * 使用优惠券事件
     */
    private void onCouponUse(CouponItem coupon, int position) {
        if (!coupon.canUse()) {
            return;
        }

        // 显示确认对话框
        showUseTicketConfirmDialog(coupon, position);
    }

    /**
     * 显示使用体验券确认对话框
     */
    private void showUseTicketConfirmDialog(CouponItem coupon, int position) {
        String message = String.format("确定要使用这张%s吗？\n使用后将延长VIP %s",
                coupon.getTypeName(),
                coupon.getDurationText());

        new android.app.AlertDialog.Builder(this)
                .setTitle("使用体验券")
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> {
                    useTicket(coupon, position);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 调用API使用体验券
     */
    private void useTicket(CouponItem coupon, int position) {
        // 显示加载状态
        Toast.makeText(this, "正在使用体验券...", Toast.LENGTH_SHORT).show();

        new ApiServiceDelegate().useTicket(coupon.getTicketId(), new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里记录请求参数用于调试
            }

            @Override
            public void onSuccess(String result) {
                try {
                    UseTicketResponse response = GsonUtils.gson2Bean(result, UseTicketResponse.class);

                    if (response != null && response.isSuccess()) {
                        // 使用成功
                        handleUseTicketSuccess(response, coupon, position);
                    } else {
                        // 使用失败
                        String errorMsg = response != null ? response.getMessage() : "使用失败";
                        Toast.makeText(CouponListActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    Toast.makeText(CouponListActivity.this, "数据解析失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFail(String error) {
                // 在开发阶段，模拟成功响应用于测试
                if (BuildConfig.DEBUG) {
                    Toast.makeText(CouponListActivity.this, "API调用失败，模拟成功响应", Toast.LENGTH_SHORT).show();

                    // 创建模拟的成功响应
                    UseTicketResponse mockResponse = new UseTicketResponse();
                    mockResponse.setSuccess(true);
                    mockResponse.setMessage("体验券使用成功");
                    mockResponse.setTicketId(coupon.getTicketId());
                    mockResponse.setTicketUuid(coupon.getTicketUuid());
                    mockResponse.setVipExpireTime("2024/04/28 24:00:00");
                    mockResponse.setExtendedHours(coupon.getDurationHours());

                    handleUseTicketSuccess(mockResponse, coupon, position);
                } else {
                    Toast.makeText(CouponListActivity.this, "使用失败: " + error, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 处理使用体验券成功的响应
     */
    private void handleUseTicketSuccess(UseTicketResponse response, CouponItem coupon, int position) {
        // 显示成功消息
        String successMsg = String.format("体验券使用成功！\n券号: %d\nVIP延长至: %s\n延长时长: %d小时",
                response.getTicketId(),
                response.getVipExpireTime(),
                response.getExtendedHours());

        new android.app.AlertDialog.Builder(this)
                .setTitle("使用成功")
                .setMessage(successMsg)
                .setPositiveButton("确定", null)
                .show();

        // 更新本地数据
        coupon.setUsed(true);

        // 刷新列表
        couponAdapter.notifyItemChanged(position);

        // 可选：重新加载整个列表以获取最新数据
        // loadCouponsFromServer();
        updateUserInfo();
    }

    private void updateUserInfo() {
        new ApiServiceDelegate().user(UserAgent.getInstance().getMobile(), new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 处理请求参数
            }

            @Override
            public void onSuccess(String result) {
                EventBus.getDefault().post(UserAgent.getInstance().getUserInfo());
            }

            @Override
            public void onFail(String error) {
                // 处理失败情况
            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.iv_back) {
            finish();
        }
    }
}
