package com.yingyu.zh.multiapp;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import androidx.fragment.app.FragmentTransaction;

import com.umeng.cconfig.UMRemoteConfig;
import com.xyz.host.R;
import com.xyz.host.ads.TtFullScreenAd;
import com.xyz.host.home.MyFragment;
import com.xyz.host.home.add.AddAppActivity;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.utils.StatAgent;

import androidx.annotation.Nullable;

/**
 * 个人中心Activity - 使用MyFragment
 */
public class MyActivity extends AppCompatActivity {

    private static final int REQUEST_ADD_APP = 100;

    public static void start(Context context) {
        Intent intent = new Intent(context, MyActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_my);

        initViews();
        loadMyFragment();
    }



    private void initViews() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(v -> finish());
    }

    private void loadMyFragment() {
        // 加载MyFragment
        MyFragment myFragment = MyFragment.newInstance();
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.fragment_container, myFragment);
        transaction.commit();
    }

    /**
     * 处理隐藏应用结果（由MyFragment调用）
     */
    public void handleHideAppResult(@Nullable Intent data) {
        if (data != null) {
            forwardResultToMultiAppActivity(data);
        }
    }



    /**
     * 将onActivityResult结果转发给MultiAppActivity处理
     */
    private void forwardResultToMultiAppActivity(@Nullable Intent data) {
        if (data != null) {
            // 创建Bundle来传递结果数据
            Bundle resultBundle = new Bundle();
            resultBundle.putAll(data.getExtras()); // 复制所有extras

            // 启动MultiAppActivity并传递Bundle
            Intent intent = new Intent(this, MultiAppActivity.class);
            intent.putExtra("forward_result", true);
            intent.putExtra("result_bundle", resultBundle);
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
            startActivity(intent);

            // 显示提示信息
            Toast.makeText(this, "应用隐藏操作完成，正在跳转到主页面", Toast.LENGTH_SHORT).show();

            // 可选：关闭当前页面
            // finish();
        }
    }

}
