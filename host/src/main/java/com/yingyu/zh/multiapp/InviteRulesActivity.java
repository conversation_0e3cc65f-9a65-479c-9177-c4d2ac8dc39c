package com.yingyu.zh.multiapp;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;


import com.xyz.host.R;

/**
 * 邀请规则页面
 */
public class InviteRulesActivity extends AppCompatActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, InviteRulesActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_invite_rules);

        initViews();
    }



    private void initViews() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(v -> finish());
        
        // 设置标题
        TextView tvTitle = findViewById(R.id.tv_title);
        tvTitle.setText("邀请规则");
        
        // 设置规则内容
        TextView tvRulesContent = findViewById(R.id.tv_rules_content);
        String rulesContent = buildRulesContent();
        tvRulesContent.setText(rulesContent);
    }

    /**
     * 构建规则内容
     */
    private String buildRulesContent() {
        StringBuilder sb = new StringBuilder();
        
        sb.append("邀请规则\n\n");
        
        sb.append("1. 被邀请的好友必须是新用户（之前未下载过无界多开），只有新用户才能填写邀请码。\n\n");
        
        sb.append("2. 被邀请的用户只能填写一次邀请码。\n\n");
        
        sb.append("3. 每邀请1人可获得一张1天体验券。\n\n");
        
        sb.append("4. 被邀请人可获得一张1天体验券。\n\n");
        
        sb.append("*本次活动在法律允许的范围内，最终解释权归我司所有。");
        
        return sb.toString();
    }
}
