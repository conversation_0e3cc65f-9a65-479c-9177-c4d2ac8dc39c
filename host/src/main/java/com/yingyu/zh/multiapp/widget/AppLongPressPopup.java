package com.yingyu.zh.multiapp.widget;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import com.xyz.host.R;
import com.xyz.host.utils.StatAgent;
import com.yingyu.zh.multiapp.model.AppItem;
import com.umeng.cconfig.UMRemoteConfig;

/**
 * iPhone风格的应用长按弹出菜单
 */
public class AppLongPressPopup extends PopupWindow {
    
    private Context context;
    private View contentView;
    private LinearLayout llDeleteApp;
    private LinearLayout llAddToDesktop;
    private LinearLayout llSetName;
    private LinearLayout llMoreSettings;
    private OnMenuItemClickListener listener;
    private OnTouchEventListener touchEventListener;
    
    public interface OnMenuItemClickListener {
        void onDeleteApp(AppItem appItem);
        void onAddToDesktop(AppItem appItem);
        void onSetName(AppItem appItem);
        void onMoreSettings(AppItem appItem);
        void onDismiss(); // 弹窗关闭回调
    }

    public interface OnTouchEventListener {
        boolean onTouchEvent(MotionEvent event);
    }
    
    public AppLongPressPopup(Context context) {
        super(context);
        this.context = context;
        initView();
        initPopup();
    }
    
    private void initView() {
        contentView = LayoutInflater.from(context).inflate(R.layout.popup_app_menu, null);
        llDeleteApp = contentView.findViewById(R.id.ll_delete_app);
        llAddToDesktop = contentView.findViewById(R.id.ll_add_to_desktop);
        llSetName = contentView.findViewById(R.id.ll_set_name);
        llMoreSettings = contentView.findViewById(R.id.ll_more_settings);

        setContentView(contentView);
    }
    
    private void initPopup() {
        // 设置宽度为屏幕宽度的一半
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
        setWidth(screenWidth / 2);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);

        // 恢复原来的设置，但添加触摸监听
        setFocusable(true);
        setOutsideTouchable(true);
        setBackgroundDrawable(new ColorDrawable(0x00000000)); // 透明背景

        // 设置动画
        setAnimationStyle(R.style.PopupMenuAnimation);

        // 设置关闭监听
        setOnDismissListener(() -> {
            if (listener != null) {
                listener.onDismiss();
            }
        });

        // 设置触摸监听器，将触摸事件传递给Activity
        setTouchInterceptor((v, event) -> {
            if (touchEventListener != null) {
                return touchEventListener.onTouchEvent(event);
            }
            return false;
        });
    }
    
    public void setOnMenuItemClickListener(OnMenuItemClickListener listener) {
        this.listener = listener;
    }

    public void setOnTouchEventListener(OnTouchEventListener listener) {
        this.touchEventListener = listener;
    }
    
    public void showAtLocation(View anchor, AppItem appItem, int columnPosition) {
        StatAgent.onEvent(context, "app_funcpage", "name", appItem.getAppName());

        // 根据审核开关控制设置名称功能的显示隐藏
        boolean isReviewMode = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("review_mode"));
        if (isReviewMode) {
            // 审核模式：隐藏设置名称功能
            llSetName.setVisibility(View.GONE);
            // 同时隐藏对应的分割线（设置名称上方的分割线）
            View setNameDivider = contentView.findViewById(R.id.divider_set_name);
            if (setNameDivider != null) {
                setNameDivider.setVisibility(View.GONE);
            }
        } else {
            // 正常模式：显示设置名称功能
            llSetName.setVisibility(View.VISIBLE);
            // 同时显示对应的分割线
            View setNameDivider = contentView.findViewById(R.id.divider_set_name);
            if (setNameDivider != null) {
                setNameDivider.setVisibility(View.VISIBLE);
            }
        }

        if (listener != null) {
            llDeleteApp.setOnClickListener(v -> {
                listener.onDeleteApp(appItem);
                dismiss();
            });

            llAddToDesktop.setOnClickListener(v -> {
                listener.onAddToDesktop(appItem);
                dismiss();
            });

            llSetName.setOnClickListener(v -> {
                listener.onSetName(appItem);
                dismiss();
            });

            llMoreSettings.setOnClickListener(v -> {
                listener.onMoreSettings(appItem);
                dismiss();
            });
        }

        // 获取弹窗大小（已在initPopup中设置为屏幕宽度的一半）
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
        int popupWidth = screenWidth / 2;

        // 先测量弹窗高度
        contentView.measure(View.MeasureSpec.makeMeasureSpec(popupWidth, View.MeasureSpec.EXACTLY),
                           View.MeasureSpec.UNSPECIFIED);
        int popupHeight = contentView.getMeasuredHeight();

        // 计算显示位置
        int[] location = new int[2];
        anchor.getLocationOnScreen(location);

        // 转换5dp为像素
        int offsetDp = (int) (5 * context.getResources().getDisplayMetrics().density);

        int x, y;

        // 根据列位置决定对齐方式
        if (columnPosition <= 1) {
            // 前两个图标：弹窗左边与图标左边对齐
            x = location[0];
        } else {
            // 后两个图标：弹窗右边与图标右边对齐
            x = location[0] + anchor.getWidth() - popupWidth;
        }

        // Y坐标：图标上方5dp
        y = location[1] - popupHeight - offsetDp;

        // 确保不超出屏幕边界
        if (x < 0) x = 0;
        if (x + popupWidth > screenWidth) x = screenWidth - popupWidth;
        if (y < 0) y = location[1] + anchor.getHeight() + offsetDp; // 如果上方空间不够，显示在下方

        showAtLocation(anchor, Gravity.NO_GRAVITY, x, y);
    }
    
    /**
     * 添加背景模糊效果
     */
    public void showWithBlur(View anchor, AppItem appItem, int columnPosition) {
        // 先显示菜单
        showAtLocation(anchor, appItem, columnPosition);

        // 添加背景模糊效果（可选）
        // 这里可以添加对背景的模糊处理
    }
}
