package com.yingyu.zh.multiapp.helper;

import android.graphics.Canvas;
import android.os.Vibrator;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;
import com.yingyu.zh.multiapp.adapter.AppGridAdapter;

/**
 * 应用拖拽辅助类
 * 处理应用图标的拖拽逻辑，包括拖拽开始、移动、结束的回调
 */
public class AppDragHelper extends ItemTouchHelper.Callback {
    
    private AppGridAdapter adapter;
    private OnDragListener dragListener;
    private boolean isDragging = false;
    
    public interface OnDragListener {
        void onDragStart();
        void onDragEnd();
        void onItemMove(int fromPosition, int toPosition);
    }
    
    public AppDragHelper(AppGridAdapter adapter) {
        this.adapter = adapter;
    }
    
    public void setOnDragListener(OnDragListener listener) {
        this.dragListener = listener;
    }
    
    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        // 支持上下左右四个方向的拖拽
        int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN | 
                       ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
        // 不支持滑动删除
        int swipeFlags = 0;
        return makeMovementFlags(dragFlags, swipeFlags);
    }
    
    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView,
                         @NonNull RecyclerView.ViewHolder viewHolder,
                         @NonNull RecyclerView.ViewHolder target) {
        int fromPosition = viewHolder.getAdapterPosition();
        int toPosition = target.getAdapterPosition();

        Log.d("DragDebug", "onMove: 从位置 " + fromPosition + " 移动到位置 " + toPosition);

        // 通知适配器移动项目
        if (adapter != null) {
            adapter.moveItem(fromPosition, toPosition);
        }

        // 通知监听器
        if (dragListener != null) {
            dragListener.onItemMove(fromPosition, toPosition);
        }
        
        return true;
    }
    
    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
        // 不支持滑动删除，留空
    }
    
    @Override
    public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
        super.onSelectedChanged(viewHolder, actionState);

        if (viewHolder != null) {
            int position = viewHolder.getAdapterPosition();
            Log.d("DragDebug", "AppDragHelper.onSelectedChanged: actionState=" + actionState + ", viewHolder=" + viewHolder + ", position=" + position);
            Log.d("DragDebug", "是否为第一排应用: " + (position < 4));
        } else {
            Log.d("DragDebug", "AppDragHelper.onSelectedChanged: actionState=" + actionState + ", viewHolder=null");
        }

        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG) {
            // 开始拖拽
            Log.d("DragDebug", "AppDragHelper: 开始拖拽状态");
            isDragging = true;
            if (dragListener != null) {
                dragListener.onDragStart();
            }

            // 设置拖拽时的视觉效果
            if (viewHolder != null) {
                Log.d("DragDebug", "AppDragHelper: 设置拖拽视觉效果");
                viewHolder.itemView.setAlpha(0.8f);
                viewHolder.itemView.setScaleX(1.1f);
                viewHolder.itemView.setScaleY(1.1f);
                viewHolder.itemView.setElevation(16f);
            }

        } else if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
            // 拖拽结束
            Log.d("DragDebug", "AppDragHelper: 拖拽结束状态");
            if (isDragging) {
                isDragging = false;
                if (dragListener != null) {
                    dragListener.onDragEnd();
                }
            }
        }
    }
    
    @Override
    public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        
        // 恢复视觉效果
        viewHolder.itemView.setAlpha(1.0f);
        viewHolder.itemView.setScaleX(1.0f);
        viewHolder.itemView.setScaleY(1.0f);
        viewHolder.itemView.setElevation(2f);
    }
    
    @Override
    public void onChildDraw(@NonNull Canvas c, @NonNull RecyclerView recyclerView,
                           @NonNull RecyclerView.ViewHolder viewHolder,
                           float dX, float dY, int actionState, boolean isCurrentlyActive) {
        Log.d("DragDebug", "onChildDraw: dX=" + dX + ", dY=" + dY + ", actionState=" + actionState + ", isCurrentlyActive=" + isCurrentlyActive);

        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive);

        if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && isCurrentlyActive) {
            // 拖拽时添加阴影效果
            viewHolder.itemView.setElevation(16f);
            Log.d("DragDebug", "设置拖拽阴影效果");
        }
    }
    
    @Override
    public boolean isLongPressDragEnabled() {
        // 禁用默认的长按拖拽，我们自己控制拖拽的开始
        return false;
    }
    
    @Override
    public boolean isItemViewSwipeEnabled() {
        // 禁用滑动删除
        return false;
    }
    
    /**
     * 检查是否正在拖拽
     */
    public boolean isDragging() {
        return isDragging;
    }
}
