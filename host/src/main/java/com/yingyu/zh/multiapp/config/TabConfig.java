package com.yingyu.zh.multiapp.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 底部Tab配置管理类
 * 支持动态配置Tab的显示顺序和启用状态
 */
public class TabConfig {
    
    // 审核模式Tab配置（第一套）
    private static final List<TabType> REVIEW_MODE_TAB_ORDER = Arrays.asList(
        TabType.VIP,
        TabType.HIDE_APP,
        TabType.ADD_APP,
        TabType.SET_PASSWORD,
        TabType.MY_CENTER
    );

    // 正常模式Tab配置（第二套）
    private static final List<TabType> NORMAL_MODE_TAB_ORDER = Arrays.asList(
        TabType.VIP,
        TabType.CHANGE_ICON,
        TabType.ADD_APP,
        TabType.SET_PASSWORD,
        TabType.MY_CENTER
    );

    // 审核开关状态
    private static boolean isReviewMode = false;

    // 当前Tab配置（根据审核开关动态选择）
    private static List<TabType> getCurrentTabOrder() {
        return isReviewMode ? REVIEW_MODE_TAB_ORDER : NORMAL_MODE_TAB_ORDER;
    }
    
    // Tab启用状态配置
    private static boolean vipTabEnabled = true;
    private static boolean changeIconTabEnabled = true;
    private static boolean addAppTabEnabled = true;
    private static boolean hideAppTabEnabled = true;
    private static boolean setPasswordTabEnabled = true;
    private static boolean locationProtectTabEnabled = true;
    private static boolean myCenterTabEnabled = true;

    /**
     * 获取当前Tab配置顺序（根据审核开关自动选择配置）
     */
    public static List<TabType> getTabOrder() {
        List<TabType> enabledTabs = new ArrayList<>();
        List<TabType> currentOrder = getCurrentTabOrder();

        for (TabType tabType : currentOrder) {
            if (isTabEnabled(tabType)) {
                enabledTabs.add(tabType);
            }
        }
        return enabledTabs;
    }

    /**
     * 设置审核模式开关
     * @param reviewMode true=审核模式（隐藏应用），false=正常模式（位置保护）
     */
    public static void setReviewMode(boolean reviewMode) {
        isReviewMode = reviewMode;
    }

    /**
     * 获取当前是否为审核模式
     */
    public static boolean isReviewMode() {
        return isReviewMode;
    }

    /**
     * 获取审核模式Tab配置
     */
    public static List<TabType> getReviewModeTabOrder() {
        return new ArrayList<>(REVIEW_MODE_TAB_ORDER);
    }

    /**
     * 获取正常模式Tab配置
     */
    public static List<TabType> getNormalModeTabOrder() {
        return new ArrayList<>(NORMAL_MODE_TAB_ORDER);
    }

    /**
     * 设置Tab顺序（注意：此方法在审核开关模式下不推荐使用）
     * @deprecated 建议使用setReviewMode()来切换配置
     */
    @Deprecated
    public static void setTabOrder(List<TabType> tabOrder) {
        // 在审核开关模式下，此方法被弱化，建议使用setReviewMode
        // currentTabOrder = new ArrayList<>(tabOrder);
    }

    /**
     * 重置为默认配置（正常模式）
     */
    public static void resetToDefault() {
        isReviewMode = false; // 默认为正常模式
    }

    /**
     * 检查Tab是否启用
     */
    public static boolean isTabEnabled(TabType tabType) {
        switch (tabType) {
            case VIP:
                return vipTabEnabled;
            case CHANGE_ICON:
                return changeIconTabEnabled;
            case ADD_APP:
                return addAppTabEnabled;
            case HIDE_APP:
                return hideAppTabEnabled;
            case SET_PASSWORD:
                return setPasswordTabEnabled;
            case LOCATION_PROTECT:
                return locationProtectTabEnabled;
            case MY_CENTER:
                return myCenterTabEnabled;
            default:
                return false;
        }
    }

    /**
     * 设置Tab启用状态
     */
    public static void setTabEnabled(TabType tabType, boolean enabled) {
        switch (tabType) {
            case VIP:
                vipTabEnabled = enabled;
                break;
            case CHANGE_ICON:
                changeIconTabEnabled = enabled;
                break;
            case ADD_APP:
                addAppTabEnabled = enabled;
                break;
            case HIDE_APP:
                hideAppTabEnabled = enabled;
                break;
            case SET_PASSWORD:
                setPasswordTabEnabled = enabled;
                break;
            case LOCATION_PROTECT:
                locationProtectTabEnabled = enabled;
                break;
            case MY_CENTER:
                myCenterTabEnabled = enabled;
                break;
        }
    }

    /**
     * 批量设置Tab启用状态
     */
    public static void setTabsEnabled(TabType[] tabTypes, boolean enabled) {
        for (TabType tabType : tabTypes) {
            setTabEnabled(tabType, enabled);
        }
    }

    /**
     * 获取默认Tab顺序（正常模式）
     */
    public static List<TabType> getDefaultTabOrder() {
        return new ArrayList<>(NORMAL_MODE_TAB_ORDER);
    }

    /**
     * 在指定位置插入Tab（注意：在审核开关模式下不推荐使用）
     * @deprecated 建议使用setReviewMode()来切换配置
     */
    @Deprecated
    public static void insertTab(TabType tabType, int position) {
        // 在审核开关模式下，此方法被弱化
        // 建议直接使用setReviewMode()来切换预定义的配置
    }

    /**
     * 移除Tab（注意：在审核开关模式下不推荐使用）
     * @deprecated 建议使用setReviewMode()来切换配置
     */
    @Deprecated
    public static void removeTab(TabType tabType) {
        // 在审核开关模式下，此方法被弱化
        // 建议直接使用setReviewMode()来切换预定义的配置
    }

    /**
     * 交换两个Tab的位置（注意：在审核开关模式下不推荐使用）
     * @deprecated 建议使用setReviewMode()来切换配置
     */
    @Deprecated
    public static void swapTabs(TabType tab1, TabType tab2) {
        // 在审核开关模式下，此方法被弱化
        // 建议直接使用setReviewMode()来切换预定义的配置
    }
}
