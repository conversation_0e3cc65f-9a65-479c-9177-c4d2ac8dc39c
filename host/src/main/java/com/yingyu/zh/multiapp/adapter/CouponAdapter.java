package com.yingyu.zh.multiapp.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.yingyu.zh.multiapp.model.CouponItem;
import com.xyz.host.R;

import java.util.List;

/**
 * 优惠券列表适配器
 */
public class CouponAdapter extends RecyclerView.Adapter<CouponAdapter.ViewHolder> {
    
    private Context context;
    private List<CouponItem> couponList;
    private OnCouponClickListener onCouponClickListener;
    private OnCouponUseListener onCouponUseListener;

    public interface OnCouponClickListener {
        void onCouponClick(CouponItem coupon, int position);
    }

    public interface OnCouponUseListener {
        void onCouponUse(CouponItem coupon, int position);
    }

    public CouponAdapter(Context context, List<CouponItem> couponList) {
        this.context = context;
        this.couponList = couponList;
    }

    public void setOnCouponClickListener(OnCouponClickListener listener) {
        this.onCouponClickListener = listener;
    }

    public void setOnCouponUseListener(OnCouponUseListener listener) {
        this.onCouponUseListener = listener;
    }

    /**
     * 更新数据
     */
    public void updateData(List<CouponItem> newCouponList) {
        this.couponList = newCouponList;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_coupon, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CouponItem coupon = couponList.get(position);

        // 设置优惠券标题
        holder.tvCouponTitle.setText(coupon.getTypeName());
        
        // 设置有效期信息
        holder.tvCouponValidity.setText(coupon.getValidityText());

        // 设置来源信息
        String sourceDescription = coupon.getSourceDescription();
        if (sourceDescription != null && !sourceDescription.isEmpty()) {
            holder.tvCouponSource.setText(sourceDescription);
            holder.tvCouponSource.setVisibility(View.VISIBLE);
        } else {
            holder.tvCouponSource.setVisibility(View.GONE);
        }
        
        // 根据状态设置UI
        if (coupon.isExpired()) {
            // 已过期状态
            holder.tvUseButton.setVisibility(View.GONE);
            holder.tvStatusMark.setVisibility(View.VISIBLE);
            holder.tvStatusMark.setText("已过期");
            holder.tvCouponTitle.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
            holder.tvCouponValidity.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
            holder.tvCouponSource.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
        } else if (coupon.isUsed()) {
            // 已使用状态
            holder.tvUseButton.setVisibility(View.GONE);
            holder.tvStatusMark.setVisibility(View.VISIBLE);
            holder.tvStatusMark.setText("已使用");
            holder.tvCouponTitle.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
            holder.tvCouponValidity.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
            holder.tvCouponSource.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
        } else {
            // 可使用状态
            holder.tvUseButton.setVisibility(View.VISIBLE);
            holder.tvUseButton.setText("去使用");
            holder.tvUseButton.setEnabled(true);
            holder.tvStatusMark.setVisibility(View.GONE);
            holder.tvCouponTitle.setTextColor(context.getResources().getColor(android.R.color.white));
            holder.tvCouponValidity.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
            holder.tvCouponSource.setTextColor(context.getResources().getColor(R.color.multiapp_brand_red));
        }

        // 设置点击事件
        holder.tvUseButton.setOnClickListener(v -> {
            if (coupon.canUse() && onCouponUseListener != null) {
                onCouponUseListener.onCouponUse(coupon, position);
            }
        });

        // 设置整个项目的点击事件
        holder.itemView.setOnClickListener(v -> {
            if (onCouponClickListener != null) {
                onCouponClickListener.onCouponClick(coupon, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return couponList != null ? couponList.size() : 0;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvCouponTitle;
        TextView tvCouponValidity;
        TextView tvCouponSource;
        TextView tvUseButton;
        TextView tvStatusMark;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCouponTitle = itemView.findViewById(R.id.tv_coupon_title);
            tvCouponValidity = itemView.findViewById(R.id.tv_coupon_validity);
            tvCouponSource = itemView.findViewById(R.id.tv_coupon_source);
            tvUseButton = itemView.findViewById(R.id.tv_use_button);
            tvStatusMark = itemView.findViewById(R.id.tv_status_mark);
        }
    }
}
