package com.yingyu.zh.multiapp;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;


import com.xyz.host.R;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.user.engine.ApiServiceDelegate;
import com.xyz.host.user.engine.ApiCallback;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.utils.UIUtils;

import org.json.JSONObject;
import java.util.Map;

/**
 * 邀请与兑换页面
 */
public class InviteRedeemActivity extends AppCompatActivity implements View.OnClickListener {

    private TextView tvInviteCode;
    private TextView tvInvitedCount;
    private TextView tvRedeemCount;
    private EditText etRedeemCode;

    public static void start(Context context) {
        Intent intent = new Intent(context, InviteRedeemActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_invite_redeem);

        initViews();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 每次页面恢复时刷新数据
        loadInviteCodeFromServer();
        StatAgent.onEvent(this, "invite_page");
    }



    private void initViews() {
        // 返回按钮
        findViewById(R.id.iv_back).setOnClickListener(this);
        
        // 规则按钮
        findViewById(R.id.tv_rules).setOnClickListener(this);
        
        // 邀请码相关
        tvInviteCode = findViewById(R.id.tv_invite_code);
        findViewById(R.id.tv_copy_code).setOnClickListener(this);
        
        // 统计数据
        tvInvitedCount = findViewById(R.id.tv_invited_count);
        tvRedeemCount = findViewById(R.id.tv_redeem_count);

        // 优惠券数量点击事件
        findViewById(R.id.ll_redeem_count).setOnClickListener(this);

        // 立即分享按钮
        findViewById(R.id.btn_share_invite).setOnClickListener(this);
        
        // 兑换相关
        etRedeemCode = findViewById(R.id.et_redeem_code);
        findViewById(R.id.btn_confirm_redeem).setOnClickListener(this);
    }

    /**
     * 从服务器加载邀请码和统计数据
     */
    private void loadInviteCodeFromServer() {
        new ApiServiceDelegate().getInviteCode(new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里记录请求参数用于调试
            }

            @Override
            public void onSuccess(String result) {
                try {
                    JSONObject jsonObject = new JSONObject(result);

                    // 解析邀请码
                    String inviteCode = jsonObject.optString("invite_code", "");
                    if (!inviteCode.isEmpty()) {
                        tvInviteCode.setText(inviteCode);
                    }

                    // 解析统计数据
                    int invitedCount = jsonObject.optInt("invited_count", 0);
                    int maxInviteLimit = jsonObject.optInt("max_invite_limit", 20);

                    tvInvitedCount.setText(String.valueOf(invitedCount));

                    // 这里暂时显示已邀请数量，体验券数量需要单独接口获取
                    tvRedeemCount.setText("0");

                    // 加载体验券数据
                    loadTicketsFromServer();

                } catch (Exception e) {
                    Toast.makeText(InviteRedeemActivity.this, "数据解析失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFail(String error) {
                Toast.makeText(InviteRedeemActivity.this, "获取邀请码失败: " + error, Toast.LENGTH_SHORT).show();
                // 显示默认数据
                tvInviteCode.setText("加载中...");
                tvInvitedCount.setText("0");
                tvRedeemCount.setText("0");
            }
        });
    }

    /**
     * 从服务器加载体验券数据
     */
    private void loadTicketsFromServer() {
        new ApiServiceDelegate().getTickets(new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里记录请求参数用于调试
            }

            @Override
            public void onSuccess(String result) {
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    int totalCount = jsonObject.optInt("total_count", 0);
                    tvRedeemCount.setText(String.valueOf(totalCount));
                } catch (Exception e) {
                    // 解析失败，保持默认值
                    tvRedeemCount.setText("0");
                }
            }

            @Override
            public void onFail(String error) {
                // 获取体验券失败，保持默认值
                tvRedeemCount.setText("0");
            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        
        if (id == R.id.iv_back) {
            finish();
        } else if (id == R.id.tv_rules) {
            showRules();
        } else if (id == R.id.btn_share_invite) {
            shareInvite();
        } else if (id == R.id.btn_confirm_redeem) {
            confirmRedeem();
        } else if (id == R.id.ll_redeem_count) {
            // 跳转到优惠券列表页面
            CouponListActivity.start(this);
        } else if (id == R.id.tv_copy_code) {
            copyInviteCode();
        }
    }

    /**
     * 显示规则
     */
    private void showRules() {
        InviteRulesActivity.start(this);
    }


    /**
     * 复制邀请码
     */
    private void copyInviteCode() {
        String inviteCode = tvInviteCode.getText().toString();
        android.content.ClipboardManager clipboard = (android.content.ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        android.content.ClipData clip = android.content.ClipData.newPlainText("邀请码", inviteCode);
        clipboard.setPrimaryClip(clip);
        Toast.makeText(this, "邀请码已复制", Toast.LENGTH_SHORT).show();
    }

    /**
     * 分享邀请码
     */
    private void shareInvite() {
        CommonUtils.shareWithInvite(this, tvInviteCode.getText().toString());
    }

    /**
     * 确认兑换
     */
    private void confirmRedeem() {
        String redeemCode = etRedeemCode.getText().toString().trim();
        if (redeemCode.isEmpty()) {
            Toast.makeText(this, "请输入邀请码", Toast.LENGTH_SHORT).show();
            return;
        }

        if (redeemCode.length() != 6) {
            Toast.makeText(this, "邀请码应为6位字符", Toast.LENGTH_SHORT).show();
            return;
        }

        // 调用接口使用邀请码
        new ApiServiceDelegate().useInviteCode(redeemCode, new ApiCallback() {
            @Override
            public void onParams(Map<String, String> header, String params) {
                // 可以在这里记录请求参数用于调试
            }

            @Override
            public void onSuccess(String result) {
                try {
                    JSONObject jsonObject = new JSONObject(result);
                    boolean success = jsonObject.optBoolean("success", false);
                    String message = jsonObject.optString("message", "");

                    if (success) {
                        int rewardTickets = jsonObject.optInt("reward_tickets", 0);
                        String inviterMobile = jsonObject.optString("inviter_mobile", "");

                        String successMsg = "邀请码使用成功！";
                        if (rewardTickets > 0) {
                            successMsg += "您获得了" + rewardTickets + "张体验券";
                        }
                        if (!inviterMobile.isEmpty()) {
                            successMsg += "，邀请人：" + inviterMobile;
                        }

                        Toast.makeText(InviteRedeemActivity.this, successMsg, Toast.LENGTH_LONG).show();

                        // 清空输入框
                        etRedeemCode.setText("");

                        // 刷新体验券数据
                        loadTicketsFromServer();

                    } else {
                        Toast.makeText(InviteRedeemActivity.this, message.isEmpty() ? "邀请码使用失败" : message, Toast.LENGTH_SHORT).show();
                    }

                } catch (Exception e) {
                    Toast.makeText(InviteRedeemActivity.this, "数据解析失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFail(String error) {
                // 根据错误信息显示友好提示
                String errorMsg = "邀请码使用失败";
                if (error.contains("Invalid invite code")) {
                    errorMsg = "邀请码无效，请检查后重试";
                } else if (error.contains("already been invited")) {
                    errorMsg = "您已经被邀请过了，每个用户只能被邀请一次";
                } else if (error.contains("Cannot invite yourself")) {
                    errorMsg = "不能使用自己的邀请码";
                } else if (error.contains("limit exceeded")) {
                    errorMsg = "该邀请码使用次数已达上限";
                } else if (!error.isEmpty()) {
                    errorMsg = error;
                }

                Toast.makeText(InviteRedeemActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
