package com.yingyu.zh.multiapp;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.xyz.host.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 个人中心页面
 */
public class PersonalCenterActivity extends AppCompatActivity {

    private LinearLayout llMenuContainer;
    private ImageView ivBack;
    private TextView tvLogin;

    public static void start(Context context) {
        Intent intent = new Intent(context, PersonalCenterActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_personal_center);
        
        initViews();
        initData();
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvLogin = findViewById(R.id.tv_login);
        llMenuContainer = findViewById(R.id.ll_menu_container);

        // 设置返回按钮点击事件
        ivBack.setOnClickListener(v -> finish());
        
        // 设置登录按钮点击事件
        tvLogin.setOnClickListener(v -> {
            Toast.makeText(this, "点击了立即登录", Toast.LENGTH_SHORT).show();
        });
    }

    private void initData() {
        // 创建菜单数据
        List<MenuItem> menuItems = createMenuData();
        
        // 动态创建菜单项
        createMenuItems(menuItems);
    }

    /**
     * 创建菜单数据
     */
    private List<MenuItem> createMenuData() {
        List<MenuItem> list = new ArrayList<>();
        
        list.add(new MenuItem("开通VIP会员", R.drawable.ic_vip, true));
        list.add(new MenuItem("隐私设置", R.drawable.ic_set_pass, false));
        list.add(new MenuItem("版本更新", R.drawable.ic_new_version, true));
        list.add(new MenuItem("帮助与反馈", R.drawable.ic_share, false));
        list.add(new MenuItem("关于", R.drawable.ic_about, false));
        
        return list;
    }

    /**
     * 动态创建菜单项
     */
    private void createMenuItems(List<MenuItem> menuItems) {
        llMenuContainer.removeAllViews();
        
        for (int i = 0; i < menuItems.size(); i++) {
            MenuItem item = menuItems.get(i);
            View menuView = createMenuItemView(item);
            
            llMenuContainer.addView(menuView);

            // 添加分割线（除了最后一项）
            if (i < menuItems.size() - 1) {
                View divider = new View(this);
                LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, 1);
                dividerParams.setMargins(20, 0, 20, 0); // 左右边距20dp
                divider.setLayoutParams(dividerParams);
                divider.setBackgroundColor(0xFFE5E5E5); // 更浅的灰色
                llMenuContainer.addView(divider);
            }
        }
    }

    /**
     * 创建单个菜单项视图
     */
    private View createMenuItemView(MenuItem item) {
        View view = LayoutInflater.from(this).inflate(R.layout.item_personal_menu, null);
        
        ImageView ivIcon = view.findViewById(R.id.iv_menu_icon);
        TextView tvTitle = view.findViewById(R.id.tv_menu_title);
        ImageView ivArrow = view.findViewById(R.id.iv_menu_arrow);
        ImageView ivRedDot = view.findViewById(R.id.iv_menu_red_dot);
        
        ivIcon.setImageResource(item.getIconResId());
        tvTitle.setText(item.getTitle());
        ivRedDot.setVisibility(item.isHasRedDot() ? View.VISIBLE : View.GONE);
        
        // 设置点击事件
        view.setOnClickListener(v -> {
            Toast.makeText(this, "点击了：" + item.getTitle(), Toast.LENGTH_SHORT).show();
        });
        
        return view;
    }

    /**
     * 菜单项数据类
     */
    public static class MenuItem {
        private String title;
        private int iconResId;
        private boolean hasRedDot;

        public MenuItem(String title, int iconResId, boolean hasRedDot) {
            this.title = title;
            this.iconResId = iconResId;
            this.hasRedDot = hasRedDot;
        }

        public String getTitle() {
            return title;
        }

        public int getIconResId() {
            return iconResId;
        }

        public boolean isHasRedDot() {
            return hasRedDot;
        }
    }
}
