package com.yingyu.zh.multiapp.config;

import java.util.Arrays;

/**
 * Tab配置示例类
 * 展示如何使用TabConfig来配置底部Tab（支持审核开关）
 */
public class TabConfigExample {

    /**
     * 示例1：切换到审核模式（第一套配置）
     * 显示：去广告、修改图标、添加应用、隐藏应用、我的
     */
    public static void switchToReviewMode() {
        TabConfig.setReviewMode(true);
    }

    /**
     * 示例2：切换到正常模式（第二套配置）
     * 显示：去广告、修改图标、添加应用、位置保护、我的
     */
    public static void switchToNormalMode() {
        TabConfig.setReviewMode(false);
    }

    /**
     * 示例3：根据条件动态切换模式
     */
    public static void dynamicModeSwitch(boolean isInReview) {
        if (isInReview) {
            // 审核期间使用审核模式
            TabConfig.setReviewMode(true);
        } else {
            // 正常运营使用正常模式
            TabConfig.setReviewMode(false);
        }
    }

    /**
     * 示例4：VIP用户配置（在任何模式下都隐藏去广告Tab）
     */
    public static void vipUserTabs() {
        // VIP用户不需要看到"去广告"Tab
        TabConfig.setTabEnabled(TabType.VIP, false);
        // 模式切换不受影响，只是隐藏VIP Tab
    }

    /**
     * 示例5：简化版Tab配置（只显示核心功能）
     */
    public static void simplifiedTabs() {
        // 使用正常模式但禁用部分Tab
        TabConfig.setReviewMode(false);
        TabConfig.setTabEnabled(TabType.VIP, false);
        TabConfig.setTabEnabled(TabType.CHANGE_ICON, false);
        // 只显示：添加应用、位置保护、我的
    }

    /**
     * 示例6：新用户引导配置
     */
    public static void newUserGuideTabs() {
        // 新用户使用正常模式但只显示核心功能
        TabConfig.setReviewMode(false);
        TabConfig.setTabEnabled(TabType.VIP, false);
        TabConfig.setTabEnabled(TabType.CHANGE_ICON, false);
        TabConfig.setTabEnabled(TabType.LOCATION_PROTECT, false);
        // 只显示：添加应用、我的
    }

    /**
     * 重置为默认配置
     */
    public static void resetToDefault() {
        TabConfig.resetToDefault(); // 重置为正常模式

        // 重新启用所有Tab
        TabConfig.setTabEnabled(TabType.VIP, true);
        TabConfig.setTabEnabled(TabType.CHANGE_ICON, true);
        TabConfig.setTabEnabled(TabType.ADD_APP, true);
        TabConfig.setTabEnabled(TabType.HIDE_APP, true);
        TabConfig.setTabEnabled(TabType.LOCATION_PROTECT, true);
        TabConfig.setTabEnabled(TabType.MY_CENTER, true);
    }

    /**
     * 根据用户类型和审核状态配置Tab
     */
    public static void configureForUserType(boolean isVip, boolean isNewUser, boolean isInReview) {
        // 首先根据审核状态设置模式
        TabConfig.setReviewMode(isInReview);

        // 然后根据用户类型调整
        if (isVip) {
            vipUserTabs();
        } else if (isNewUser) {
            newUserGuideTabs();
        }
    }

    /**
     * 获取当前配置信息
     */
    public static String getCurrentConfigInfo() {
        boolean isReviewMode = TabConfig.isReviewMode();
        String mode = isReviewMode ? "审核模式" : "正常模式";
        String tabs = isReviewMode ? "隐藏应用" : "位置保护";
        return String.format("当前模式：%s，特色功能：%s", mode, tabs);
    }
}
