package com.yingyu.zh.multiapp.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

/**
 * 使用体验券接口响应数据模型
 */
@Keep
public class UseTicketResponse {
    @SerializedName("success")
    private boolean success;

    @SerializedName("message")
    private String message;

    @SerializedName("ticket_id")
    private int ticketId;

    @SerializedName("ticket_uuid")
    private String ticketUuid;

    @SerializedName("vip_expire_time")
    private String vipExpireTime;

    @SerializedName("extended_hours")
    private int extendedHours;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getVipExpireTime() {
        return vipExpireTime;
    }

    public void setVipExpireTime(String vipExpireTime) {
        this.vipExpireTime = vipExpireTime;
    }

    public int getExtendedHours() {
        return extendedHours;
    }

    public void setExtendedHours(int extendedHours) {
        this.extendedHours = extendedHours;
    }

    public int getTicketId() {
        return ticketId;
    }

    public void setTicketId(int ticketId) {
        this.ticketId = ticketId;
    }

    public String getTicketUuid() {
        return ticketUuid;
    }

    public void setTicketUuid(String ticketUuid) {
        this.ticketUuid = ticketUuid;
    }
}
