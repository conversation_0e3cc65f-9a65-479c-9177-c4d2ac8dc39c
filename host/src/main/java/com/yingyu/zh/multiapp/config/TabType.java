package com.yingyu.zh.multiapp.config;

import com.xyz.host.R;

/**
 * 底部Tab类型枚举
 */
public enum TabType {
    VIP("去广告", R.drawable.ic_vip, false, false),
    CHANGE_ICON("修改图标", R.drawable.ic_launcher, false, false), // 图标会动态更新
    ADD_APP("添加应用", R.drawable.ic_add, true, false),
    HIDE_APP("隐藏应用", R.drawable.ic_hide_app, false, false),
    SET_PASSWORD("修改密码", R.drawable.ic_set_pass, false, false),
    LOCATION_PROTECT("位置保护", R.drawable.ic_pos, false, false),
    MY_CENTER("我的", R.drawable.ic_tab_me, false, false);

    private final String title;
    private final int iconResId; // 使用资源ID避免混淆问题
    private final boolean isAddButton;
    private final boolean hasRedDot;

    TabType(String title, int iconResId, boolean isAddButton, boolean hasRedDot) {
        this.title = title;
        this.iconResId = iconResId;
        this.isAddButton = isAddButton;
        this.hasRedDot = hasRedDot;
    }

    public String getTitle() {
        return title;
    }

    public int getIconResId() {
        return iconResId;
    }

    public boolean isAddButton() {
        return isAddButton;
    }

    public boolean hasRedDot() {
        return hasRedDot;
    }
}
