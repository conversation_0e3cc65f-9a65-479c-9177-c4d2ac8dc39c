# 底部Tab配置系统使用说明

## 概述

这个配置系统支持两套预定义的Tab配置，通过审核开关自动切换，无需修改核心代码。

## 两套Tab配置

### 第一套：审核模式（review_mode_switch = true）
- 去广告
- 修改图标
- 添加应用
- 隐藏应用
- 我的

### 第二套：正常模式（review_mode_switch = false）
- 去广告
- 修改图标
- 添加应用
- 位置保护
- 我的

## 核心组件

### 1. TabType (枚举)
定义了所有可用的Tab类型：
- `VIP` - 去广告
- `CHANGE_ICON` - 修改图标  
- `ADD_APP` - 添加应用
- `HIDE_APP` - 隐藏应用
- `MY_CENTER` - 我的

### 2. TabConfig (配置管理类)
提供Tab配置的核心功能：
- 设置Tab显示顺序
- 启用/禁用特定Tab
- 动态调整Tab位置

### 3. TabClickHandler (点击处理接口)
统一处理Tab点击事件

## 基本使用方法

### 1. 切换Tab配置模式
```java
// 切换到审核模式（显示隐藏应用）
TabConfig.setReviewMode(true);

// 切换到正常模式（显示位置保护）
TabConfig.setReviewMode(false);

// 刷新界面
multiAppActivity.refreshBottomTabs();
```

### 2. 检查当前模式
```java
boolean isReviewMode = TabConfig.isReviewMode();
if (isReviewMode) {
    // 当前是审核模式
} else {
    // 当前是正常模式
}
```

### 2. 启用/禁用Tab
```java
// 禁用VIP Tab
TabConfig.setTabEnabled(TabType.VIP, false);

// 批量禁用多个Tab
TabConfig.setTabsEnabled(new TabType[]{TabType.VIP, TabType.CHANGE_ICON}, false);
```

### 3. 动态调整Tab位置
```java
// 交换两个Tab的位置
TabConfig.swapTabs(TabType.VIP, TabType.MY_CENTER);

// 在指定位置插入Tab
TabConfig.insertTab(TabType.ADD_APP, 0); // 插入到第一位

// 移除Tab
TabConfig.removeTab(TabType.CHANGE_ICON);
```

### 4. 重置为默认配置
```java
TabConfig.resetToDefault();
```

## 使用场景示例

### 场景1：应用审核期间
```java
// 审核期间使用审核模式，隐藏敏感功能
TabConfig.setReviewMode(true);
multiAppActivity.refreshBottomTabs();
```

### 场景2：正常运营期间
```java
// 正常运营使用完整功能
TabConfig.setReviewMode(false);
multiAppActivity.refreshBottomTabs();
```

### 场景3：VIP用户配置
```java
// VIP用户不显示"去广告"Tab（在任何模式下）
TabConfig.setTabEnabled(TabType.VIP, false);
multiAppActivity.refreshBottomTabs();
```

### 场景4：根据远程配置动态切换
```java
// 从远程配置获取审核开关状态
String reviewMode = UMRemoteConfig.getInstance().getConfigValue("review_mode_switch");
TabConfig.setReviewMode("true".equals(reviewMode));
multiAppActivity.refreshBottomTabs();
```

## 在MultiAppActivity中使用

在MultiAppActivity中，您可以在适当的时机调用配置方法：

```java
@Override
protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    
    // ... 其他初始化代码 ...
    
    // 根据用户状态配置Tab
    configureTabsForUser();
    
    initData();
}

private void configureTabsForUser() {
    boolean isVip = UserAgent.getInstance().isVipUser();
    boolean isNewUser = checkIfNewUser();
    
    if (isVip) {
        TabConfig.setTabEnabled(TabType.VIP, false);
    }
    
    if (isNewUser) {
        // 新用户简化配置
        TabConfig.setTabOrder(Arrays.asList(
            TabType.ADD_APP, 
            TabType.MY_CENTER
        ));
    }
}
```

## 注意事项

1. 配置更改后需要调用 `refreshBottomTabs()` 来刷新界面
2. 配置是全局的，会影响所有MultiAppActivity实例
3. 可以通过SharedPreferences持久化配置
4. 建议在应用启动时进行配置，避免频繁更改

## 扩展功能

如果需要添加新的Tab类型：

1. 在 `TabType` 枚举中添加新类型
2. 在 `createTabFromType()` 方法中添加对应的处理逻辑
3. 在 `handleTabClick()` 方法中添加点击处理逻辑
