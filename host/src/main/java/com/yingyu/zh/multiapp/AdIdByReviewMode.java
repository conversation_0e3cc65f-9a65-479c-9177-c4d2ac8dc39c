package com.yingyu.zh.multiapp;

import com.umeng.cconfig.UMRemoteConfig;

public class AdIdByReviewMode {

    public static String getAdId(String name) {
        boolean isReviewMode = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("review_mode"));
        if (isReviewMode) {
            return "0";
        } else {
            return UMRemoteConfig.getInstance().getConfigValue(name);
        }
    }
}
