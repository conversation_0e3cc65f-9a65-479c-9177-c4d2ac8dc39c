package com.yingyu.zh.multiapp.model;

import androidx.annotation.Keep;

import com.google.gson.annotations.SerializedName;

/**
 * 优惠券数据模型
 */
@Keep
public class CouponItem {
    @SerializedName("ticket_id")
    private int ticketId;           // 体验券唯一ID

    @SerializedName("ticket_uuid")
    private String ticketUuid;      // 体验券唯一标识

    @SerializedName("type_id")
    private String typeId;          // 体验券类型ID

    @SerializedName("type_name")
    private String typeName;        // 体验券类型名称

    @SerializedName("duration_hours")
    private int durationHours;      // 时长（小时）

    @SerializedName("source")
    private String source;          // 来源代码

    @SerializedName("source_display")
    private String sourceDisplay;   // 来源显示名称

    @SerializedName("expire_time")
    private String expireTime;      // 过期时间

    @SerializedName("is_expired")
    private boolean isExpired;      // 是否过期

    private boolean isUsed;         // 是否已使用（本地状态，不从API获取）

    public CouponItem() {
    }

    public CouponItem(int ticketId, String ticketUuid, String typeId, String typeName, int durationHours,
                     String source, String sourceDisplay, String expireTime, boolean isExpired) {
        this.ticketId = ticketId;
        this.ticketUuid = ticketUuid;
        this.typeId = typeId;
        this.typeName = typeName;
        this.durationHours = durationHours;
        this.source = source;
        this.sourceDisplay = sourceDisplay;
        this.expireTime = expireTime;
        this.isExpired = isExpired;
        this.isUsed = false;
    }

    // Getters and Setters
    public int getTicketId() {
        return ticketId;
    }

    public void setTicketId(int ticketId) {
        this.ticketId = ticketId;
    }

    public String getTicketUuid() {
        return ticketUuid;
    }

    public void setTicketUuid(String ticketUuid) {
        this.ticketUuid = ticketUuid;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public int getDurationHours() {
        return durationHours;
    }

    public void setDurationHours(int durationHours) {
        this.durationHours = durationHours;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceDisplay() {
        return sourceDisplay;
    }

    public void setSourceDisplay(String sourceDisplay) {
        this.sourceDisplay = sourceDisplay;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public boolean isExpired() {
        return isExpired;
    }

    public void setExpired(boolean expired) {
        isExpired = expired;
    }

    public boolean isUsed() {
        return isUsed;
    }

    public void setUsed(boolean used) {
        isUsed = used;
    }

    /**
     * 获取显示的有效期文本
     */
    public String getValidityText() {
        String durationText = getDurationText();

        if (isExpired) {
            return durationText + " | " + expireTime + " 失效";
        } else if (isUsed) {
            return durationText + " | " + expireTime + " 使用";
        } else {
            return durationText + " | " + expireTime + " 失效";
        }
    }

    /**
     * 根据时长获取有效期描述
     */
    public String getDurationText() {
        if (durationHours <= 0) {
            return "有效期未知";
        } else if (durationHours < 24) {
            return durationHours + "小时";
        } else if (durationHours % 24 == 0) {
            int days = durationHours / 24;
            return days + "天";
        } else {
            int days = durationHours / 24;
            int hours = durationHours % 24;
            return days + "天" + hours + "小时";
        }
    }

    /**
     * 获取状态文本
     */
    public String getStatusText() {
        if (isExpired) {
            return "已过期";
        } else if (isUsed) {
            return "已使用";
        } else {
            return "去使用";
        }
    }

    /**
     * 是否可以使用
     */
    public boolean canUse() {
        return !isExpired && !isUsed;
    }

    /**
     * 获取来源描述文本
     */
    public String getSourceDescription() {
        if (sourceDisplay != null && !sourceDisplay.isEmpty()) {
            return "(" + sourceDisplay + ")";
        }

        // 如果没有显示名称，根据来源代码生成描述
        if (source != null) {
            switch (source) {
                case "invite_reward":
                    return "(受邀专享)";
                case "inviter_reward":
                    return "(邀请奖励)";
                case "admin":
                    return "(系统发放)";
                case "system":
                    return "(系统发放)";
                default:
                    return "(其他来源)";
            }
        }

        return "";
    }

    /**
     * 获取详细描述信息
     */
    public String getDetailDescription() {
        StringBuilder description = new StringBuilder();

        description.append("类型: ").append(typeName).append("\n");
        description.append("时长: ").append(getDurationText()).append("\n");
        description.append("券号: ").append(ticketId).append("\n");
        description.append("过期时间: ").append(expireTime).append("\n");
        description.append("来源: ").append(sourceDisplay != null ? sourceDisplay : source).append("\n");

        return description.toString();
    }
}
