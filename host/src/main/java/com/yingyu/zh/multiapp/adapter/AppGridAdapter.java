package com.yingyu.zh.multiapp.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.xyz.host.R;
import com.yingyu.zh.multiapp.model.AppItem;

import java.util.Collections;
import java.util.List;

/**
 * 应用网格适配器
 */
public class AppGridAdapter extends RecyclerView.Adapter<AppGridAdapter.ViewHolder> {
    
    private Context context;
    private List<AppItem> appList;
    private OnItemClickListener onItemClickListener;
    private OnItemLongClickListener onItemLongClickListener;
    // private OnItemTouchListener onItemTouchListener; // 不再使用
    private OnItemMoveListener onItemMoveListener;
    private int highlightPosition = -1; // 高亮位置

    public interface OnItemClickListener {
        void onItemClick(AppItem item, int position);
    }

    public interface OnItemLongClickListener {
        void onItemLongClick(AppItem item, int position, View view, int columnPosition);
    }

    // public interface OnItemTouchListener {
    //     boolean onItemTouch(AppItem item, int position, View view, MotionEvent event);
    // } // 不再使用

    public interface OnItemMoveListener {
        void onItemMove(int fromPosition, int toPosition);
    }

    public AppGridAdapter(Context context, List<AppItem> appList) {
        this.context = context;
        this.appList = appList;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener listener) {
        this.onItemLongClickListener = listener;
    }

    // public void setOnItemTouchListener(OnItemTouchListener listener) {
    //     this.onItemTouchListener = listener;
    // } // 不再使用

    public void setOnItemMoveListener(OnItemMoveListener listener) {
        this.onItemMoveListener = listener;
    }

    /**
     * 更新数据
     */
    public void updateData(List<AppItem> newAppList) {
        this.appList = newAppList;
        notifyDataSetChanged();
    }

    /**
     * 获取应用列表
     */
    public List<AppItem> getAppList() {
        return appList;
    }

    /**
     * 设置高亮位置
     */
    public void setHighlightPosition(int position) {
        this.highlightPosition = position;
        notifyDataSetChanged();
    }

    /**
     * 清除高亮
     */
    public void clearHighlight() {
        this.highlightPosition = -1;
        notifyDataSetChanged();
    }

    /**
     * 移动项目位置
     */
    public void moveItem(int fromPosition, int toPosition) {
        if (fromPosition < toPosition) {
            for (int i = fromPosition; i < toPosition; i++) {
                Collections.swap(appList, i, i + 1);
            }
        } else {
            for (int i = fromPosition; i > toPosition; i--) {
                Collections.swap(appList, i, i - 1);
            }
        }
        notifyItemMoved(fromPosition, toPosition);

        // 通知监听器
        if (onItemMoveListener != null) {
            onItemMoveListener.onItemMove(fromPosition, toPosition);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_app_grid, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AppItem item = appList.get(position);

        if (item.getAppIcon() != null) {
            // 设置图标并确保填充整个ImageView
            holder.ivAppIcon.setImageBitmap(item.getAppIcon());
            holder.ivAppIcon.setScaleType(ImageView.ScaleType.FIT_XY);
        } else {
            holder.ivAppIcon.setImageResource(com.xyz.host.R.drawable.ic_launcher);
            holder.ivAppIcon.setScaleType(ImageView.ScaleType.FIT_XY);
        }
        // 显示应用名称（不再在名称后添加序号，改用标签显示）
        holder.tvAppName.setText(item.getAppName());

        // 显示绿点：新应用显示绿点，已启动过的应用不显示
        holder.ivOnlineDot.setVisibility(item.isNew() ? View.VISIBLE : View.GONE);

        // 显示多开标识：如果是多开应用（userId > 0），显示标签
        if (item.getDockerBean() != null && item.getDockerBean().getUserId() > 0) {
            holder.tvUserIdLabel.setVisibility(View.VISIBLE);
            holder.tvUserIdLabel.setText(String.valueOf(item.getDockerBean().getUserId()));
        } else {
            holder.tvUserIdLabel.setVisibility(View.GONE);
        }

        // 设置高亮效果（带动画）
        if (position == highlightPosition) {
            // 高亮状态：选中的应用更加突出
            animateToHighlight(holder);

            // 添加白色发光边框效果
            holder.ivAppIcon.setBackgroundResource(R.drawable.shape_highlight_icon_bg);
        } else if (highlightPosition != -1) {
            // 非选中状态：在有高亮时，其他应用变暗
            animateToDimmed(holder);

            // 恢复原始背景
            holder.ivAppIcon.setBackgroundResource(R.drawable.shape_iphone_icon_bg);
        } else {
            // 正常状态
            animateToNormal(holder);

            // 恢复原始背景
            holder.ivAppIcon.setBackgroundResource(R.drawable.shape_iphone_icon_bg);
        }

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(item, position);
            }
        });

        // 设置长按监听器
        holder.itemView.setOnLongClickListener(v -> {
            if (onItemLongClickListener != null) {
                // 计算列位置（假设每行4个图标）
                int columnPosition = position % 4;
                onItemLongClickListener.onItemLongClick(item, position, v, columnPosition);
                return true;
            }
            return false;
        });
    }

    @Override
    public int getItemCount() {
        return appList.size();
    }

    /**
     * 动画到高亮状态
     */
    private void animateToHighlight(ViewHolder holder) {
        holder.itemView.animate()
                .alpha(1.0f)
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(200)
                .start();
        holder.itemView.setElevation(16 * context.getResources().getDisplayMetrics().density);
    }

    /**
     * 动画到变暗状态
     */
    private void animateToDimmed(ViewHolder holder) {
        holder.itemView.animate()
                .alpha(0.2f)
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(200)
                .start();
        holder.itemView.setElevation(1 * context.getResources().getDisplayMetrics().density);
    }

    /**
     * 动画到正常状态
     */
    private void animateToNormal(ViewHolder holder) {
        holder.itemView.animate()
                .alpha(1.0f)
                .scaleX(1.0f)
                .scaleY(1.0f)
                .setDuration(200)
                .start();
        holder.itemView.setElevation(2 * context.getResources().getDisplayMetrics().density);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivAppIcon;
        TextView tvAppName;
        ImageView ivOnlineDot;
        TextView tvUserIdLabel;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAppIcon = itemView.findViewById(R.id.iv_app_icon);
            tvAppName = itemView.findViewById(R.id.tv_app_name);
            ivOnlineDot = itemView.findViewById(R.id.iv_online_dot);
            tvUserIdLabel = itemView.findViewById(R.id.tv_user_id_label);
        }
    }
} 