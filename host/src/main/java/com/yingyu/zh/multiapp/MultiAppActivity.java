package com.yingyu.zh.multiapp;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.fun.vbox.client.core.VCore;
import com.fun.vbox.entity.VAppInfo;
import com.fun.vbox.utils.AbiUtils;
import com.xyz.host.MainApp;
import com.xyz.host.R;
import com.xyz.host.ads.RewardHandler;
import com.xyz.host.db.AppInfo;
import com.xyz.host.db.DbManager;
import com.xyz.host.db.DockerBean;
import com.xyz.host.home.VipPaymentHelper;
import com.xyz.host.home.add.AddAppActivity;
import com.xyz.host.home.AppDetailActivity;
import com.xyz.host.home.ShortcutHandleActivity;
import com.xyz.host.utils.ApkUtils;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.utils.InstallExtUtils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.util.Log;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;

import com.yingyu.zh.multiapp.adapter.AppGridAdapter;
import com.yingyu.zh.multiapp.helper.AppDragHelper;
import com.yingyu.zh.multiapp.model.AppItem;
import com.yingyu.zh.multiapp.model.BottomTab;
import com.yingyu.zh.multiapp.widget.AppLongPressPopup;
import com.yingyu.zh.multiapp.config.TabType;
import com.yingyu.zh.multiapp.config.TabConfig;
import com.yingyu.zh.multiapp.handler.TabClickHandler;
import com.xyz.host.home.SplashActivity;
import com.xyz.host.home.SplashCalculatorActivity;
import com.xyz.host.utils.StatAgent;
import com.xyz.host.utils.CommonUtils;
import com.xyz.host.dialog.CommonDialog;
import com.xyz.host.ads.TtFullScreenAd;
import com.xyz.host.user.engine.UserAgent;
import com.xyz.host.home.view.NumberInputDialog;
import com.umeng.cconfig.UMRemoteConfig;

import android.content.ComponentName;
import android.content.pm.PackageManager;
import android.content.pm.PackageInfo;
import android.content.pm.ApplicationInfo;
import android.graphics.drawable.Drawable;

import androidx.appcompat.app.AlertDialog;

import com.xyz.host.db.DbManager;
import com.fun.vbox.client.core.VCore;
import com.xyz.host.dialog.ShortcutDialog;
import com.xyz.host.dialog.ModifyAppNameDialog;
import com.xyz.host.sp.AppSharePref;
import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdDislike;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.xyz.host.ads.TTAdManagerHolder;
import com.xyz.host.utils.UIUtils;

import android.text.TextUtils;

import com.xyz.host.model.MapSdkConfig;
import com.xyz.host.utils.AMapUtils;
import com.xyz.host.utils.HomeUtils;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import jonathanfinerty.once.Once;

import java.io.File;
import java.util.concurrent.TimeUnit;


import java.util.ArrayList;
import java.util.List;

/**
 * 多开应用主界面
 */
public class MultiAppActivity extends AppCompatActivity implements TabClickHandler {

    private static final int REQUEST_ADD_APP = 100;
    private static final int BANNER_AD_HEIGHT_DP = 100; // 固定广告高度100dp

    private RecyclerView rvAppGrid;
    private LinearLayout llBottomTabs;
    private AppGridAdapter appAdapter;
    private AppLongPressPopup longPressPopup;
    private FrameLayout blurOverlay;
    private View guideOverlay;
    private TTNativeExpressAd mBannerAd;
    private FrameLayout mBannerContainer;
    private FrameLayout bottomTabsContainer;
    private TextView tvReward;
    private int mRewardMin = 30;
    private RewardHandler mRewardHandler;
    private Disposable mDisposable = null;

    // 拖拽相关
    private AppDragHelper dragHelper;
    private ItemTouchHelper itemTouchHelper;
    private boolean isMenuShowing = false;
    private boolean isDragging = false;
    private float downX, downY;
    private int currentLongPressPosition = -1; // 当前长按的位置
    private AppItem currentLongPressItem = null; // 当前长按的应用
    private static final float DRAG_THRESHOLD = 20f; // 拖拽阈值，单位dp

    public static void start(Context context) {
        Intent intent = new Intent(context, MultiAppActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_multiapp);

        // 添加HomeActivity中的重要初始化逻辑
        MapSdkConfig.init();
        AMapUtils.setPkg();

        initViews();

        // 配置底部Tab（可根据需要调整）
        configureBottomTabs();

        initData();

        // 显示提示信息
        HomeUtils.tryShowTip(this);

        // 加载全屏广告（非VIP用户）
        if (!UserAgent.getInstance().isVipUser()) {
            // 1个小时只展示一次
            if (!Once.beenDone(TimeUnit.HOURS, 1, "home_full_ad")) {
                String codeId = AdIdByReviewMode.getAdId("home_full_ads_id");
                new TtFullScreenAd().load(this, codeId);
                Once.markDone("home_full_ad");
            }
        }

        // 更新用户信息
        UserAgent.getInstance().update();

        mRewardHandler = new RewardHandler();
        mRewardHandler.init();
        showRewardTime();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent); // 更新当前Intent

        // 检查是否有从MyActivity转发过来的结果需要处理
        handleForwardedResult();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        StatAgent.onEvent(this, "home_page");
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        // 如果正在拖拽，不要干扰拖拽过程
        if (isDragging) {
            Log.d("DragDebug", "拖拽中，不处理全局触摸事件: " + ev.getAction());
            return super.dispatchTouchEvent(ev);
        }

        // 如果长按菜单正在显示，监听全局触摸事件
        if (isMenuShowing && currentLongPressPosition != -1 && currentLongPressItem != null) {
            Log.d("DragDebug", "全局触摸事件: " + ev.getAction() + ", 菜单显示中");
            handleGlobalTouchForDrag(ev);
        }
        return super.dispatchTouchEvent(ev);
    }

    private long getRewardTime() {
        long time = AppSharePref.get().getLong("expire_time", 0);
        if (time != 0) {
            long currentTime = System.currentTimeMillis();
            return time - currentTime;
        } else {
            return 0;
        }
    }

    @SuppressLint("SetTextI18n")
    private void showRewardTime() {
        long diffTime = getRewardTime();
        if (diffTime <= 0) {
            tvReward.setVisibility(View.GONE);
            return;
        }
        long diffHours = TimeUnit.MILLISECONDS.toHours(diffTime);
        long diffMinutes = TimeUnit.MILLISECONDS.toMinutes(diffTime) % 60;
        long diffSeconds = TimeUnit.MILLISECONDS.toSeconds(diffTime) % 60;

        long totalDiffMinutes = TimeUnit.MILLISECONDS.toMinutes(diffTime);
        if (totalDiffMinutes > mRewardMin) {
            AppSharePref.get().putLong("expire_time", 0);
            Toast.makeText(this, "时间异常，奖励时间清零", Toast.LENGTH_SHORT).show();
            tvReward.setVisibility(View.GONE);
            return;
        }

        StringBuilder rewardTime = new StringBuilder("奖励时间剩余：");
        if (diffHours > 0) {
            rewardTime.append(diffHours).append("小时");
        }
        if (diffMinutes > 0) {
            rewardTime.append(diffMinutes).append("分钟");
        }
        rewardTime.append(diffSeconds).append("秒");

        tvReward.setVisibility(View.VISIBLE);
        tvReward.setText(rewardTime.toString());

        if (mDisposable == null) {
            mDisposable = Observable.interval(1, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(new Consumer<Long>() {
                        @Override
                        public void accept(Long aLong) throws Exception {
                            showRewardTime();
                        }
                    });
        }
    }


    private void initViews() {
        rvAppGrid = findViewById(R.id.rv_app_grid);
        llBottomTabs = findViewById(R.id.ll_bottom_tabs);
        blurOverlay = findViewById(R.id.blur_overlay);
        mBannerContainer = findViewById(R.id.adContainer);
        bottomTabsContainer = findViewById(R.id.bottom_tabs_container);
        tvReward = findViewById(R.id.tv_reward);

        // 设置应用网格布局为4列，iPhone风格
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 4);
        rvAppGrid.setLayoutManager(gridLayoutManager);

        // 添加网格间距装饰器，类似iPhone的间距
        int spacing = (int) (12 * getResources().getDisplayMetrics().density); // 12dp
        rvAppGrid.addItemDecoration(new GridSpacingItemDecoration(4, spacing, true));

        // 设置毛玻璃覆盖层点击事件和触摸监听
        blurOverlay.setOnClickListener(v -> hideBlurOverlay());

        // 为毛玻璃覆盖层设置触摸监听，用于检测拖拽手势
        blurOverlay.setOnTouchListener((v, event) -> {
            Log.d("DragDebug", "毛玻璃覆盖层触摸事件: " + event.getAction() + ", isMenuShowing=" + isMenuShowing + ", isDragging=" + isDragging);

            // 如果正在拖拽，不处理触摸事件
            if (isDragging) {
                return false;
            }

            if (isMenuShowing && currentLongPressPosition != -1 && currentLongPressItem != null) {
                boolean handled = handleBlurOverlayTouch(event);
                Log.d("DragDebug", "触摸事件处理结果: " + handled);
                return handled;
            }
            return false;
        });

        // 初始化引导覆盖层
        initGuideOverlay();
    }

    private void initData() {
        // 初始化应用数据
        List<AppItem> appList = createAppData();
        appAdapter = new AppGridAdapter(this, appList);
        appAdapter.setOnItemClickListener((item, position) -> {
            Log.d("DragDebug", "应用点击事件触发: " + item.getAppName());
            launchApp(item);
        });

        // 设置长按事件监听器
        appAdapter.setOnItemLongClickListener((item, position, view, columnPosition) -> {
            Log.d("DragDebug", "长按事件触发: item=" + item.getAppName() + ", position=" + position + ", view=" + view);

            // 验证position是否正确，如果不正确则重新查找正确的位置
            int correctPosition = position;
            if (position >= 0 && position < appAdapter.getAppList().size()) {
                AppItem actualItem = appAdapter.getAppList().get(position);
                Log.d("DragDebug", "位置验证: 传入item=" + item.getAppName() + ", position=" + position + "对应的实际item=" + actualItem.getAppName());

                if (!item.getAppName().equals(actualItem.getAppName())) {
                    Log.w("DragDebug", "长按事件位置不匹配，重新查找正确位置");

                    // 重新查找正确的位置
                    correctPosition = -1;
                    for (int i = 0; i < appAdapter.getAppList().size(); i++) {
                        AppItem listItem = appAdapter.getAppList().get(i);
                        if (listItem != null && item.getAppName().equals(listItem.getAppName())) {
                            correctPosition = i;
                            Log.d("DragDebug", "找到正确位置: " + correctPosition);
                            break;
                        }
                    }

                    if (correctPosition == -1) {
                        Log.e("DragDebug", "无法找到应用的正确位置: " + item.getAppName());
                        return;
                    }
                }
            }

            showLongPressMenu(item, view, columnPosition, correctPosition);
        });

        // 注释掉智能触摸监听器，因为我们通过全局触摸事件处理拖拽
        // appAdapter.setOnItemTouchListener((item, position, view, event) -> {
        //     return handleSmartTouch(item, position, view, event);
        // });

        // 设置拖拽移动监听器
        appAdapter.setOnItemMoveListener((fromPosition, toPosition) -> {
            updateAppOrder(fromPosition, toPosition);
        });

        rvAppGrid.setAdapter(appAdapter);

        // 初始化拖拽功能
        initDragHelper();

        // 初始化长按弹出菜单
        initLongPressPopup();

        // 初始化底部Tab数据
        List<BottomTab> tabList = createStaticTabData();
        createBottomTabs(tabList);

        // 检查是否需要显示新手引导
        checkAndShowGuide(appList);

        // 加载广告位
        if (!UserAgent.getInstance().isVipUser()) {
            loadBannerAd();
        }


    }

    /**
     * 创建应用数据（从数据库加载）
     */
    private List<AppItem> createAppData() {
        List<AppItem> list = new ArrayList<>();

        // 从数据库加载真实的应用分身数据
        List<com.xyz.host.db.DockerBean> dockerBeans = DbManager.getInstance().getAppAll();

        for (com.xyz.host.db.DockerBean bean : dockerBeans) {
            // 使用packageName+userId确保instanceName的唯一性，避免拖拽错位问题
            String instanceName = bean.getPackageName() + "_" + bean.getUserId();
            Log.d("AppDebug", "创建AppItem: packageName=" + bean.getPackageName() +
                  ", userId=" + bean.getUserId() + ", instanceName=" + instanceName);

            // 从数据库的字节数组转换为Bitmap
            Bitmap appIcon = null;
            byte[] iconBytes = bean.getAppIcon();
            if (iconBytes != null && iconBytes.length > 0) {
                try {
                    appIcon = BitmapFactory.decodeByteArray(iconBytes, 0, iconBytes.length);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            // 创建AppItem并关联DockerBean
            // isNew = !isOpened，即未打开过的应用显示为新应用（显示绿点）
            AppItem appItem = new AppItem(
                    bean.getAppName(),
                    bean.getPackageName(),
                    appIcon,
                    !bean.isOpened(), // 新应用逻辑：未打开过的显示绿点
                    instanceName
            );
            // 将DockerBean存储在AppItem中，用于启动应用
            appItem.setDockerBean(bean);
            list.add(appItem);
        }

        return list;
    }

    /**
     * 创建可配置的底部Tab数据
     */
    private List<BottomTab> createStaticTabData() {
        List<BottomTab> list = new ArrayList<>();

        // 根据配置获取Tab顺序
        List<TabType> tabOrder = TabConfig.getTabOrder();

        for (TabType tabType : tabOrder) {
            BottomTab tab = createTabFromType(tabType);
            if (tab != null) {
                list.add(tab);
            }
        }

        return list;
    }

    /**
     * 根据Tab类型创建BottomTab对象
     */
    private BottomTab createTabFromType(TabType tabType) {
        switch (tabType) {
            case VIP:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            case CHANGE_ICON:
                // 修改图标Tab - 根据当前图标状态显示不同图标
                int iconResId = getChangeIconResId();
                return new BottomTab(tabType, tabType.getTitle(), iconResId,
                        tabType.isAddButton(), tabType.hasRedDot());
            case ADD_APP:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            case HIDE_APP:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            case SET_PASSWORD:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            case LOCATION_PROTECT:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            case MY_CENTER:
                return new BottomTab(tabType, tabType.getTitle(), tabType.getIconResId(),
                        tabType.isAddButton(), tabType.hasRedDot());
            default:
                return null;
        }
    }


    /**
     * 初始化长按弹出菜单
     */
    private void initLongPressPopup() {
        longPressPopup = new AppLongPressPopup(this);

        longPressPopup.setOnMenuItemClickListener(new AppLongPressPopup.OnMenuItemClickListener() {
            @Override
            public void onDeleteApp(AppItem appItem) {
                StatAgent.onEvent(getBaseContext(), "delete_app", "name", appItem.getAppName());
                showDeleteConfirmDialog(appItem);
            }

            @Override
            public void onAddToDesktop(AppItem appItem) {
                StatAgent.onEvent(getBaseContext(), "add_desktop", "name", appItem.getAppName());
                showAddToDesktopDialog(appItem);
            }

            @Override
            public void onSetName(AppItem appItem) {
                StatAgent.onEvent(getBaseContext(), "set_name", "name", appItem.getAppName());
                showSetNameDialog(appItem);
            }

            @Override
            public void onMoreSettings(AppItem appItem) {
                showMoreSettingsDialog(appItem);
            }

            @Override
            public void onDismiss() {
                // 弹窗关闭时隐藏毛玻璃效果
                hideBlurOverlay();
            }
        });

        // 设置触摸事件监听器，将PopupWindow的触摸事件传递给Activity处理
        longPressPopup.setOnTouchEventListener(event -> {
            Log.d("DragDebug", "PopupWindow触摸事件: " + event.getAction());
            // 简化处理，主要依赖毛玻璃覆盖层处理拖拽
            return false; // 不消费事件，让PopupWindow正常处理
        });
    }

    /**
     * 显示长按菜单
     */
    private void showLongPressMenu(AppItem appItem, View anchorView, int columnPosition, int position) {
        Log.d("DragDebug", "显示长按菜单，position=" + position + ", appItem=" + appItem.getAppName() + ", anchorView=" + anchorView);

        // 记录用户已经长按过应用
        AppSharePref.get().putBoolean(AppSharePref.HAS_LONG_PRESSED_APP, true);

        // 标记菜单正在显示并记录当前长按的应用信息
        isMenuShowing = true;
        currentLongPressPosition = position;
        currentLongPressItem = appItem;

        // 重置触摸位置，准备监听后续的移动事件
        downX = 0;
        downY = 0;

        // 显示毛玻璃背景
        showBlurOverlay(position);

        if (longPressPopup != null) {
            longPressPopup.showAtLocation(anchorView, appItem, columnPosition);
        }

        Log.d("DragDebug", "长按菜单显示完成，isMenuShowing=" + isMenuShowing + ", currentLongPressPosition=" + currentLongPressPosition);
    }

    /**
     * 显示毛玻璃覆盖层，突出显示选中的应用
     */
    private void showBlurOverlay(int selectedPosition) {
        // 显示毛玻璃背景（带动画）
        blurOverlay.setVisibility(View.VISIBLE);
        blurOverlay.setAlpha(0f);
        blurOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

        // 高亮选中的应用图标
        highlightSelectedApp(selectedPosition);
    }

    /**
     * 隐藏毛玻璃覆盖层
     */
    private void hideBlurOverlay() {
        Log.d("DragDebug", "hideBlurOverlay被调用");

        if (blurOverlay.getVisibility() == View.VISIBLE) {
            Log.d("DragDebug", "开始隐藏毛玻璃覆盖层");

            // 标记菜单不再显示并重置长按信息
            isMenuShowing = false;
            currentLongPressPosition = -1;
            currentLongPressItem = null;

            // 隐藏毛玻璃背景（带动画）
            blurOverlay.animate()
                    .alpha(0f)
                    .setDuration(200)
                    .withEndAction(() -> {
                        blurOverlay.setVisibility(View.GONE);
                        blurOverlay.setAlpha(1f); // 重置透明度
                        Log.d("DragDebug", "毛玻璃覆盖层隐藏完成");
                    })
                    .start();

            // 恢复所有应用图标的正常状态
            restoreAllApps();

            // 关闭弹出菜单（如果还在显示）
            if (longPressPopup != null && longPressPopup.isShowing()) {
                longPressPopup.dismiss();
            }
        } else {
            Log.d("DragDebug", "毛玻璃覆盖层已经隐藏");
        }
    }

    /**
     * 高亮选中的应用
     */
    private void highlightSelectedApp(int position) {
        if (appAdapter != null) {
            appAdapter.setHighlightPosition(position);
        }
    }

    /**
     * 恢复所有应用的正常状态
     */
    private void restoreAllApps() {
        if (appAdapter != null) {
            appAdapter.clearHighlight();
        }
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog(AppItem appItem) {
        new AlertDialog.Builder(this)
                .setTitle(R.string.delete_app)
                .setMessage(getString(R.string.delete_app_msg, appItem.getAppName()))
                .setPositiveButton(android.R.string.yes, (dialog, which) -> {
                    deleteApp(appItem);
                })
                .setNegativeButton(android.R.string.no, null)
                .show();
    }

    /**
     * 显示添加至桌面对话框
     */
    private void showAddToDesktopDialog(AppItem appItem) {
        com.xyz.host.db.DockerBean dockerBean = appItem.getDockerBean();
        if (dockerBean != null) {
            new ShortcutDialog(this, dockerBean).show();
        } else {
            Toast.makeText(this, "应用数据异常，无法添加至桌面", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 显示设置名称对话框
     */
    private void showSetNameDialog(AppItem appItem) {
        com.xyz.host.db.DockerBean dockerBean = appItem.getDockerBean();
        if (dockerBean != null) {
            new ModifyAppNameDialog(this, dockerBean.getUserId(), dockerBean.getPackageName(), dockerBean.getAppName())
                    .setModifyNameCallback(name -> {
                        // 更新DockerBean中的应用名称
                        dockerBean.setAppName(name);
                        // 更新AppItem中的应用名称
                        appItem.setAppName(name);
                        Log.d("AppDebug", "更新应用名称: " + name);
                        // 更新数据库
                        DbManager.getInstance().updateAppName(dockerBean);
                        // 刷新界面
                        appAdapter.notifyDataSetChanged();
                        Toast.makeText(this, "名称修改成功", Toast.LENGTH_SHORT).show();
                    }).show();
        } else {
            Toast.makeText(this, "应用数据异常，无法设置名称", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 删除应用
     */
    private void deleteApp(AppItem appItem) {
        try {
            com.xyz.host.db.DockerBean dockerBean = appItem.getDockerBean();
            if (dockerBean != null) {
                // 从数据库删除
                DbManager.getInstance().deleteOne(dockerBean.getUserId(), dockerBean.getPackageName());

                // 从虚拟引擎卸载
                if ("app".equals(dockerBean.getInstallType())) {
                    VCore.get().uninstallPackage(this, dockerBean.getAppInfo());
                } else {
                    List<com.xyz.host.db.DockerBean> list = DbManager.getInstance().getListByPkg(dockerBean.getPackageName());
                    if (list == null || list.isEmpty()) {
                        VCore.get().fullUninstallApk(this, dockerBean.getAppInfo());
                    }
                }

                // 刷新界面
                refreshAppList();
                Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "删除失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 显示更多设置对话框
     */
    private void showMoreSettingsDialog(AppItem appItem) {
        Log.d("MenuDebug", "showMoreSettingsDialog开始，appItem: " + (appItem != null ? appItem.getInstanceName() : "null"));

        if (appItem == null) {
            Log.e("MenuDebug", "AppItem为null");
            Toast.makeText(this, "应用信息为空，无法打开更多设置", Toast.LENGTH_SHORT).show();
            return;
        }

        com.xyz.host.db.DockerBean dockerBean = appItem.getDockerBean();
        if (dockerBean != null) {
            Log.d("MenuDebug", "DockerBean不为空，packageName: " + dockerBean.getPackageName() +
                  ", userId: " + dockerBean.getUserId() + ", appName: " + dockerBean.getAppName());

            try {
                // 获取AppInfo并检查是否为空
                com.fun.vbox.entity.VAppInfo appInfo = dockerBean.getAppInfo();
                if (appInfo != null) {
                    if (appInfo.userId != 0) {
                        if (!UserAgent.getInstance().isVipUser()) {
                            VipPaymentHelper.showVipPaymentDialog(this);
                            return;
                        }
                    }
                    Log.d("MenuDebug", "AppInfo获取成功，准备启动AppDetailActivity");
                    Log.d("MenuDebug", "AppInfo详情: packageName=" + appInfo.packageName +
                          ", userId=" + appInfo.userId + ", appMode=" + appInfo.appMode);

                    AppDetailActivity.go(this, appInfo, dockerBean.getAppName());
                    Log.d("MenuDebug", "AppDetailActivity.go调用完成");
                } else {
                    Log.e("MenuDebug", "AppInfo为null");
                    Toast.makeText(this, "应用信息获取失败，无法打开更多设置", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Log.e("MenuDebug", "启动AppDetailActivity时出错: " + e.getMessage(), e);
                Toast.makeText(this, "启动更多设置失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        } else {
            Log.e("MenuDebug", "DockerBean为null");
            Toast.makeText(this, "应用数据异常，无法打开更多设置", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 获取修改图标的资源ID
     */
    private int getChangeIconResId() {
        if (!"xiaomi".equals(CommonUtils.getMetaData(this, "UMENG_CHANNEL"))) {
            int componentEnabled = getPackageManager().getComponentEnabledSetting(
                    new ComponentName(this, SplashActivity.class.getName()));
            boolean isDefaultLauncher = PackageManager.COMPONENT_ENABLED_STATE_DEFAULT == componentEnabled
                    || PackageManager.COMPONENT_ENABLED_STATE_ENABLED == componentEnabled;
            if (isDefaultLauncher) {
                return R.drawable.ic_calculator;
            } else {
                return R.drawable.ic_launcher;
            }
        }
        return R.drawable.ic_launcher; // 默认图标
    }

    /**
     * 创建底部Tab视图
     */
    private void createBottomTabs(List<BottomTab> tabList) {
        llBottomTabs.removeAllViews();

        for (int i = 0; i < tabList.size(); i++) {
            BottomTab item = tabList.get(i);
            View tabView = createTabView(item, i);

            // 设置权重，让Tab平分宽度
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.MATCH_PARENT, 1.0f);
            tabView.setLayoutParams(params);

            llBottomTabs.addView(tabView);
        }
    }

    /**
     * 创建单个Tab视图
     */
    private View createTabView(BottomTab item, int position) {
        View view = LayoutInflater.from(this).inflate(R.layout.item_bottom_tab, null);

        ImageView ivIcon = view.findViewById(R.id.iv_tab_icon);
        TextView tvTitle = view.findViewById(R.id.tv_tab_title);
        ImageView ivRedDot = view.findViewById(R.id.iv_red_dot);

        ivIcon.setImageResource(item.getIconResId());
        tvTitle.setText(item.getTitle());
        ivRedDot.setVisibility(item.isHasRedDot() ? View.VISIBLE : View.GONE);

        // 如果是添加按钮，设置特殊样式
        if (item.isAddButton()) {
            ivIcon.setBackgroundResource(R.drawable.shape_add_button_bg);
            ivIcon.setPadding(6, 6, 6, 6);
            tvTitle.setTextColor(getResources().getColor(android.R.color.white));
        } else {
            ivIcon.setBackground(null);
            ivIcon.setPadding(0, 0, 0, 0);
            tvTitle.setTextColor(getResources().getColor(android.R.color.white));
        }

        // 设置点击事件
        view.setOnClickListener(v -> {
            if (item.getTabType() != null) {
                // 使用新的Tab类型处理方式
                handleTabClick(item.getTabType());
            } else {
                // 兼容旧的处理方式
                handleLegacyTabClick(item);
            }
        });

        return view;
    }

    /**
     * 实现TabClickHandler接口 - 处理Tab点击事件
     */
    @Override
    public void handleTabClick(TabType tabType) {
        switch (tabType) {
            case VIP:
                StatAgent.onEvent(this, "tool", "name", "vip");
                // 调用VIP支付弹窗而不是跳转到VipActivity
                com.xyz.host.home.VipPaymentHelper.showVipPaymentDialog(this);
                break;
            case CHANGE_ICON:
                handleChangeIcon();
                break;
            case ADD_APP:
                startAddAppActivity();
                break;
            case HIDE_APP:
                handleHideApp();
                break;
            case SET_PASSWORD:
                handleSetPassword();
                break;
            case LOCATION_PROTECT:
                handleLocationProtect();
                break;
            case MY_CENTER:
                handleMyCenter();
                break;
            default:
                Toast.makeText(this, "未知Tab类型：" + tabType, Toast.LENGTH_SHORT).show();
                break;
        }
    }

    /**
     * 兼容旧的Tab点击处理方式
     */
    private void handleLegacyTabClick(BottomTab item) {
        if (item.isAddButton()) {
            // 启动添加应用界面
            startAddAppActivity();
        } else if ("我的".equals(item.getTitle())) {
            // 启动个人中心页面
            handleMyCenter();
        } else if ("去广告".equals(item.getTitle())) {
            // 启动去广告（VIP购买）弹窗
            StatAgent.onEvent(this, "tool", "name", "vip");
            com.xyz.host.home.VipPaymentHelper.showVipPaymentDialog(this);
        } else if ("修改图标".equals(item.getTitle())) {
            // 修改图标功能
            handleChangeIcon();
        } else if ("隐藏应用".equals(item.getTitle())) {
            // 隐藏应用功能
            handleHideApp();
        } else if ("位置保护".equals(item.getTitle())) {
            // 位置保护功能
            handleLocationProtect();
        } else {
            Toast.makeText(this, "点击了：" + item.getTitle(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 刷新底部Tab（当配置改变时调用）
     */
    public void refreshBottomTabs() {
        List<BottomTab> tabList = createStaticTabData();
        createBottomTabs(tabList);
    }

    /**
     * 配置底部Tab
     */
    private void configureBottomTabs() {
        // 根据审核开关配置Tab
        boolean isReviewMode = Boolean.parseBoolean(UMRemoteConfig.getInstance().getConfigValue("review_mode"));
        TabConfig.setReviewMode(isReviewMode);

        // 根据用户状态配置Tab
//        boolean isVip = UserAgent.getInstance().isVipUser();
//        if (isVip) {
//            // VIP用户不显示"去广告"Tab
//            TabConfig.setTabEnabled(TabType.VIP, false);
//        }

        // 可以根据需要添加更多配置逻辑
        // 例如：根据渠道、用户等级、功能开关等进行配置
    }

    /**
     * 获取审核模式开关状态
     * 可以从远程配置、SharedPreferences或其他地方获取
     */
    private boolean getReviewModeSwitch() {
//        // 方式1：从远程配置获取
//        try {
//            String reviewMode = UMRemoteConfig.getInstance().getConfigValue("review_mode_switch");
//            return "true".equals(reviewMode);
//        } catch (Exception e) {
//            // 如果获取失败，默认为正常模式
//        }
//
//        // 方式2：从SharedPreferences获取（可选）
//        // return AppSharePref.get().getBoolean("review_mode_switch", false);
//
//        // 方式3：根据渠道判断（可选）
//        // String channel = CommonUtils.getMetaData(this, "UMENG_CHANNEL");
//        // return "review".equals(channel);
//
//        // 默认为审核模式（不显示位置保护）
        return true;
    }

    /**
     * 配置Tab示例方法 - 可以在需要时调用来改变Tab配置
     */
    public void configureTabsExample() {
        // 示例1：改变Tab顺序
        // TabConfig.setTabOrder(Arrays.asList(TabType.ADD_APP, TabType.MY_CENTER, TabType.VIP, TabType.HIDE_APP));

        // 示例2：禁用某些Tab
        // TabConfig.setTabEnabled(TabType.CHANGE_ICON, false);

        // 示例3：交换两个Tab的位置
        // TabConfig.swapTabs(TabType.VIP, TabType.MY_CENTER);

        // 刷新界面
        // refreshBottomTabs();
    }

    /**
     * 处理修改图标功能
     */
    private void handleChangeIcon() {
        if (!"xiaomi".equals(CommonUtils.getMetaData(this, "UMENG_CHANNEL"))) {
            int componentEnabled = getPackageManager().getComponentEnabledSetting(
                    new ComponentName(this, SplashActivity.class.getName()));
            boolean isDefaultLauncher = PackageManager.COMPONENT_ENABLED_STATE_DEFAULT == componentEnabled
                    || PackageManager.COMPONENT_ENABLED_STATE_ENABLED == componentEnabled;

            if (!UserAgent.getInstance().isVipUser()) {
                String codeId = AdIdByReviewMode.getAdId("app_icon_ads_id");
                new TtFullScreenAd().load(this, codeId);
            }

            StatAgent.onEvent(this, "tool", "name", "appIcon");
            new CommonDialog.Builder(this)
                    .setTitle(R.string.notice)
                    .setMessage("设置图标会退出应用，可以去桌面找到图标")
                    .setPositiveButton(R.string.ensure)
                    .setNegativeButton(R.string.cancel)
                    .setCallback(new CommonDialog.Callback() {
                        @Override
                        public void onOk() {
                            camoApp(isDefaultLauncher);
                        }
                    }).show();
        } else {
            Toast.makeText(this, "当前渠道不支持修改图标", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 切换应用图标
     */
    private void camoApp(boolean isSetCalc) {
        if (isSetCalc) {
            getPackageManager().setComponentEnabledSetting(new ComponentName(this, SplashActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
            getPackageManager().setComponentEnabledSetting(new ComponentName(this, SplashCalculatorActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        } else {
            getPackageManager().setComponentEnabledSetting(new ComponentName(this, SplashCalculatorActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
            getPackageManager().setComponentEnabledSetting(new ComponentName(this, SplashActivity.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }

        // 延迟更新底部Tab图标，确保组件状态已经改变
        llBottomTabs.postDelayed(() -> {
            updateChangeIconTab();
        }, 100);
    }

    /**
     * 更新修改图标Tab的图标
     */
    private void updateChangeIconTab() {
        // 找到修改图标Tab（第2个tab，索引为1）
        if (llBottomTabs.getChildCount() > 1) {
            View changeIconTab = llBottomTabs.getChildAt(1);
            if (changeIconTab != null) {
                ImageView ivIcon = changeIconTab.findViewById(R.id.iv_tab_icon);
                if (ivIcon != null) {
                    // 获取当前应该显示的图标资源ID
                    int newIconResId = getChangeIconResId();
                    ivIcon.setImageResource(newIconResId);
                }
            }
        }
    }

    /**
     * 处理隐藏应用功能
     */
    private void handleHideApp() {
        if (!UserAgent.getInstance().isVipUser()) {
            String codeId = AdIdByReviewMode.getAdId("hide_ads_id");
            new TtFullScreenAd().load(this, codeId);
        }

        StatAgent.onEvent(this, "tool", "name", "appHide");
        Intent intent = new Intent(this, AddAppActivity.class);
        intent.putExtra("type", "app_hide");
        startActivityForResult(intent, REQUEST_ADD_APP);
    }

    /**
     * 处理位置保护功能
     */
    private void handleLocationProtect() {
        // TODO: 跳转到位置保护页面
        Toast.makeText(this, "位置保护功能开发中...", Toast.LENGTH_SHORT).show();
    }

    /**
     * 处理修改密码功能
     */
    private void handleSetPassword() {
        if (!UserAgent.getInstance().isVipUser()) {
            String codeId = AdIdByReviewMode.getAdId("pass_full_ads_id");
            new TtFullScreenAd().load(this, codeId);
        }

        StatAgent.onEvent(this, "tool", "name", "setPass");
        new NumberInputDialog(this).show();
    }

    /**
     * 处理个人中心功能
     */
    private void handleMyCenter() {
        // 启动新的个人中心Activity
        MyActivity.start(this);
    }

    /**
     * 启动添加应用界面
     */
    private void startAddAppActivity() {
        StatAgent.onEvent(this, "add_app");
        // 隐藏引导
        hideGuide();

        Intent intent = new Intent(this, AddAppActivity.class);
        startActivityForResult(intent, REQUEST_ADD_APP);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_ADD_APP && resultCode == 101 && data != null) {
            AppInfo appInfo = data.getParcelableExtra("appInfo");
            String type = data.getStringExtra("type");

            if (appInfo != null) {
                installApp(appInfo, type);
            }
        } else if (requestCode == ApkUtils.UNINSTALL_REQUEST_CODE) {
            // 处理卸载结果
            if (resultCode == RESULT_OK) {
                Toast.makeText(this, "应用卸载成功，隐藏完成", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "应用卸载取消或失败", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 安装应用分身
     */
    private void installApp(AppInfo appInfo, String type) {
        new Thread(() -> {
            try {
                String installType = appInfo.getInstallType();
                String packageName = appInfo.getPackageName();
                // 获取下一个用户ID
                int userId = DbManager.getInstance().getNextUserId(appInfo.getPackageName());

                appInfo.setUserId(userId);

                boolean is64bit;
                if ("app".equals(installType)) {
                    is64bit = AbiUtils.is64BitAbi(appInfo.getApplicationInfo());
                } else {
                    is64bit = AbiUtils.is64BitAbi2(appInfo.getApplicationInfo());
                }
                if (!is64bit) {
                    if (InstallExtUtils.handlerInstall(this)) {
                        return;
                    }
                }

                boolean isInstallSuccess;
                VAppInfo vAppInfo = VAppInfo.makeFromSys(packageName, userId);
                if ("app".equals(installType)) {
                    vAppInfo.appMode = VAppInfo.MODE_APP_FROM_SYSTEM;
                    vAppInfo.is64bit = is64bit;
                } else {
                    vAppInfo.appMode = VAppInfo.MODE_APP_FROM_FILE;
                    vAppInfo.is64bit = is64bit;

                    File file = new File(appInfo.getApkPath());
                    Uri uri;
                    if (appInfo.getApkPath().startsWith("/data/app/")) {
                        vAppInfo.path = Uri.parse("package:" + packageName).toString();
                    } else {
                        uri = FileProvider.getUriForFile(MainApp.getApp(), VCore.getConfig().getHostPackageName().concat(".provider"), file);
                        if (AbiUtils.isRunExt(vAppInfo)) {
                            MainApp.getApp().grantUriPermission(VCore.getConfig().getExtPackageName(), uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                        }
                        vAppInfo.path = uri.toString();
                    }
                }
                isInstallSuccess = VCore.get().installPackage(this, vAppInfo);

                runOnUiThread(() -> {
                    if (isInstallSuccess) {
                        // 添加到数据库
                        DbManager.getInstance().addApp(
                                appInfo.getPackageName(),
                                userId,
                                appInfo.getAppName(),
                                appInfo.getAppIcon(),
                                appInfo.getInstallType(),
                                appInfo.getApkPath(),
                                is64bit
                        );

                        Toast.makeText(this, "应用分身创建成功", Toast.LENGTH_SHORT).show();

                        // 刷新应用列表
                        refreshAppList();

                        // 检查是否需要显示长按提示
                        checkAndShowLongPressTip();

                        // 如果是隐藏应用，显示卸载提示对话框
                        if ("app_hide".equals(type)) {
                            new CommonDialog.Builder(this)
                                    .setTitle("提示")
                                    .setMessage("想要完全隐藏还需要卸载应用，是否现在卸载？")
                                    .setNegativeButton(R.string.cancel)
                                    .setPositiveButton(R.string.ensure)
                                    .setCallback(new CommonDialog.Callback() {
                                        @Override
                                        public void onOk() {
                                            // 调用系统卸载功能
                                            ApkUtils.uninstall(MultiAppActivity.this, appInfo.getPackageName(), ApkUtils.UNINSTALL_REQUEST_CODE);
                                        }
                                    }).show();
                        }

                    } else {
                        Toast.makeText(this, "应用分身创建失败", Toast.LENGTH_SHORT).show();
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
                runOnUiThread(() -> {
                    Toast.makeText(this, "安装失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }

    /**
     * 刷新应用列表
     */
    private void refreshAppList() {
        // 重新加载应用数据
        List<AppItem> appList = createAppData();
        appAdapter.updateData(appList);

        // 重新检查是否需要显示引导
        checkAndShowGuide(appList);
    }

    /**
     * 处理从MyActivity转发过来的结果
     */
    private void handleForwardedResult() {
        Intent intent = getIntent();
        if (intent != null && intent.getBooleanExtra("forward_result", false)) {
            // 从Bundle中获取转发的结果数据
            Bundle resultBundle = intent.getBundleExtra("result_bundle");
            if (resultBundle != null) {
                // 直接从Bundle中获取AppInfo对象
                com.xyz.host.db.AppInfo appInfo = resultBundle.getParcelable("appInfo");
                String type = resultBundle.getString("type");

                if (appInfo != null) {
                    // 如果AppInfo没有图标，尝试获取
                    if (appInfo.getAppIcon() == null) {
                        Bitmap appIcon = getAppIcon(appInfo.getPackageName(),
                                appInfo.getApkPath(),
                                appInfo.getInstallType());
                        appInfo.setAppIcon(appIcon);
                    }

                    // 处理转发的结果
                    installApp(appInfo, type);

                    // 清除Intent中的转发标记，避免重复处理
                    intent.removeExtra("forward_result");
                    intent.removeExtra("result_bundle");

                    // 显示成功提示
                    Toast.makeText(this, "应用隐藏操作已完成", Toast.LENGTH_SHORT).show();
                }
            }
        }
    }

    /**
     * 获取应用图标
     */
    private Bitmap getAppIcon(String packageName, String apkPath, String installType) {
        try {
            if ("file".equals(installType) && apkPath != null) {
                // 从APK文件获取图标
                PackageManager pm = getPackageManager();
                PackageInfo packageInfo = pm.getPackageArchiveInfo(apkPath, PackageManager.GET_ACTIVITIES);
                if (packageInfo != null) {
                    ApplicationInfo appInfo = packageInfo.applicationInfo;
                    appInfo.sourceDir = apkPath;
                    appInfo.publicSourceDir = apkPath;
                    Drawable drawable = appInfo.loadIcon(pm);
                    return CommonUtils.drawable2Bitmap(drawable);
                }
            } else {
                // 从系统已安装应用获取图标
                PackageManager pm = getPackageManager();
                ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
                Drawable drawable = appInfo.loadIcon(pm);
                return CommonUtils.drawable2Bitmap(drawable);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 启动应用分身
     */
    private void launchApp(AppItem appItem) {
        if (appItem == null || appItem.getDockerBean() == null) {
            Toast.makeText(this, "应用数据异常", Toast.LENGTH_SHORT).show();
            return;
        }

        com.xyz.host.db.DockerBean bean = appItem.getDockerBean();
        String installType = bean.getInstallType();
        boolean is64bit;

        // 检查应用类型和架构
        if ("app".equals(installType)) {
            if (!VCore.get().isOutsideInstalled(bean.getPackageName())) {
                Toast.makeText(this, "请先在手机中安装" + bean.getAppName(), Toast.LENGTH_SHORT).show();
                return;
            }
            is64bit = AbiUtils.is64BitAbi(bean.getPackageName());
        } else {
            is64bit = "64".equals(bean.getAppBit());
        }

        // 检查32位应用支持
        if (!is64bit) {
            if (InstallExtUtils.handlerInstall(this)) {
                return;
            }
        }

        if (!UserAgent.getInstance().isVipUser()) {
            String value = UMRemoteConfig.getInstance().getConfigValue("free_app_count");
            int freeAppCount = 1;
            try {
                freeAppCount = Integer.parseInt(value);
            } catch (Throwable e) {
                // Ignored.
            }
            if (appAdapter.getItemCount() > freeAppCount || bean.getUserId() != 0) {
                String rewardTip = "";
                try {
                    String strValue = UMRemoteConfig.getInstance().getConfigValue("reward_min");
                    mRewardMin = Integer.parseInt(strValue);
                    rewardTip = "奖励" + strValue + "分钟";
                } catch (Throwable e) {
                    // Ignored.
                }

                if (mRewardHandler.handleReward(this, new Runnable() {
                    boolean isLaunched = false;

                    @Override
                    public void run() {
                        showRewardTime();
                        if (isLaunched) {
                            return;
                        }
                        launchApp(bean, is64bit);
                        isLaunched = true;
                    }
                })) {
                    return;
                }
            }
        }

        launchApp(bean, is64bit);

        // 如果是新应用（显示绿点），启动后隐藏绿点
        if (appItem.isNew()) {
            appItem.setNew(false); // 标记为已启动，隐藏绿点
            // 刷新适配器以更新UI
            if (appAdapter != null) {
                appAdapter.notifyDataSetChanged();
            }
        }

        Toast.makeText(this, "正在启动：" + appItem.getAppName(), Toast.LENGTH_SHORT).show();
    }

    private void launchApp(DockerBean bean, boolean is64bit) {
        ShortcutHandleActivity.go(this, bean);
        DbManager.getInstance().updateOpenByPkg(bean);
        if (is64bit) {
            StatAgent.onEvent(this, "dual_launch", "name", bean.getAppName());
        } else {
            StatAgent.onEvent(this, "dual_launch_32", "name", bean.getAppName());
        }
    }

    /**
     * 网格间距装饰器，用于实现iPhone风格的图标间距
     */
    public static class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {
        private int spanCount;
        private int spacing;
        private boolean includeEdge;

        public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
            this.spanCount = spanCount;
            this.spacing = spacing;
            this.includeEdge = includeEdge;
        }

        @Override
        public void getItemOffsets(Rect outRect, android.view.View view, RecyclerView parent, RecyclerView.State state) {
            int position = parent.getChildAdapterPosition(view);
            int column = position % spanCount;

            if (includeEdge) {
                outRect.left = spacing - column * spacing / spanCount;
                outRect.right = (column + 1) * spacing / spanCount;

                if (position < spanCount) {
                    outRect.top = spacing;
                }
                outRect.bottom = spacing;
            } else {
                outRect.left = column * spacing / spanCount;
                outRect.right = spacing - (column + 1) * spacing / spanCount;
                if (position >= spanCount) {
                    outRect.top = spacing;
                }
            }
        }
    }

    /**
     * 初始化引导覆盖层
     */
    private void initGuideOverlay() {
        // 动态创建引导覆盖层
        guideOverlay = LayoutInflater.from(this).inflate(R.layout.guide_overlay, null);
        FrameLayout rootLayout = (FrameLayout) findViewById(android.R.id.content);
        rootLayout.addView(guideOverlay);
        guideOverlay.setVisibility(View.GONE);

        // 设置气泡按钮点击事件（点击启动添加应用流程）
        TextView tvGuideAddButton = guideOverlay.findViewById(R.id.tv_guide_add_button);
        if (tvGuideAddButton != null) {
            tvGuideAddButton.setOnClickListener(v -> {
                // 关闭引导并启动添加应用流程
                hideGuide();
                startAddAppActivity();
            });
        }

        // 设置覆盖层点击事件（点击空白区域关闭引导）
        // 注意：这个要放在最后，避免覆盖子视图的点击事件
        guideOverlay.setOnClickListener(v -> hideGuide());
    }

    /**
     * 检查并显示新手引导
     */
    private void checkAndShowGuide(List<AppItem> appList) {
        if (appList == null || appList.isEmpty()) {
            showGuide();
        }
    }

    /**
     * 显示新手引导
     */
    private void showGuide() {
        if (guideOverlay != null) {
            guideOverlay.setVisibility(View.VISIBLE);

            // 找到"添加"按钮并高亮显示
            highlightAddButton();
        }
    }

    /**
     * 隐藏新手引导
     */
    private void hideGuide() {
        if (guideOverlay != null) {
            guideOverlay.setVisibility(View.GONE);

            // 停止添加按钮的动画并恢复原始大小
            if (llBottomTabs.getChildCount() > 2) {
                View addButton = llBottomTabs.getChildAt(2);
                if (addButton != null) {
                    addButton.clearAnimation();
                    addButton.setScaleX(1.0f);
                    addButton.setScaleY(1.0f);
                }
            }
        }
    }

    /**
     * 高亮显示添加按钮
     */
    private void highlightAddButton() {
        // 延迟执行，确保底部tab已经创建完成
        llBottomTabs.post(() -> {
            // 找到"添加"按钮（第3个tab，索引为2）
            if (llBottomTabs.getChildCount() > 2) {
                View addButton = llBottomTabs.getChildAt(2);
                if (addButton != null) {
                    // 创建持续的脉冲动画效果
                    startPulseAnimation(addButton);
                }
            }
        });
    }

    /**
     * 开始脉冲动画
     */
    private void startPulseAnimation(View view) {
        view.animate()
                .scaleX(1.15f)
                .scaleY(1.15f)
                .setDuration(800)
                .withEndAction(() -> {
                    view.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(800)
                            .withEndAction(() -> {
                                // 如果引导还在显示，继续动画
                                if (guideOverlay != null && guideOverlay.getVisibility() == View.VISIBLE) {
                                    startPulseAnimation(view);
                                }
                            })
                            .start();
                })
                .start();
    }

    /**
     * 检查并显示长按提示
     */
    private void checkAndShowLongPressTip() {
        // 检查用户是否已经长按过应用
        boolean hasLongPressed = AppSharePref.get().getBoolean(AppSharePref.HAS_LONG_PRESSED_APP, false);

        if (!hasLongPressed) {
            // 延迟显示提示，让用户先看到新添加的应用
            rvAppGrid.postDelayed(() -> {
                showLongPressTip();
            }, 1000);
        }
    }

    /**
     * 显示长按提示对话框
     */
    private void showLongPressTip() {
        new CommonDialog.Builder(this)
                .setTitle("💡 小贴士")
                .setMessage("您可以长按应用图标，发现更多实用功能哦！")
                .setPositiveButton("我知道了")
                .setNegativeButton("不再提示")
                .setCallback(new CommonDialog.Callback() {
                    @Override
                    public void onOk() {
                        // 用户点击"我知道了"，不做任何操作，下次还会提示
                    }

                    @Override
                    public void onCancel() {
                        // 用户点击"不再提示"，记录已经长按过，不再显示提示
                        AppSharePref.get().putBoolean(AppSharePref.HAS_LONG_PRESSED_APP, true);
                    }
                })
                .show();
    }

    /**
     * 加载Banner广告
     */
    private void loadBannerAd() {
        String codeId = AdIdByReviewMode.getAdId("home_banner_ads_id");
        if (TextUtils.isEmpty(codeId)) {
            // 没有广告配置，确保广告容器隐藏，BottomTabs保持原位置
            mBannerContainer.setVisibility(View.GONE);
            adjustBottomTabsForAd(false);
            return;
        }
        TTAdNative mAdNativeLoader = TTAdManagerHolder.get().createAdNative(this);
        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId(codeId)
                .setAdCount(1)
                .setExpressViewAcceptedSize(UIUtils.getScreenWidthDp(MultiAppActivity.this), BANNER_AD_HEIGHT_DP)
                .build();
        mAdNativeLoader.loadBannerExpressAd(adSlot, new TTAdNative.NativeExpressAdListener() {
            @Override
            public void onError(int i, String s) {
                // android.util.Log.e("MyTest", "loadBanner:" + i + "," + s);
                mBannerContainer.removeAllViews();
                mBannerContainer.setVisibility(View.GONE);
                // 广告加载失败，确保BottomTabs保持原位置
                adjustBottomTabsForAd(false);
            }

            @Override
            public void onNativeExpressAdLoad(List<TTNativeExpressAd> list) {
                if (list != null && !list.isEmpty()) {
                    mBannerAd = list.get(0);
                    mBannerAd.setSlideIntervalTime(30 * 1000);
                    showBannerAd();
                }
            }
        });
    }

    /**
     * 显示Banner广告
     */
    private void showBannerAd() {
        if (mBannerAd != null) {
            mBannerAd.setExpressInteractionListener(new TTNativeExpressAd.ExpressAdInteractionListener() {
                @Override
                public void onAdClicked(View view, int i) {

                }

                @Override
                public void onAdShow(View view, int i) {

                }

                @Override
                public void onRenderFail(View view, String s, int i) {

                }

                @Override
                public void onRenderSuccess(View view, float v, float v1) {
                    // 广告渲染成功，调整BottomTabs位置
                    adjustBottomTabsForAd(true);
                }
            });
            mBannerAd.setDislikeCallback(this, new TTAdDislike.DislikeInteractionCallback() {
                @Override
                public void onShow() {
                }

                @Override
                public void onSelected(int i, String s, boolean b) {
                    mBannerContainer.removeAllViews();
                    mBannerContainer.setVisibility(View.GONE);
                    // 广告被关闭，恢复BottomTabs位置
                    adjustBottomTabsForAd(false);
                }

                @Override
                public void onCancel() {
                }
            });
            View bannerView = mBannerAd.getExpressAdView();
            if (bannerView != null) {
                mBannerContainer.removeAllViews();
                mBannerContainer.addView(bannerView);
                mBannerContainer.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 根据广告状态调整BottomTabs位置
     */
    private void adjustBottomTabsForAd(boolean hasAd) {
        if (bottomTabsContainer == null) return;

        // 由于现在BottomTabs和广告在同一个LinearLayout中，
        // BottomTabs会自动显示在广告上方，不需要手动调整margin
        // 这个方法保留是为了将来可能的扩展需求
    }

    /**
     * 初始化拖拽功能
     */
    private void initDragHelper() {
        Log.d("DragDebug", "初始化拖拽功能");

        dragHelper = new AppDragHelper(appAdapter);
        dragHelper.setOnDragListener(new AppDragHelper.OnDragListener() {
            @Override
            public void onDragStart() {
                Log.d("DragDebug", "拖拽开始回调");
                isDragging = true;
                // 确保菜单状态正确，但不要清空currentLongPressItem，因为我们还需要它来验证
                isMenuShowing = false;
                // 注释掉这两行，避免在拖拽过程中丢失信息
                // currentLongPressPosition = -1;
                // currentLongPressItem = null;
            }

            @Override
            public void onDragEnd() {
                Log.d("DragDebug", "拖拽结束回调");
                isDragging = false;
                // 拖拽结束后清空状态变量
                currentLongPressPosition = -1;
                currentLongPressItem = null;
                // 拖拽结束后保存应用顺序
                saveAppOrder();
            }

            @Override
            public void onItemMove(int fromPosition, int toPosition) {
                Log.d("DragDebug", "拖拽移动: " + fromPosition + " -> " + toPosition);
                // 在拖拽过程中实时更新顺序（已在AppDragHelper中处理）
            }
        });

        itemTouchHelper = new ItemTouchHelper(dragHelper);
        itemTouchHelper.attachToRecyclerView(rvAppGrid);

        Log.d("DragDebug", "拖拽功能初始化完成，itemTouchHelper=" + itemTouchHelper + ", rvAppGrid=" + rvAppGrid);
    }

    /**
     * 处理全局触摸事件以检测拖拽
     */
    private void handleGlobalTouchForDrag(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 记录初始触摸位置（长按菜单显示后的第一次触摸）
                if (downX == 0 && downY == 0) {
                    downX = event.getRawX();
                    downY = event.getRawY();
                    Log.d("DragDebug", "记录初始位置: (" + downX + ", " + downY + ")");
                }
                break;

            case MotionEvent.ACTION_MOVE:
                // 如果还没有记录初始位置，先记录
                if (downX == 0 && downY == 0) {
                    downX = event.getRawX();
                    downY = event.getRawY();
                    Log.d("DragDebug", "在MOVE中记录初始位置: (" + downX + ", " + downY + ")");
                    return;
                }

                // 检查是否在长按菜单显示状态下移动
                if (isMenuShowing && !isDragging) {
                    float deltaX = Math.abs(event.getRawX() - downX);
                    float deltaY = Math.abs(event.getRawY() - downY);
                    float threshold = DRAG_THRESHOLD * getResources().getDisplayMetrics().density;

                    Log.d("DragDebug", "移动检测: deltaX=" + deltaX + ", deltaY=" + deltaY + ", threshold=" + threshold);

                    // 如果移动距离超过阈值，开始拖拽
                    if (deltaX > threshold || deltaY > threshold) {
                        Log.d("DragDebug", "开始拖拽！");
                        startDragFromMenu();
                    }
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // 重置触摸位置
                Log.d("DragDebug", "重置触摸位置");
                downX = 0;
                downY = 0;
                break;
        }
    }

    /**
     * 从长按菜单状态开始拖拽
     */
    private void startDragFromMenu() {
        Log.d("DragDebug", "startDragFromMenu被调用，position=" + currentLongPressPosition + ", appItem=" + (currentLongPressItem != null ? currentLongPressItem.getInstanceName() : "null"));

        if (currentLongPressPosition != -1 && itemTouchHelper != null && currentLongPressItem != null) {
            // 打印当前所有应用的信息，用于调试
            Log.d("DragDebug", "当前应用列表:");
            for (int i = 0; i < appAdapter.getAppList().size(); i++) {
                AppItem item = appAdapter.getAppList().get(i);
                Log.d("DragDebug", "  位置" + i + ": " + item.getInstanceName());
            }

            // 重新查找正确的位置（通过实例名称）
            int correctPosition = -1;
            for (int i = 0; i < appAdapter.getAppList().size(); i++) {
                AppItem item = appAdapter.getAppList().get(i);
                if (currentLongPressItem.getInstanceName().equals(item.getInstanceName())) {
                    correctPosition = i;
                    Log.d("DragDebug", "通过实例名称找到正确位置: " + correctPosition);
                    break;
                }
            }

            if (correctPosition == -1) {
                Log.e("DragDebug", "无法找到应用 " + currentLongPressItem.getInstanceName() + " 的位置");
                hideBlurOverlay();
                return;
            }

            // 更新正确的位置
            currentLongPressPosition = correctPosition;

            // 直接通过实例名称查找ViewHolder，不刷新适配器
            Log.d("DragDebug", "直接通过实例名称查找ViewHolder");
            RecyclerView.ViewHolder viewHolder = null;

            // 遍历所有可见的ViewHolder并通过实例名称匹配
            Log.d("DragDebug", "遍历查找ViewHolder，RecyclerView子视图数量: " + rvAppGrid.getChildCount());
            for (int i = 0; i < rvAppGrid.getChildCount(); i++) {
                View child = rvAppGrid.getChildAt(i);
                RecyclerView.ViewHolder holder = rvAppGrid.getChildViewHolder(child);
                int adapterPos = holder.getAdapterPosition();

                if (adapterPos >= 0 && adapterPos < appAdapter.getAppList().size()) {
                    AppItem item = appAdapter.getAppList().get(adapterPos);
                    if (item != null && currentLongPressItem != null) {
                        Log.d("DragDebug", "子视图" + i + ": adapterPosition=" + adapterPos + ", 实例=" + item.getInstanceName());

                        if (currentLongPressItem.getInstanceName().equals(item.getInstanceName())) {
                            viewHolder = holder;
                            currentLongPressPosition = adapterPos; // 更新位置
                            Log.d("DragDebug", "通过实例名称匹配找到ViewHolder，位置=" + adapterPos);
                            break;
                        }
                    } else {
                        Log.d("DragDebug", "子视图" + i + ": adapterPosition=" + adapterPos + ", 实例=" + (item != null ? item.getInstanceName() : "null"));
                    }
                } else {
                    Log.d("DragDebug", "子视图" + i + ": adapterPosition=" + adapterPos + " (无效位置)");
                }
            }

            // 创建final引用以在lambda中使用
            final RecyclerView.ViewHolder finalViewHolder = viewHolder;
            final AppItem finalLongPressItem = currentLongPressItem; // 保存当前长按的应用信息

            // 立即隐藏菜单
            hideBlurOverlay();

            // 延迟启动拖拽，确保菜单隐藏完成
            rvAppGrid.postDelayed(() -> {
                if (finalViewHolder != null && finalLongPressItem != null) {
                    // 在启动拖拽前再次验证ViewHolder是否正确
                    int actualPosition = finalViewHolder.getAdapterPosition();
                    int layoutPosition = finalViewHolder.getLayoutPosition();
                    Log.d("DragDebug", "延迟启动拖拽前验证: ViewHolder位置=" + actualPosition + ", layoutPosition=" + layoutPosition);
                    Log.d("DragDebug", "ViewHolder详细状态: " + finalViewHolder.toString());
                    Log.d("DragDebug", "是否为第一排应用: " + (actualPosition < 4));

                    if (actualPosition >= 0 && actualPosition < appAdapter.getAppList().size()) {
                        AppItem actualItem = appAdapter.getAppList().get(actualPosition);
                        if (actualItem != null) {
                            Log.d("DragDebug", "延迟验证: 期望=" + finalLongPressItem.getInstanceName() + ", 实际=" + actualItem.getInstanceName() + ", position=" + actualPosition);

                            if (finalLongPressItem.getInstanceName().equals(actualItem.getInstanceName())) {
                                try {
                                    Log.d("DragDebug", "延迟验证通过，启动拖拽");
                                    Log.d("DragDebug", "ItemTouchHelper状态: " + itemTouchHelper);
                                    Log.d("DragDebug", "ViewHolder可拖拽性检查...");

                                    itemTouchHelper.startDrag(finalViewHolder);
                                    Log.d("DragDebug", "拖拽已启动");
                                } catch (Exception e) {
                                    Log.e("DragDebug", "启动拖拽时出错: " + e.getMessage());
                                    e.printStackTrace();
                                }
                            } else {
                                Log.e("DragDebug", "延迟验证失败！期望=" + finalLongPressItem.getInstanceName() + ", 实际=" + actualItem.getInstanceName());
                                Log.e("DragDebug", "ViewHolder在延迟期间发生了变化，重新查找");

                                // 重新查找正确的ViewHolder
                                RecyclerView.ViewHolder correctViewHolder = findViewHolderByInstanceName(finalLongPressItem.getInstanceName());
                                if (correctViewHolder != null) {
                                    Log.d("DragDebug", "重新找到正确的ViewHolder，启动拖拽");
                                    try {
                                        itemTouchHelper.startDrag(correctViewHolder);
                                        Log.d("DragDebug", "重新拖拽已启动");
                                    } catch (Exception e) {
                                        Log.e("DragDebug", "重新启动拖拽时出错: " + e.getMessage());
                                    }
                                } else {
                                    Log.e("DragDebug", "重新查找也失败了");
                                }
                            }
                        } else {
                            Log.e("DragDebug", "延迟验证时actualItem为null");
                        }
                    } else {
                        Log.e("DragDebug", "延迟验证时ViewHolder位置无效: " + actualPosition);
                        Log.e("DragDebug", "ViewHolder已失效，重新查找有效的ViewHolder");

                        // ViewHolder失效，重新查找
                        RecyclerView.ViewHolder correctViewHolder = findViewHolderByInstanceName(finalLongPressItem.getInstanceName());
                        if (correctViewHolder != null) {
                            int newPosition = correctViewHolder.getAdapterPosition();
                            Log.d("DragDebug", "重新找到有效的ViewHolder，位置=" + newPosition);
                            try {
                                itemTouchHelper.startDrag(correctViewHolder);
                                Log.d("DragDebug", "使用新ViewHolder启动拖拽成功");
                            } catch (Exception e) {
                                Log.e("DragDebug", "使用新ViewHolder启动拖拽失败: " + e.getMessage());
                                e.printStackTrace();
                            }
                        } else {
                            Log.e("DragDebug", "无法找到有效的ViewHolder");
                        }
                    }
                }
            }, 50); // 进一步减少延迟时间，减少ViewHolder失效的可能性

            // 菜单已经在上面隐藏了
        } else {
            Log.d("DragDebug", "条件不满足：position=" + currentLongPressPosition + ", itemTouchHelper=" + itemTouchHelper + ", currentLongPressItem=" + currentLongPressItem);
        }
    }


    /**
     * 通过实例名称查找ViewHolder（支持多个相同应用的不同实例）
     */
    private RecyclerView.ViewHolder findViewHolderByInstanceName(String instanceName) {
        for (int i = 0; i < rvAppGrid.getChildCount(); i++) {
            View child = rvAppGrid.getChildAt(i);
            RecyclerView.ViewHolder holder = rvAppGrid.getChildViewHolder(child);
            int adapterPos = holder.getAdapterPosition();

            if (adapterPos >= 0 && adapterPos < appAdapter.getAppList().size()) {
                AppItem item = appAdapter.getAppList().get(adapterPos);
                // 使用instanceName进行精确匹配，区分同一应用的不同实例
                if (item != null && instanceName.equals(item.getInstanceName())) {
                    Log.d("DragDebug", "找到匹配的ViewHolder: instanceName=" + instanceName + ", position=" + adapterPos);
                    return holder;
                }
            }
        }
        Log.w("DragDebug", "未找到匹配的ViewHolder: instanceName=" + instanceName);
        return null;
    }

    /**
     * 使用ViewHolder开始拖拽
     */
    private void startDragWithViewHolder(RecyclerView.ViewHolder viewHolder) {
        if (viewHolder != null && currentLongPressItem != null) {
            int actualPosition = viewHolder.getAdapterPosition();
            Log.d("DragDebug", "ViewHolder详细信息: adapterPosition=" + actualPosition + ", layoutPosition=" + viewHolder.getLayoutPosition());

            if (actualPosition >= 0 && actualPosition < appAdapter.getAppList().size()) {
                AppItem actualItem = appAdapter.getAppList().get(actualPosition);
                if (actualItem != null) {
                    Log.d("DragDebug", "最终验证: 期望=" + currentLongPressItem.getAppName() + ", 实际=" + actualItem.getAppName() + ", position=" + actualPosition);

                    if (!currentLongPressItem.getAppName().equals(actualItem.getAppName())) {
                        Log.e("DragDebug", "ViewHolder位置仍然不匹配！期望=" + currentLongPressItem.getAppName() + ", 实际=" + actualItem.getAppName());
                        return;
                    }
                } else {
                    Log.e("DragDebug", "actualItem为null，position=" + actualPosition);
                    return;
                }
            } else {
                Log.e("DragDebug", "ViewHolder位置无效: " + actualPosition);
                return;
            }

            Log.d("DragDebug", "验证通过，开始拖拽");

            try {
                Log.d("DragDebug", "启动拖拽，ViewHolder=" + viewHolder);
                itemTouchHelper.startDrag(viewHolder);
                Log.d("DragDebug", "拖拽已启动");
            } catch (Exception e) {
                Log.e("DragDebug", "启动拖拽时出错: " + e.getMessage());
            }
        } else {
            Log.e("DragDebug", "ViewHolder或currentLongPressItem为null，无法开始拖拽. viewHolder=" + viewHolder + ", currentLongPressItem=" + currentLongPressItem);
        }
    }

    /**
     * 处理毛玻璃覆盖层的触摸事件
     */
    private boolean handleBlurOverlayTouch(MotionEvent event) {
        Log.d("DragDebug", "handleBlurOverlayTouch: action=" + event.getAction() + ", downX=" + downX + ", downY=" + downY);

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 记录初始触摸位置
                downX = event.getRawX();
                downY = event.getRawY();
                Log.d("DragDebug", "毛玻璃层记录初始位置: (" + downX + ", " + downY + ")");
                return true; // 消费DOWN事件，确保能接收到后续的MOVE事件

            case MotionEvent.ACTION_MOVE:
                // 如果还没有记录初始位置，先记录
                if (downX == 0 && downY == 0) {
                    downX = event.getRawX();
                    downY = event.getRawY();
                    Log.d("DragDebug", "毛玻璃层在MOVE中记录初始位置: (" + downX + ", " + downY + ")");
                    return true;
                }

                // 计算移动距离
                float deltaX = Math.abs(event.getRawX() - downX);
                float deltaY = Math.abs(event.getRawY() - downY);
                float threshold = DRAG_THRESHOLD * getResources().getDisplayMetrics().density;

                Log.d("DragDebug", "移动检测: deltaX=" + deltaX + ", deltaY=" + deltaY + ", threshold=" + threshold + ", isMenuShowing=" + isMenuShowing + ", isDragging=" + isDragging);

                // 检查是否在长按菜单显示状态下移动
                if (isMenuShowing && !isDragging) {
                    // 如果移动距离超过阈值，开始拖拽
                    if (deltaX > threshold || deltaY > threshold) {
                        Log.d("DragDebug", "触发拖拽！准备开始拖拽...");
                        startDragFromMenu();
                        return true;
                    }
                }
                return true;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                // 重置触摸位置
                Log.d("DragDebug", "毛玻璃层重置触摸位置");
                downX = 0;
                downY = 0;
                return false; // 不消费UP事件，让点击事件正常处理
        }
        return false;
    }

    /**
     * 处理智能触摸事件
     * 在长按菜单显示时，检测移动手势并启动拖拽
     */
    private boolean handleSmartTouch(AppItem item, int position, View view, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 记录初始触摸位置
                downX = event.getRawX();
                downY = event.getRawY();
                break;
        }
        return false;
    }

    /**
     * 更新应用顺序（在拖拽过程中调用）
     */
    private void updateAppOrder(int fromPosition, int toPosition) {
        // 这里可以添加实时的顺序更新逻辑
        // 目前主要的顺序更新在saveAppOrder()中处理
    }

    /**
     * 保存应用顺序到数据库
     */
    private void saveAppOrder() {
        new Thread(() -> {
            try {
                if (appAdapter != null) {
                    List<AppItem> currentList = appAdapter.getAppList();

                    // 构建DockerBean列表用于更新顺序
                    List<com.xyz.host.db.DockerBean> dockerBeans = new ArrayList<>();
                    for (AppItem appItem : currentList) {
                        com.xyz.host.db.DockerBean dockerBean = appItem.getDockerBean();
                        if (dockerBean != null) {
                            dockerBeans.add(dockerBean);
                        }
                    }

                    // 使用DbManager的updateOrder方法更新数据库
                    if (!dockerBeans.isEmpty()) {
                        DbManager.getInstance().updateOrder(dockerBeans);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }
}