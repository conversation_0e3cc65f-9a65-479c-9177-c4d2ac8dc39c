package com.yingyu.zh.multiapp.model;

import com.yingyu.zh.multiapp.config.TabType;

/**
 * 底部Tab数据模型
 */
public class BottomTab {
    private TabType tabType; // Tab类型
    private String title;
    private int iconResId;
    private boolean isAddButton; // 是否是添加按钮
    private boolean hasRedDot; // 是否显示红点

    public BottomTab(TabType tabType, String title, int iconResId, boolean isAddButton, boolean hasRedDot) {
        this.tabType = tabType;
        this.title = title;
        this.iconResId = iconResId;
        this.isAddButton = isAddButton;
        this.hasRedDot = hasRedDot;
    }

    // 兼容旧的构造函数
    public BottomTab(String title, int iconResId, boolean isAddButton, boolean hasRedDot) {
        this.tabType = null; // 旧方式创建的Tab没有类型
        this.title = title;
        this.iconResId = iconResId;
        this.isAddButton = isAddButton;
        this.hasRedDot = hasRedDot;
    }

    public TabType getTabType() {
        return tabType;
    }

    public void setTabType(TabType tabType) {
        this.tabType = tabType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getIconResId() {
        return iconResId;
    }

    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }

    public boolean isAddButton() {
        return isAddButton;
    }

    public void setAddButton(boolean addButton) {
        isAddButton = addButton;
    }

    public boolean isHasRedDot() {
        return hasRedDot;
    }

    public void setHasRedDot(boolean hasRedDot) {
        this.hasRedDot = hasRedDot;
    }
} 