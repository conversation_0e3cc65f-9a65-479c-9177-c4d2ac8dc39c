<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.xyz.host">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />

    <application
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:name=".MainApp"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:largeHeap="true"
        android:theme="@style/Theme.DroidPluginDemo"
        tools:replace="android:allowBackup"
        tools:ignore="HardcodedDebugMode">

        <activity android:name=".home.SplashActivity"
            android:label="@string/app_name"
            android:icon="@mipmap/ic_launcher"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.DEFAULT"/>

                <data
                    android:scheme="yingyu"
                    android:host="virtual"
                    android:pathPrefix="/open"
                    />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".home.SplashCalculatorActivity"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_calculator"
            android:label="@string/calculator"
            android:targetActivity=".home.SplashActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.DEFAULT"/>

                <data
                    android:scheme="yingyu"
                    android:host="virtual"
                    android:pathPrefix="/open"
                    />
            </intent-filter>
        </activity-alias>

        <activity android:name=".home.HomeActivity" />
        <activity android:name="com.yingyu.zh.multiapp.MultiAppActivity" />

        <activity
            android:name="com.yingyu.zh.multiapp.InviteRedeemActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.yingyu.zh.multiapp.InviteRulesActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.yingyu.zh.multiapp.CouponListActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity android:name="com.yingyu.zh.multiapp.MyActivity" />
        <activity android:name="com.yingyu.zh.multiapp.PersonalCenterActivity" />
        <activity android:name=".home.WebActivity" />
        <activity android:name=".home.add.AddAppActivity" />
        <activity
            android:name=".home.SetLocationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".home.SetDeviceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".home.CheckInActivity"
            android:screenOrientation="portrait"
            android:theme="@style/MyDialogStyleBottom" />
        <activity
            android:name=".home.AppDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/MyDialogStyleBottom" />
        <activity
            android:name=".home.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/MyDialogStyleBottom" />
        <activity
            android:name=".home.VCameraActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.yingyu.zh.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity
            android:name="com.yingyu.zh.wxapi.WXPayEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity android:name=".maskprivacy.MaskPolicyActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:launchMode="singleTask" />

        <activity android:name=".maskprivacy.PolicyActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait" />

        <activity
            android:name=".home.ShortcutHandleActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:taskAffinity="virtual.shortcut.task"
            android:theme="@style/TransparentTheme">
            <intent-filter tools:ignore="AppLinkUrlError">
                <action android:name="${applicationId}.vbox.action.shortcut" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.tencent.tauth.AuthActivity"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tencent102699596" />
            </intent-filter>
        </activity>

        <service
            android:name="com.amap.api.location.APSService"
            android:foregroundServiceType="location" />

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="3fedf42665966afdeeaee3111f09f4c" />

        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="${UMENG_CHANNEL}" />

        <activity android:name=".home.AboutActivity" />
        <activity android:name=".home.VipTipActivity" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>

</manifest>