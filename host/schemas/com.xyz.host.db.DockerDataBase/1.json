{"formatVersion": 1, "database": {"version": 1, "identityHash": "c75027b742f8466559526452cd224bc5", "entities": [{"tableName": "<PERSON><PERSON><PERSON><PERSON>", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `user_id` INTEGER NOT NULL, `app_name` TEXT, `app_icon` BLOB, `package_name` TEXT, `apk_path` TEXT, `app_order` INTEGER NOT NULL, `app_bit` TEXT, `install_type` TEXT, `install_time` INTEGER NOT NULL, `is_opened` INTEGER NOT NULL, `is_outside` INTEGER NOT NULL, `outside_pkg` TEXT, `last_update_Time` INTEGER NOT NULL, `app_version_code` INTEGER NOT NULL, `extra` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "appName", "columnName": "app_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "appIcon", "columnName": "app_icon", "affinity": "BLOB", "notNull": false}, {"fieldPath": "packageName", "columnName": "package_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "apkPath", "columnName": "apk_path", "affinity": "TEXT", "notNull": false}, {"fieldPath": "appOrder", "columnName": "app_order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "appBit", "columnName": "app_bit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "installType", "columnName": "install_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "installTime", "columnName": "install_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isOpened", "columnName": "is_opened", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isOutside", "columnName": "is_outside", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "outside_pkg", "columnName": "outside_pkg", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "last_update_Time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "appVersionCode", "columnName": "app_version_code", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extra", "columnName": "extra", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_DockerBean_user_id_package_name", "unique": false, "columnNames": ["user_id", "package_name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_DockerBean_user_id_package_name` ON `${TABLE_NAME}` (`user_id`, `package_name`)"}], "foreignKeys": []}, {"tableName": "LocInfo", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `mcc` INTEGER NOT NULL, `mnc` INTEGER NOT NULL, `psc` INTEGER NOT NULL, `lac` INTEGER NOT NULL, `cid` INTEGER NOT NULL, `base_station_id` INTEGER NOT NULL, `system_id` INTEGER NOT NULL, `network_id` INTEGER NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `address` TEXT, `bssid` TEXT, `list_bssid` TEXT, `name` TEXT, `loc_order` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mcc", "columnName": "mcc", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mnc", "columnName": "mnc", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "psc", "columnName": "psc", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lac", "columnName": "lac", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cid", "columnName": "cid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "baseStationId", "columnName": "base_station_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "systemId", "columnName": "system_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "networkId", "columnName": "network_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latitude", "columnName": "latitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "longitude", "columnName": "longitude", "affinity": "REAL", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bssid", "columnName": "bssid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "listBssid", "columnName": "list_bssid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "locOrder", "columnName": "loc_order", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_LocInfo_id", "unique": false, "columnNames": ["id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_LocInfo_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c75027b742f8466559526452cd224bc5')"]}}