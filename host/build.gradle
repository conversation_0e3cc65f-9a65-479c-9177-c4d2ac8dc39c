apply plugin: 'com.android.application'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-android'
apply plugin: 'asm-plugin'
apply plugin: 'AndResGuard'

android {
    compileSdkVersion "${compile_sdk_version}" as Integer
    buildToolsVersion "${build_tools_version}"

    def verCode = Integer.parseInt(new Date().format("yyyyMMddHH"))
    defaultConfig {
        applicationId "com.yingyu.zh"
        minSdkVersion "${min_sdk_version}" as Integer
        targetSdkVersion "${target_sdk_version}" as Integer
        versionCode 101
        versionName "1.0.1"
        ndk {
            abiFilters /*"armeabi-v7a", */"arm64-v8a"
        }
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation":
                                     "$projectDir/schemas".toString()]
            }
        }
        manifestPlaceholders = [
                qqappid           : "102699596"
        ]
    }

    signingConfigs {
        release {
            storeFile file("./keystore/sign_release.jks")
            storePassword 'vDLQoHv4'
            keyAlias 'aoq'
            keyPassword '6dR6ZZKA'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'vb-proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'vb-proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    flavorDimensions "channel"

    // 渠道包定义，默认定义的名称就是渠道名称
    productFlavors {
        yingyongbao {
            dimension "channel"
        }
        share {
            dimension "channel"
        }
//        '360' {
//            dimension "channel"
//        }
//        official {
//            dimension "channel"
//        }
//        ali {
//            dimension "channel"
//        }
        xiaomi {
            dimension "channel"
        }
        honor {
            dimension "channel"
        }
//        registration {
//            dimension "channel"
//        }
    }

    // 批量渠道包值替换
    productFlavors.all { flavor ->
        flavor.manifestPlaceholders = [UMENG_CHANNEL: name]
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def fileName = "yingyu_${defaultConfig.versionName}.apk"
            outputFileName = fileName
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation fileTree(dir: "../common/libs", include: ["*.jar", "*.aar"])

    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

    implementation "androidx.room:room-runtime:2.4.1"
    annotationProcessor "androidx.room:room-compiler:2.4.1"

    implementation "com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4"

    implementation "com.google.code.gson:gson:2.9.1"
    implementation "com.tencent:mmkv-static:1.2.10"

    // 友盟
    implementation 'com.umeng.umsdk:common:9.8.0'
    implementation 'com.umeng.umsdk:abtest:1.0.0'
    implementation "com.umeng.umsdk:asms:1.8.6"

    // 友盟
    implementation 'com.umeng.umsdk:share-core:7.3.5'
    implementation 'com.umeng.umsdk:share-qq:7.3.5'
    implementation 'com.umeng.umsdk:share-wx:7.3.5'

    implementation 'com.tencent.tauth:qqopensdk:3.52.0'
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0'

    // ads
    implementation "com.pangle.cn:mediation-sdk:6.4.1.5"
    implementation "com.pangle.cn:mediation-gdt-adapter:4.591.1461.2"

    //rxJava
    implementation("io.reactivex.rxjava2:rxjava:${rxjava2_version}") {
        exclude group: 'com.android.support', module: 'appcompat-v7'
    }
    implementation("io.reactivex.rxjava2:rxandroid:${rxandroid_version}") {
        exclude group: 'com.android.support', module: 'appcompat-v7'
    }

    implementation 'androidx.documentfile:documentfile:1.0.1'
    implementation 'com.jaeger.statusbarutil:library:1.5.1'

    implementation project(':libSwipeBack')

    implementation "com.github.bumptech.glide:glide:4.10.0"

    implementation "com.squareup.okhttp3:okhttp:3.10.0"
    implementation "com.squareup.retrofit2:retrofit:2.5.0"

    implementation "org.greenrobot:eventbus:3.3.1"
    implementation 'com.jonathanfinerty.once:once:1.3.1'
}

//https://github.com/shwenzhang/AndResGuard/blob/master/README.zh-cn.md
andResGuard {
    mappingFile = null
    use7zip = true
    useSign = true
    keepRoot = false
    mergeDuplicatedRes = true
    fixedResName = "vv"
    // add <yourpackagename>.R.drawable.icon into whitelist.
    // because the launcher will get the icon with his name
    whiteList = ["R.mipmap.ic_launcher",
                 //https://docs.fabric.io/android/crashlytics/build-tools.html
                 "R.string.com.crashlytics.*",
                 "R.string.umeng_key",
                 "R.string.map_key",
                 "R.string.alipay_app_id",
                 "R.anim.umeng*",
                 "R.string.umeng*",
                 "R.string.UM*",
                 "R.string.tb_*",
                 "R.layout.umeng*",
                 "R.layout.socialize_*",
                 "R.layout.*messager*",
                 "R.layout.tb_*",
                 "R.color.umeng*",
                 "R.color.tb_*",
                 "R.style.*UM*",
                 "R.style.umeng*",
                 "R.drawable.umeng*",
                 "R.drawable.tb_*",
                 "R.drawable.sina*",
                 "R.drawable.qq_*",
                 "R.drawable.tb_*",
                 "R.id.umeng*",
                 "R.id.*messager*",
                 "R.id.progress_bar_parent",
                 "R.id.socialize_*",
                 "R.id.webView",
                 //穿山甲广告
                 "R.anim.tt_*",
                 "R.color.appdownloader_*",
                 "R.color.tt_*",
                 "R.dimen.tt_*",
                 "R.drawable.appdownloader_*",
                 "R.drawable.tt_*",
                 "R.id.appdownloader_*",
                 "R.id.tt_*",
                 "R.integer.tt_*",
                 "R.layout.appdownloader_*",
                 "R.layout.tt_*",
                 "R.string.appdownloader_*",
                 "R.string.tt_*",
                 "R.style.tt_*",
                 "R.style.appdownloader_*",
                 "R.style.Theme_Dialog_TTDownload",
                 "R.style.Theme_Dialog_TTDownloadOld"
    ]
    compressFilePattern = ["*.png",
                           "*.jpg",
                           "*.jpeg",
                           "*.gif",
                           "*.webp"]
    sevenzip {
        if (org.gradle.internal.os.OperatingSystem.current().isMacOsX()) {
            try {
                def command = "sysctl machdep.cpu.brand_string"
                def proc = command.execute()
                def out = proc.in.text.toString()
                logger.lifecycle("brand_string: $out")
                if (out.contains("Apple M")) {
                    path = "${project.rootDir}/sevenzip/mac/m1/7zz"
                } else {
                    artifact = "com.tencent.mm:SevenZip:1.2.21"
                }
            } catch (Exception e) {
                logger.lifecycle("Can't brand_string Reason: $e")
                artifact = "com.tencent.mm:SevenZip:1.2.21"
            }
        } else {
            artifact = "com.tencent.mm:SevenZip:1.2.21"
        }
    }

    /**
     * Optional: if finalApkBackupPath is null, AndResGuard will overwrite final apk
     * to the path which assemble[Task] write to*/
//    finalApkBackupPath = "${project.rootDir}/app/release/final.apk"
}
