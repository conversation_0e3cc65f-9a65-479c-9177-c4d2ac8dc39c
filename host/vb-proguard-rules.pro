#-dontshrink
#-keepattributes *Annotation*,InnerClasses
#-keepattributes Signature,EnclosingMethod
-keepclassmembers class * implements java.io.Serializable {*;}

-dontwarn android.**
-dontwarn com.android.**
-dontwarn com.fun.vbox.mirror.**

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.ContentProvider

# Parcelable
-keep class * implements android.os.Parcelable {
  public *;
}

-keepclassmembers class * extends android.os.Binder{
    public <methods>;
}

-keepclasseswithmembernames class * {
    native <methods>;
}
# android
-keep class android.**{
    *;
}

#-keep class com.fun.vbox.mirror.** {*;}
-keepclassmembers class com.fun.vbox.mirror.**{
   public *;
}

-keepclassmembers class com.fun.vbox.reflect.**{
   public *;
}

# 不要移除，但是可以混淆
-keep,allowobfuscation class * extends com.fun.vbox.base.MethodProxy {
   public *;
}

-keep class com.fun.vbox.client.NativeLib{
    public <methods>;
}

-keep class com.fun.vbox.client.core.HostApp {*;}
-keep class com.fun.vbox.client.core.Az {*;}
#-keep class com.fun.vbox.client.component.MyComponentFactory {*;}
-keep class com.fun.vbox.utils.lo.TencentLocationManagerProxy {*;}

#-keep class com.fun.vbox.hook.InstrumentationProxy {
#    public *;
#}

-keep class com.fun.vbox.base.JniHelper {*;}
-keep class com.fun.vbox.base.MethodUtils {*;}


-repackageclasses z1