// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.6.0'
    repositories {
        // 穿山甲广告
        maven { url 'https://artifact.bytedance.com/repository/Volcengine/' }
        maven { url 'https://artifact.bytedance.com/repository/pangle' }
        maven { url "https://artifact.bytedance.com/repository/AwemeOpenSDK" }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://repo1.maven.org/maven2/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.fun.vbox.plugins:asm-plugin"
        classpath ("com.tencent.mm:AndResGuard-gradle-plugin:1.2.21") { changing = true }
        classpath 'com.github.megatronking.stringfog:gradle-plugin:3.0.0'
        classpath 'com.github.megatronking.stringfog:xor:3.0.0'
    }
}

allprojects {
    repositories {
        // 穿山甲广告
        maven { url 'https://artifact.bytedance.com/repository/Volcengine/' }
        maven { url 'https://artifact.bytedance.com/repository/pangle' }
        maven { url "https://artifact.bytedance.com/repository/AwemeOpenSDK" }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://repo1.maven.org/maven2/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
