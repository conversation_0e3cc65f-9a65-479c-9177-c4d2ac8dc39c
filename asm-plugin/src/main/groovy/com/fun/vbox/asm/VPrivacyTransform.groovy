package com.fun.vbox.asm

import com.android.build.api.transform.*
import com.android.build.gradle.internal.pipeline.TransformManager
import javassist.ClassPool
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.logging.Logger

import java.util.jar.JarOutputStream
import java.util.zip.ZipOutputStream

class VPrivacyTransform extends Transform implements Plugin<Project> {
    private Project project
    private static Logger logger
    private static boolean isForceInsert = true
    private InsertCodeStrategy insertCodeStrategy
    protected Set<String> removeClassList = new HashSet<>()

    VPrivacyTransform() {
    }

    protected boolean isRemoveClass(String className) {
        return removeClassList.contains(className)
    }

    @Override
    void apply(Project target) {
        project = target
        logger = project.logger
        if (!isForceInsert) {
            def taskNames = project.gradle.startParameter.taskNames
            def isDebugTask = false
            for (int index = 0; index < taskNames.size(); ++index) {
                def taskName = taskNames[index]
                logger.debug "input start parameter task is ${taskName}"
                //FIXME: assembleRelease下屏蔽Prepare，这里因为还没有执行Task，没法直接通过当前的BuildType来判断，所以直接分析当前的startParameter中的taskname，
                //另外这里有一个小坑task的名字不能是缩写必须是全称 例如assembleDebug不能是任何形式的缩写输入
                if (taskName.endsWith("Debug") && taskName.contains("Debug")) {
                    isDebugTask = true
                    break
                }
            }
            if (!isDebugTask) {
                project.android.registerTransform(this)
                logger.quiet "Register vbox_asm transform successful !!!"
            }
        } else {
            project.android.registerTransform(this)
        }
    }

    @Override
    String getName() {
        return "vbox_asm"
    }

    @Override
    Set<QualifiedContent.ContentType> getInputTypes() {
        return TransformManager.CONTENT_CLASS
    }

    @Override
    Set<QualifiedContent.Scope> getScopes() {
        return TransformManager.SCOPE_FULL_PROJECT
    }

    @Override
    boolean isIncremental() {
        return false
    }

    @Override
    void transform(
            Context context,
            Collection<TransformInput> inputs,
            Collection<TransformInput> referencedInputs,
            TransformOutputProvider outputProvider,
            boolean isIncremental) throws IOException, TransformException, InterruptedException {
        logger.quiet '================vbox_asm start================'

        def startTime = System.currentTimeMillis()
        outputProvider.deleteAll()

        ClassPool classPool = new ClassPool()
        project.android.bootClasspath.each {
            classPool.appendClassPath((String) it.absolutePath)
        }

        def box = ConvertUtils.toCtClasses(inputs, classPool, outputProvider)
        insertCodeStrategy = new AsmInsertImpl()
        box.directoryInputs.forEach {file, ctClass ->
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs()
            }
            FileOutputStream directoryOutStream = new FileOutputStream(file)
            insertCodeStrategy.insertCode(ctClass, directoryOutStream)
        }
        box.jarInputs.forEach { file, ctClasses ->
            ZipOutputStream outStream = new JarOutputStream(new FileOutputStream(file))
            ctClasses.forEach {
                if (!isRemoveClass(it.getName())) {
                    insertCodeStrategy.insertCode(it, outStream)
                } else {
                    logger.quiet "remove ${it.getName()}."
                }
            }
            try {
                outStream.close()
            } catch (Throwable e) {
                project.logger.error("", e)
            }
        }

        def cost = (System.currentTimeMillis() - startTime) / 1000
        logger.quiet "vbox_asm cost $cost second"
        logger.quiet '================vbox_asm  end================'
    }
}