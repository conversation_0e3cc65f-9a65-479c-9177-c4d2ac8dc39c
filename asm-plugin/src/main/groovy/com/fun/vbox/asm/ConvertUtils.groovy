package com.fun.vbox.asm

import com.android.SdkConstants
import com.android.build.api.transform.Format
import com.android.build.api.transform.TransformInput
import com.android.build.api.transform.TransformOutputProvider
import javassist.ClassPool
import javassist.CtClass
import javassist.NotFoundException
import org.apache.commons.io.FileUtils

import java.util.jar.JarEntry
import java.util.jar.JarFile
import java.util.regex.Matcher

class ConvertUtils {
    static VTransformInput toCtClasses(
            Collection<TransformInput> inputs,
            ClassPool classPool,
            TransformOutputProvider outputProvider) {
        VTransformInput yyTransformInput = new VTransformInput()
        def startTime = System.currentTimeMillis()
        inputs.each {
            it.directoryInputs.each {
                def sourceFilePath = it.file.toPath()
                // 获取输出目录
                def destFilePath = outputProvider.getContentLocation(it.name,
                        it.contentTypes, it.scopes, Format.DIRECTORY).toPath()
                classPool.insertClassPath(it.file.absolutePath)
                FileUtils.listFiles(it.file, null, true).each {
                    if (it.absolutePath.endsWith(SdkConstants.DOT_CLASS)) {
                        def relativePath = sourceFilePath.relativize(it.toPath())
                        def className = relativePath.toString().split(SdkConstants.DOT_CLASS)[0].replaceAll(Matcher.quoteReplacement(File.separator), '.')
                        if ("META-INF.versions.9.module-info" != className) {
                            try {
                                yyTransformInput.directoryInputs.put(destFilePath.resolve(relativePath).toFile(), classPool.get(className))
                            } catch (NotFoundException ignored) {
                                println "class not found exception class name:  $it "

                            }
                        }
                    }
                }
            }

            it.jarInputs.each {
                classPool.insertClassPath(it.file.absolutePath)
                def jarFile = new JarFile(it.file)
                def dest = outputProvider.getContentLocation(it.name, it.contentTypes, it.scopes, Format.JAR)
                Enumeration<JarEntry> classes = jarFile.entries()
                List<CtClass> ctClasses = new ArrayList<>();
                while (classes.hasMoreElements()) {
                    JarEntry libClass = classes.nextElement()
                    String className = libClass.getName()
                    if (className.endsWith(SdkConstants.DOT_CLASS)) {
                        className = className.substring(0, className.length() - SdkConstants.DOT_CLASS.length()).replaceAll('/', '.')
                        if ("META-INF.versions.9.module-info" != className) {
                            try {
                                ctClasses.add(classPool.get(className))
                            } catch (NotFoundException ignored) {
                                println "class not found exception class name:  $it "

                            }
                        }
                    }
                }
                if (ctClasses.size() > 0) {
                    yyTransformInput.jarInputs.put(dest, ctClasses)
                }
                try {
                    jarFile.close()
                } catch (Throwable ignored) {
                    //
                }
            }
        }
        def cost = (System.currentTimeMillis() - startTime) / 1000
        println "read all class file cost $cost second"
        return yyTransformInput
    }
}