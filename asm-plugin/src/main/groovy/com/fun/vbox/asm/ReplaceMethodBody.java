package com.fun.vbox.asm;

import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.commons.AdviceAdapter;

import java.util.HashMap;

public class ReplaceMethodBody extends AdviceAdapter {
    private static final HashMap<String, ReplaceInfo> sReplaceInfo = new HashMap<>();
    private final String className;
    private final String methodName;

    static {
        ReplaceInfo info = new ReplaceInfo();
        info.oldOwner = "com/amap/api/col/s/bi";
        info.oldMethodName = "e";
        info.oldMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        info.newOpcode = Opcodes.INVOKESTATIC;
        info.newOwner = "com/xyz/host/utils/AMapUtils";
        info.newMethodName = "getSignAndPkg";
        info.newMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        sReplaceInfo.put(info.oldOwner + "#" + info.oldMethodName + "#" + info.oldMethodDesc, info);

        ReplaceInfo info2 = new ReplaceInfo();
        info2.oldOwner = "com/loc/l";
        info2.oldMethodName = "e";
        info2.oldMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        info2.newOpcode = Opcodes.INVOKESTATIC;
        info2.newOwner = "com/xyz/host/utils/AMapUtils";
        info2.newMethodName = "getSignAndPkg";
        info2.newMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        sReplaceInfo.put(info2.oldOwner + "#" + info2.oldMethodName + "#" + info2.oldMethodDesc, info2);

        ReplaceInfo info3 = new ReplaceInfo();
        info3.oldOwner = "com/amap/api/mapcore2d/cq";
        info3.oldMethodName = "e";
        info3.oldMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        info3.newOpcode = Opcodes.INVOKESTATIC;
        info3.newOwner = "com/xyz/host/utils/AMapUtils";
        info3.newMethodName = "getSignAndPkg";
        info3.newMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        sReplaceInfo.put(info3.oldOwner + "#" + info3.oldMethodName + "#" + info3.oldMethodDesc, info3);

        ReplaceInfo info4 = new ReplaceInfo();
        info4.oldOwner = "android/telephony/TelephonyManager";
        info4.oldMethodName = "getDeviceId";
        info4.oldMethodDesc = "()Ljava/lang/String;";
        info4.newOpcode = Opcodes.INVOKESTATIC;
        info4.newOwner = "com/xyz/host/utils/TelephonyCache";
        info4.newMethodName = "getDeviceId";
        info4.newMethodDesc = "(Landroid/telephony/TelephonyManager;)Ljava/lang/String;";
        sReplaceInfo.put(info4.oldOwner + "#" + info4.oldMethodName + "#" + info4.oldMethodDesc, info4);

        ReplaceInfo info5 = new ReplaceInfo();
        info5.oldOwner = "android/telephony/TelephonyManager";
        info5.oldMethodName = "getSubscriberId";
        info5.oldMethodDesc = "()Ljava/lang/String;";
        info5.newOpcode = Opcodes.INVOKESTATIC;
        info5.newOwner = "com/xyz/host/utils/TelephonyCache";
        info5.newMethodName = "getSubscriberId";
        info5.newMethodDesc = "(Landroid/telephony/TelephonyManager;)Ljava/lang/String;";
        sReplaceInfo.put(info5.oldOwner + "#" + info5.oldMethodName + "#" + info5.oldMethodDesc, info5);

        ReplaceInfo info6 = new ReplaceInfo();
        info6.oldOwner = "android/telephony/TelephonyManager";
        info6.oldMethodName = "getSimSerialNumber";
        info6.oldMethodDesc = "()Ljava/lang/String;";
        info6.newOpcode = Opcodes.INVOKESTATIC;
        info6.newOwner = "com/xyz/host/utils/TelephonyCache";
        info6.newMethodName = "getSimSerialNumber";
        info6.newMethodDesc = "(Landroid/telephony/TelephonyManager;)Ljava/lang/String;";
        sReplaceInfo.put(info6.oldOwner + "#" + info6.oldMethodName + "#" + info6.oldMethodDesc, info6);

        ReplaceInfo info7 = new ReplaceInfo();
        info7.oldOwner = "android/telephony/TelephonyManager";
        info7.oldMethodName = "getImei";
        info7.oldMethodDesc = "()Ljava/lang/String;";
        info7.newOpcode = Opcodes.INVOKESTATIC;
        info7.newOwner = "com/xyz/host/utils/TelephonyCache";
        info7.newMethodName = "getImei";
        info7.newMethodDesc = "(Landroid/telephony/TelephonyManager;)Ljava/lang/String;";
        sReplaceInfo.put(info7.oldOwner + "#" + info7.oldMethodName + "#" + info7.oldMethodDesc, info7);

        ReplaceInfo info8 = new ReplaceInfo();
        info8.oldOwner = "android/provider/Settings$Secure";
        info8.oldMethodName = "getString";
        info8.oldMethodDesc = "(Landroid/content/ContentResolver;Ljava/lang/String;)Ljava/lang/String;";
        info8.newOpcode = Opcodes.INVOKESTATIC;
        info8.newOwner = "com/xyz/host/utils/TelephonyCache";
        info8.newMethodName = "getString";
        info8.newMethodDesc = "(Landroid/content/ContentResolver;Ljava/lang/String;)Ljava/lang/String;";
        sReplaceInfo.put(info8.oldOwner + "#" + info8.oldMethodName + "#" + info8.oldMethodDesc, info8);

        ReplaceInfo info9 = new ReplaceInfo();
        info9.oldOwner = "com/umeng/commonsdk/statistics/common/DeviceConfig";
        info9.oldMethodName = "getImeiNew";
        info9.oldMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        info9.newOpcode = Opcodes.INVOKESTATIC;
        info9.newOwner = "com/xyz/host/utils/TelephonyCache";
        info9.newMethodName = "getImei";
        info9.newMethodDesc = "(Landroid/content/Context;)Ljava/lang/String;";
        sReplaceInfo.put(info9.oldOwner + "#" + info9.oldMethodName + "#" + info9.oldMethodDesc, info9);
    }

    private static class ReplaceInfo {
        String oldOwner;
        String oldMethodName;
        String oldMethodDesc;

        int newOpcode;
        String newOwner;
        String newMethodName;
        String newMethodDesc;
    }

    public ReplaceMethodBody(
            MethodVisitor mv,
            String className,
            String desc,
            String name,
            int access) {
        super(Opcodes.ASM5, mv, access, name, desc);
        this.className = className;
        this.methodName = name;
    }

    @Override
    protected void onMethodEnter() {
        if (className.equals("com/umeng/commonsdk/statistics/common/DeviceConfig")
                && methodName.equals("getImeiNew")) {
            super.visitMethodInsn(INVOKESTATIC, "com/xyz/host/utils/TelephonyCache", "getImei", "()Ljava/lang/String;", false);
            super.visitFieldInsn(PUTSTATIC, "com/umeng/commonsdk/statistics/common/DeviceConfig", "sImei", "Ljava/lang/String;");
        }

        if (className.equals("com/amap/api/col/s/bi")
                && methodName.equals("h")) {
            super.visitMethodInsn(INVOKESTATIC, "com/xyz/host/utils/AMapUtils", "getApiKey", "()Ljava/lang/String;", false);
            super.visitFieldInsn(PUTSTATIC, "com/amap/api/col/s/bi", "f", "Ljava/lang/String;");
        }
        if (className.equals("com/loc/l")
                && methodName.equals("h")) {
            super.visitMethodInsn(INVOKESTATIC, "com/xyz/host/utils/AMapUtils", "getApiKey", "()Ljava/lang/String;", false);
            super.visitFieldInsn(PUTSTATIC, "com/loc/l", "f", "Ljava/lang/String;");
        }
        if (className.equals("com/amap/api/mapcore2d/cq")
                && methodName.equals("h")) {
            super.visitMethodInsn(INVOKESTATIC, "com/xyz/host/utils/AMapUtils", "getApiKey", "()Ljava/lang/String;", false);
            super.visitFieldInsn(PUTSTATIC, "com/amap/api/mapcore2d/cq", "f", "Ljava/lang/String;");
        }
    }

    @Override
    public void visitMethodInsn(
            int opcode, String owner, String name, String desc, boolean itf) {
        ReplaceInfo info = find(owner, name, desc);
        if (info != null) {
            super.visitMethodInsn(info.newOpcode, info.newOwner, info.newMethodName, info.newMethodDesc, false);
        } else {
            super.visitMethodInsn(opcode, owner, name, desc, itf);
        }
    }

    private ReplaceInfo find(String owner, String name, String desc) {
        String key = owner + "#" + name + "#" + desc;
        return sReplaceInfo.get(key);
    }
}
