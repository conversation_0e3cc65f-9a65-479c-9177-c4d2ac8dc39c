package com.fun.vbox.asm;

import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.ClassWriter;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.tree.AbstractInsnNode;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.InsnList;
import org.objectweb.asm.tree.MethodNode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.jar.JarOutputStream;
import java.util.zip.ZipOutputStream;

import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.bytecode.AccessFlag;

public class AsmInsertImpl extends InsertCodeStrategy {
    @Override
    public void insertCode(List<CtClass> box, File jarFile) throws IOException, CannotCompileException {
        ZipOutputStream outStream = new JarOutputStream(new FileOutputStream(jarFile));
        for (CtClass ctClass : box) {
            if ("META-INF.versions.9.module-info".equals(ctClass.getName())) {
                continue;
            }
            String pathClassName =  ctClass.getName().replaceAll("\\.", "/");
            ctClass.setModifiers(AccessFlag.setPublic(ctClass.getModifiers()));
            if (isNeedInsertClass(ctClass.getName())
                    && !(ctClass.isInterface()
                    || ctClass.getDeclaredMethods().length < 1)
            ) {
                byte[] classBytesArray = transformCode(ctClass.toBytecode(), pathClassName);
                zipFile(classBytesArray, outStream,pathClassName + ".class");
            } else {
                zipFile(ctClass.toBytecode(), outStream,pathClassName + ".class");

            }
            ctClass.defrost();
        }
        outStream.close();
    }

    @Override
    protected void insertCode(CtClass ctClass, OutputStream outputStream) throws CannotCompileException, IOException {
        if ("META-INF.versions.9.module-info".equals(ctClass.getName())) {
            return;
        }
        String pathClassName = ctClass.getName().replaceAll("\\.", "/");
        try {
            ctClass.setModifiers(AccessFlag.setPublic(ctClass.getModifiers()));
        } catch (Throwable e) {
            System.out.println("class not found exception class name: " + pathClassName);
        }
        byte[] classBytesArray = ctClass.toBytecode();
        if (isNeedInsertClass(ctClass.getName())
                && !(ctClass.isInterface()
                || ctClass.getDeclaredMethods().length < 1)
        ) {
            classBytesArray = transformCode(ctClass.toBytecode(), pathClassName);
        }

        if (outputStream instanceof ZipOutputStream) {
            zipFile(classBytesArray, (ZipOutputStream) outputStream, pathClassName + ".class");
        } else if (outputStream instanceof  FileOutputStream) {
            try {
                outputStream.write(classBytesArray);
                outputStream.flush();
                outputStream.close();
            } catch (Throwable e) {
                e.printStackTrace();
            }
        } else {
            throw new IllegalArgumentException("not support outputStream type");
        }
        ctClass.defrost();
    }

    private byte[] transformCode(byte[] codeByte, String className) {
        ClassWriter cw = new ClassWriter(ClassWriter.COMPUTE_MAXS);
        ClassReader cr = new ClassReader(codeByte);
        ClassNode classNode = new ClassNode();
        Map<String, Boolean> methodInstructionTypeMap = new HashMap<>();
        cr.accept(classNode, 0);
        final List<MethodNode> methods = classNode.methods;
        for (MethodNode m : methods) {
            InsnList inList = m.instructions;
            boolean isMethodInvoke = false;
            for (int i = 0; i < inList.size(); i++) {
                if (inList.get(i).getType() == AbstractInsnNode.METHOD_INSN) {
                    isMethodInvoke = true;
                }
            }
            methodInstructionTypeMap.put(m.name + m.desc, isMethodInvoke);
        }
        InsertMethodBodyAdapter insertMethodBodyAdapter = new InsertMethodBodyAdapter(cw, className, methodInstructionTypeMap);
        cr.accept(insertMethodBodyAdapter, ClassReader.EXPAND_FRAMES);
        return cw.toByteArray();
    }

    private static class InsertMethodBodyAdapter extends ClassVisitor implements Opcodes {
        ClassWriter classWriter;
        private final String className;
        private final Map<String, Boolean> methodInstructionTypeMap;

        public InsertMethodBodyAdapter(ClassWriter cw, String className, Map<String, Boolean> methodInstructionTypeMap) {
            super(Opcodes.ASM5, cw);
            this.classWriter = cw;
            this.className = className;
            this.methodInstructionTypeMap = methodInstructionTypeMap;
        }

        @Override
        public MethodVisitor visitMethod(int access, String name, String desc, String signature, String[] exceptions) {
            if (isProtect(access)) {
                access = setPublic(access);
            }
            MethodVisitor mv = super.visitMethod(access, name,
                    desc, signature, exceptions);

            if (!isQualifiedMethod(access, name, desc)) {
                return mv;
            }
            return new ReplaceMethodBody(mv, className, desc, name, access);
        }

        private boolean isProtect(int access) {
            return (access & Opcodes.ACC_PROTECTED) != 0;
        }

        private int setPublic(int access) {
            return (access & ~(Opcodes.ACC_PRIVATE | Opcodes.ACC_PROTECTED)) | Opcodes.ACC_PUBLIC;
        }

        private boolean isQualifiedMethod(int access, String name, String desc) {
            if (AsmUtils.CLASS_INITIALIZER.equals(name) || AsmUtils.CONSTRUCTOR.equals(name)) {
                return false;
            }
            if ((access & Opcodes.ACC_ABSTRACT) != 0) {
                return false;
            }
            if ((access & Opcodes.ACC_NATIVE) != 0) {
                return false;
            }
            if ((access & Opcodes.ACC_INTERFACE) != 0) {
                return false;
            }

            if ((access & Opcodes.ACC_DEPRECATED) != 0) {
                return false;
            }

            return methodInstructionTypeMap.getOrDefault(name + desc, false);
        }
    }
}
