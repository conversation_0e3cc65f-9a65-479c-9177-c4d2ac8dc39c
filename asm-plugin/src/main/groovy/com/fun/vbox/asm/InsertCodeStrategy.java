package com.fun.vbox.asm;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.NotFoundException;

public abstract class InsertCodeStrategy {
    protected List<String> exceptPackageList = new ArrayList<>();
    protected List<String> exceptClassList = new ArrayList<>();

    public InsertCodeStrategy() {
        exceptPackageList.add("kotlin.jvm.internal");
        exceptClassList.add("androidx.core.os.TraceCompat");
        exceptClassList.add("com.xyz.host.utils.TelephonyCache");
    }

    protected abstract void insertCode(List<CtClass> box, File jarFile) throws CannotCompileException, IOException, NotFoundException;

    protected abstract void insertCode(CtClass ctClass, OutputStream outputStreams) throws CannotCompileException, IOException;

    protected boolean isNeedInsertClass(String className) {
        for (String exceptName : exceptPackageList) {
            if (className.startsWith(exceptName)) {
                return false;
            }
        }
        for (String exceptClassName : exceptClassList) {
            if (className.startsWith(exceptClassName)) {
                return false;
            }
        }
        return true;
    }

    protected void zipFile(byte[] classBytesArray, ZipOutputStream zos, String entryName) {
        try {
            ZipEntry entry = new ZipEntry(entryName);
            zos.putNextEntry(entry);
            zos.write(classBytesArray, 0, classBytesArray.length);
            zos.closeEntry();
            zos.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
