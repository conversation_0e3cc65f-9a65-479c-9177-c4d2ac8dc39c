apply plugin: "kotlin"
apply plugin: "groovy"
buildscript {
    apply from: './version.gradle'
    addRepos(repositories)
    dependencies {
        classpath deps.kotlin.plugin
    }
}

addRepos(repositories)

group = 'com.fun.vbox.plugins'
archivesBaseName = 'asm-plugin'
version = versions.publishVersion

dependencies {
    implementation gradleApi()
    implementation localGroovy()
    implementation deps.android_gradle_plugin
    implementation deps.kotlin.stdlib7
    implementation fileTree(dir: "./src/main/libs", include: ['*.jar'])
    implementation deps.javassist
    implementation deps.asm
}

sourceSets {
    main {
        groovy {
            srcDirs = ['src/main/groovy']
        }

        java {
            srcDirs = ['src/main/java']
        }
    }
}