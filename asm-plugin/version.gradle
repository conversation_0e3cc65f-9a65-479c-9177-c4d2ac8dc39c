def versions = [:]
versions.publishVersion = "1.0.1"

versions.asm = '5.0.1'
versions.appcompat = "1.3.1"
versions.android_gradle_plugin = '3.4.2'
versions.javassist = '3.22.0-GA'
versions.kotlin = "1.3.61"
versions.okhttp3 = "3.12.10"
versions.material = "1.4.0"
versions.gson = "2.8.5"
ext.versions = versions

def deps = [:]
def build_versions = [:]
build_versions.min_sdk = 14
build_versions.target_sdk = 29
build_versions.compile_sdk = 29
build_versions.build_tools = "29.0.3"
build_versions.java_source = JavaVersion.VERSION_1_8
build_versions.java_target = JavaVersion.VERSION_1_8
ext.build_versions = build_versions

def kotlin = [:]
kotlin.stdlib7 = "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$versions.kotlin"
kotlin.stdlib = "org.jetbrains.kotlin:kotlin-stdlib:$versions.kotlin"
kotlin.stdlibcommon = "org.jetbrains.kotlin:kotlin-stdlib-common:$versions.kotlin"
kotlin.test = "org.jetbrains.kotlin:kotlin-test-junit:$versions.kotlin"
kotlin.plugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:$versions.kotlin"
kotlin.allopen = "org.jetbrains.kotlin:kotlin-allopen:$versions.kotlin"
deps.kotlin = kotlin

def okhttp3 = [:]
okhttp3.okhttp = "com.squareup.okhttp3:okhttp:$versions.okhttp3"
okhttp3.loginterceptor = "com.squareup.okhttp3:logging-interceptor:$versions.okhttp3"
deps.okhttp3 = okhttp3

deps.app_compat = "androidx.appcompat:appcompat:$versions.appcompat"
deps.material = "com.google.android.material:material:$versions.material"

deps.asm = "org.ow2.asm:asm:$versions.asm"
deps.javassist = "org.javassist:javassist:$versions.javassist"
deps.android_gradle_plugin = "com.android.tools.build:gradle:$versions.android_gradle_plugin"
deps.gson = "com.google.code.gson:gson:$versions.gson"

ext.deps = deps

static def addRepos(RepositoryHandler handler) {
    handler.maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
    handler.maven { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter' }
    handler.maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
    handler.maven { url 'http://maven.aliyun.com/nexus/content/repositories/gradle-plugin' }

    handler.google()
    handler.maven { url 'https://jitpack.io' }
    handler.mavenCentral()
}
ext.addRepos = this.&addRepos

def forceDeps(ConfigurationContainer configurations) {
    configurations.all {
        resolutionStrategy {
            force deps.kotlin.stdlib7
            force deps.kotlin.stdlib
            force deps.kotlin.stdlibcommon
        }
    }
}
ext.forceDeps = this.&forceDeps