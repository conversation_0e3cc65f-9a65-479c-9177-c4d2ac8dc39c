# 多开应用界面演示

## 功能描述

完全新建实现了一个多开应用界面，展示应用的多个分身实例，与效果图保持一致。

## 新增文件列表

### 数据模型
- `host/src/main/java/com/yingyu/zh/multiapp/model/AppItem.java` - 应用项数据模型
- `host/src/main/java/com/yingyu/zh/multiapp/model/BottomTab.java` - 底部Tab数据模型

### 适配器
- `host/src/main/java/com/yingyu/zh/multiapp/adapter/AppGridAdapter.java` - 应用网格适配器

### 界面
- `host/src/main/java/com/yingyu/zh/multiapp/MultiAppActivity.java` - 主Activity
- `host/src/main/res/layout/activity_multiapp.xml` - 主界面布局
- `host/src/main/res/layout/item_app_grid.xml` - 应用项布局
- `host/src/main/res/layout/item_bottom_tab.xml` - 底部Tab项布局

### 资源文件
- `host/src/main/res/drawable/bg_blue_gradient.xml` - 蓝紫色渐变背景
- `host/src/main/res/drawable/shape_green_dot.xml` - 绿色在线状态点
- `host/src/main/res/drawable/shape_red_dot.xml` - 红色提示点
- `host/src/main/res/drawable/ic_orange_avatar.xml` - 橙色用户头像
- `host/src/main/res/drawable/bg_blue_add_button.xml` - 蓝色添加按钮背景

## 如何查看演示

1. 运行应用
2. 在主界面点击右上角菜单（三个点）
3. 选择"多开界面"
4. 即可看到新的多开应用界面

## 界面特性

### 顶部区域
- 半透明搜索框，显示"搜索分身"
- 橙色用户头像显示在右侧

### 应用网格
- 4列网格布局展示应用分身
- 每个应用显示图标、名称和在线状态
- 使用ic_launcher.webp作为应用图标
- 绿色小点表示在线状态
- 支持点击事件

### 底部Tab导航
- 5个Tab项：影评解码、联系发现、新作分身、分身管理、用户中心
- 中间的"新作分身"为蓝色添加按钮
- "用户中心"显示红点提示
- 底部Tab图标使用ic_add.png
- 支持点击事件

### 静态数据
包含以下应用的分身示例：
- 小红书1、小红书千帆、小红书1、小红书千...
- 抖音1、抖音2
- QQ（两个实例）
- 微信（两个实例）

## 技术实现

- 使用RecyclerView + GridLayoutManager实现应用网格
- 使用LinearLayout动态添加实现底部Tab
- 蓝紫色渐变背景使用Shape drawable
- 圆形状态点使用Shape drawable
- 支持点击事件回调
- 完全独立的新代码，不影响现有功能

## 特点

1. **完全新建** - 所有文件都是新创建的，不影响现有类和布局
2. **包名隔离** - 使用独立的包名`com.yingyu.zh.multiapp`
3. **静态数据** - 使用写死的静态数据，方便演示
4. **图标统一** - 应用图标使用`ic_launcher.webp`
5. **Tab图标** - 底部Tab使用`ic_add.png`

## 代码结构

```
com.yingyu.zh.multiapp/
├── model/
│   ├── AppItem.java           # 应用项数据模型
│   └── BottomTab.java         # 底部Tab数据模型
├── adapter/
│   └── AppGridAdapter.java    # 应用网格适配器
└── MultiAppActivity.java     # 主Activity
```

## 可扩展性

代码结构清晰，易于扩展：
- 可轻松修改静态数据
- 可自定义应用图标
- 可修改Tab项数量和样式
- 可添加更多交互功能
- 可接入真实数据源
