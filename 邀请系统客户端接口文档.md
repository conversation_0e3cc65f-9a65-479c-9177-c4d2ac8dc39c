# 无界多开邀请系统客户端接口文档

> **📋 更新说明 (2025-07-21)**
> 本文档已更新以反映新的体验券系统变更：
> - 体验券从汇总模式改为单券记录模式
> - 每张券有独立的ID、UUID和过期时间
> - 使用体验券接口参数从 `ticket_type` 改为 `ticket_id`
> - 新增体验券来源追踪功能

## 📋 项目概述

无界多开邀请系统为Android客户端提供完整的用户邀请功能，包括邀请码生成、使用邀请码、体验券管理等核心功能。通过邀请好友，用户可以获得体验券奖励，延长VIP使用时间。

## 🔗 基础信息

- **基础URL**: `http://your-domain.com/api/`
- **数据格式**: JSON
- **请求方法**: POST
- **签名验证**: 所有接口都需要签名验证
- **加密方式**: AES-128-ECB（敏感数据）

## 🔐 签名验证机制

### 签名计算方法
```javascript
// 1. 构造签名字符串
const signString = `apk_sign${APK_SIGN}app_ver_code${APP_VER_CODE}nonce${NONCE}auth_appkey${APK_KEY}`;

// 2. 计算SHA1签名
const signature = SHA1(signString);

// 3. 在请求头中添加签名
headers['x-auth-token'] = signature;
```

### 通用请求参数
所有接口都需要包含以下参数：
```json
{
    "mobile": "13800138000",        // 必填：用户手机号
    "aid": "android_device_id_001", // 必填：Android设备唯一标识
    "app_ver_code": "1",            // 必填：应用版本号
    "nonce": 1625054400             // 必填：时间戳
}
```

### 通用请求头
```
Content-Type: application/json
x-auth-token: 签名值（SHA1计算结果）
Accept: application/json
```

## 📚 API 接口列表

### 1. 获取/生成邀请码接口

**接口地址**: `POST /api/yingyu_invite_code`

**功能描述**: 
- 获取用户的邀请码，如果没有则自动生成
- 返回邀请码、已邀请数量、最大邀请限制
- 用于"我的邀请码"或"邀请好友"页面

**请求参数**:
```json
{
    "mobile": "13800138001",
    "aid": "test_android_device_001",
    "app_ver_code": "1",
    "nonce": 1625054400
}
```

**响应格式**:
```json
{
    "invite_code": "ABC123",        // 6位邀请码
    "invited_count": 5,             // 已邀请成功的人数
    "max_invite_limit": 20          // 最大邀请限制
}
```

**错误响应**:
```json
{
    "message": "User not found"     // 用户不存在
}
```

**使用场景**:
- 用户点击"我的邀请码"按钮
- 用户进入"邀请好友"页面
- 分享邀请码给好友

---

### 2. 使用邀请码接口

**接口地址**: `POST /api/yingyu_use_invite`

**功能描述**: 
- 新用户或现有用户使用邀请码
- 验证邀请码有效性和使用条件
- 发放体验券奖励给邀请人和被邀请人

**请求参数**:
```json
{
    "mobile": "13800138002",
    "aid": "test_android_device_002",
    "invite_code": "ABC123",        // 必填：6位邀请码
    "app_ver_code": "1",
    "nonce": 1625054400
}
```

**响应格式**:
```json
{
    "success": true,
    "message": "邀请码使用成功",
    "reward_tickets": 1,            // 被邀请人获得的体验券数量
    "inviter_mobile": "138****8001" // 邀请人手机号（脱敏）
}
```

**错误响应**:
```json
{
    "message": "Invalid invite code"        // 邀请码无效
}
{
    "message": "User has already been invited"  // 用户已被邀请过
}
{
    "message": "Cannot invite yourself"     // 不能邀请自己
}
{
    "message": "Invite limit exceeded"      // 邀请人已达上限
}
```

**使用场景**:
- 新用户注册时填写邀请码
- 现有用户首次填写邀请码
- 扫描邀请二维码

---

### 3. 获取体验券列表接口

**接口地址**: `POST /api/yingyu_tickets`

**功能描述**:
- 获取用户所有有效的体验券（每张券独立记录）
- 自动清理过期的体验券
- 显示每张券的详细信息，包括来源和过期时间

**请求参数**:
```json
{
    "mobile": "13800138002",
    "aid": "test_android_device_002",
    "app_ver_code": "1",
    "nonce": 1625054400
}
```

**响应格式**:
```json
{
    "tickets": [
        {
            "ticket_id": 123,                   // 体验券唯一ID
            "ticket_uuid": "abc123-def456-...", // 体验券唯一标识
            "type_id": "day",                   // 体验券类型代码
            "type_name": "1天体验券",            // 体验券类型名称
            "duration_hours": 24,               // 时长（小时）
            "source": "invite_reward",          // 来源代码
            "source_display": "被邀请奖励",      // 来源显示名称
            "expire_time": "2025-08-15 10:30:00", // 过期时间
            "is_expired": false                 // 是否过期
        },
        {
            "ticket_id": 124,
            "ticket_uuid": "def789-ghi012-...",
            "type_id": "day",
            "type_name": "1天体验券",
            "duration_hours": 24,
            "source": "inviter_reward",
            "source_display": "邀请他人奖励",
            "expire_time": "2025-08-20 15:45:00",
            "is_expired": false
        },
        {
            "ticket_id": 125,
            "ticket_uuid": "ghi345-jkl678-...",
            "type_id": "hour",
            "type_name": "1小时体验券",
            "duration_hours": 1,
            "source": "admin",
            "source_display": "管理员发放",
            "expire_time": "2025-08-25 09:15:00",
            "is_expired": false
        }
    ],
    "total_count": 3,                           // 总数量
    "expired_cleaned": 1                        // 本次清理的过期券数量
}
```

**响应字段说明**:
- `ticket_id`: 体验券唯一ID，用于使用体验券时指定
- `ticket_uuid`: 体验券唯一标识符，用于追踪和防重复
- `type_id`: 体验券类型代码（day/hour等）
- `source`: 来源代码（invite_reward/inviter_reward/admin/system）
- `source_display`: 来源的友好显示名称

**使用场景**:
- 用户查看"我的体验券"页面
- 检查可用体验券数量和来源
- 选择要使用的特定体验券
- 查看每张券的详细信息（来源、过期时间等）

---

### 4. 使用体验券接口

**接口地址**: `POST /api/yingyu_use_ticket`

**功能描述**:
- 使用指定ID的体验券
- 延长用户VIP到期时间
- 标记体验券为已使用状态并记录使用日志

**请求参数**:
```json
{
    "mobile": "13800138002",
    "aid": "test_android_device_002",
    "ticket_id": 123,               // 必填：要使用的体验券ID
    "app_ver_code": "1",
    "nonce": 1625054400
}
```

**参数说明**:
- `ticket_id`: 要使用的体验券ID，从获取体验券列表接口中获取

**响应格式**:
```json
{
    "success": true,
    "message": "体验券使用成功",
    "ticket_id": 123,                           // 使用的体验券ID
    "ticket_uuid": "abc123-def456-...",         // 使用的体验券UUID
    "vip_expire_time": "2025-07-16 15:30:00",  // 新的VIP到期时间
    "extended_hours": 24,                       // 延长的小时数
    "remaining_tickets": 2                      // 该类型剩余体验券数量
}
```

**响应字段说明**:
- `ticket_id`: 使用的体验券ID
- `ticket_uuid`: 使用的体验券唯一标识
- `remaining_tickets`: 该类型剩余的有效体验券数量

**错误响应**:
```json
{
    "message": "Ticket not found or not belongs to user"  // 体验券不存在或不属于该用户
}
{
    "message": "Ticket is not active"                      // 体验券状态无效（已使用或已过期）
}
{
    "message": "Ticket has expired"                        // 体验券已过期
}
{
    "message": "Invalid ticket type"                       // 无效的体验券类型
}
```

**使用场景**:
- 用户点击特定体验券的"使用"按钮
- VIP即将到期时选择体验券续期
- 用户主动选择要使用的体验券

## 🛡️ 安全机制

### 防薅羊毛策略

1. **设备级别防护**
   - 每个Android设备ID（aid）只能被邀请一次
   - 验证aid格式和长度，拒绝伪造设备ID
   - 用户账号与设备强绑定

2. **用户级别防护**
   - 每个手机号只能被邀请一次
   - 每个用户最多邀请20人
   - 不能使用自己的邀请码

3. **邀请码防护**
   - 6位数字+字母组合，全局唯一
   - 使用前验证邀请码有效性
   - 每个邀请码最多使用20次

4. **体验券防护**
   - 每张体验券独立记录，具有唯一ID和UUID
   - 严格按邀请成功发放，不可重复领取
   - 每张券独立的过期时间，精确到秒
   - 详细的使用日志和来源追踪
   - 防止使用他人的体验券或已使用的券

5. **系统级别防护**
   - 所有接口必须通过签名验证
   - API调用频率限制
   - 敏感数据AES加密传输

## 🔧 集成指南

### Android客户端集成步骤

1. **配置签名参数**
```java
public class ApiConfig {
    public static final String APK_SIGN = "VxovfdgZcVtrnZA04dzmDKEx/r0=";
    public static final String APK_KEY = "MuYmxvY2twcm94eXYZbnByb29ubGluZY";
    public static final String BASE_URL = "http://your-domain.com/api/";
}
```

2. **实现签名计算**
```java
public class SignatureUtils {
    public static String calculateSignature(String appVerCode, long nonce) {
        String signString = "apk_sign" + ApiConfig.APK_SIGN + 
                           "app_ver_code" + appVerCode + 
                           "nonce" + nonce + 
                           "auth_appkey" + ApiConfig.APK_KEY;
        return sha1(signString);
    }
}
```

3. **发送API请求**
```java
public class InviteApiClient {
    public void getInviteCode(String mobile, String aid, ApiCallback callback) {
        JSONObject params = new JSONObject();
        params.put("mobile", mobile);
        params.put("aid", aid);
        params.put("app_ver_code", "1");
        params.put("nonce", System.currentTimeMillis() / 1000);
        
        String signature = SignatureUtils.calculateSignature("1", 
            System.currentTimeMillis() / 1000);
        
        // 发送POST请求到 /api/yingyu_invite_code
        // 添加请求头 x-auth-token: signature
    }
}
```

## 📱 UI集成建议

### 邀请码页面
- 显示用户的邀请码（大字体，易复制）
- 显示已邀请人数和剩余可邀请人数
- 提供分享功能（微信、QQ、复制链接）
- 显示邀请奖励说明

### 体验券页面
- 列表显示每张体验券的详细信息
- 显示券的来源（被邀请奖励、邀请他人奖励等）
- 显示每张券的过期时间
- 为每张券提供独立的"立即使用"按钮
- 显示使用后的VIP到期时间
- 支持按类型或来源筛选体验券

### 邀请码输入页面
- 提供邀请码输入框（6位字符）
- 支持扫码输入邀请码
- 显示使用邀请码的奖励说明
- 处理各种错误情况并给出友好提示

## 🚨 错误处理指南

### 通用错误处理
```java
public void handleApiError(int statusCode, String message) {
    switch (statusCode) {
        case 400:
            showToast("请求参数错误: " + message);
            break;
        case 401:
            showToast("签名验证失败，请检查网络设置");
            break;
        case 403:
            showToast("设备验证失败，请重新登录");
            break;
        case 404:
            showToast("用户不存在，请先注册");
            break;
        case 500:
            showToast("服务器繁忙，请稍后重试");
            break;
        default:
            showToast("网络错误: " + message);
    }
}
```

### 邀请码相关错误
```java
public void handleInviteError(String message) {
    if (message.contains("Invalid invite code")) {
        showDialog("邀请码无效", "请检查邀请码是否正确，或联系邀请人确认");
    } else if (message.contains("already been invited")) {
        showDialog("已被邀请过", "每个用户只能被邀请一次，您已经享受过邀请奖励");
    } else if (message.contains("Cannot invite yourself")) {
        showDialog("不能邀请自己", "请使用其他人的邀请码");
    } else if (message.contains("limit exceeded")) {
        showDialog("邀请已满", "该邀请码的使用次数已达上限");
    }
}
```

### 体验券相关错误
```java
public void handleTicketError(String message) {
    if (message.contains("not found or not belongs to user")) {
        showDialog("体验券无效", "该体验券不存在或不属于您，请刷新页面后重试");
    } else if (message.contains("not active")) {
        showDialog("体验券已使用", "该体验券已被使用或已过期，请选择其他体验券");
    } else if (message.contains("expired")) {
        showDialog("体验券已过期", "该体验券已过期，请使用其他有效体验券");
    } else if (message.contains("Invalid ticket type")) {
        showDialog("体验券类型错误", "请刷新页面后重试");
    }
}
```

## ❗ 常见问题

### Q: 为什么使用邀请码时返回"用户已被邀请过"？
A: 每个用户只能被邀请一次，基于user_id、mobile、aid三重验证。

### Q: 邀请码有使用期限吗？
A: 邀请码永久有效，但每个用户最多只能邀请20人。

### Q: 体验券会过期吗？
A: 是的，每张体验券都有独立的过期时间，通常为30天，过期后会自动清理。

### Q: 可以邀请自己吗？
A: 不可以，系统会检测并拒绝自己邀请自己的行为。

### Q: 签名验证失败怎么办？
A: 检查APK_SIGN、APK_KEY、时间戳等参数是否正确，确保签名字符串格式正确。

### Q: 如何处理网络超时？
A: 建议设置30秒超时时间，超时后提示用户重试，避免重复提交。

### Q: 体验券使用后多久生效？
A: 体验券使用后立即生效，VIP到期时间会立即更新。

### Q: 为什么每张体验券都有不同的过期时间？
A: 新系统中每张体验券都是独立记录，根据获得时间计算各自的过期时间，更加精确。

### Q: 如何区分体验券的来源？
A: 每张体验券都会显示来源信息，如"被邀请奖励"、"邀请他人奖励"、"管理员发放"等。

### Q: 可以使用别人的体验券ID吗？
A: 不可以，系统会验证体验券是否属于当前用户，防止盗用他人体验券。

## 📊 状态码说明

| HTTP状态码 | 说明 | 处理建议 |
|-----------|------|----------|
| 200 | 请求成功 | 正常处理响应数据 |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 签名验证失败 | 检查签名计算过程 |
| 403 | 设备验证失败 | 检查设备ID是否匹配 |
| 404 | 用户不存在 | 提示用户先注册 |
| 500 | 服务器内部错误 | 稍后重试或联系技术支持 |

## 🧪 测试用例

### 完整的邀请流程测试
```javascript
// 1. 邀请人获取邀请码
const inviterResponse = await getInviteCode("13800138001", "device_001");
const inviteCode = inviterResponse.invite_code;

// 2. 被邀请人使用邀请码
const inviteeResponse = await useInviteCode("13800138002", "device_002", inviteCode);
console.log("邀请成功:", inviteeResponse.success);

// 3. 被邀请人查看体验券
const ticketsResponse = await getTickets("13800138002", "device_002");
console.log("获得体验券:", ticketsResponse.total_count);

// 4. 被邀请人使用体验券
const useResponse = await useTicket("13800138002", "device_002", "1");
console.log("VIP延长至:", useResponse.vip_expire_time);
```

## 📋 更新日志

### v1.0.0 (2025-07-16)
- ✅ 实现邀请码生成和使用功能
- ✅ 实现体验券管理功能
- ✅ 添加完整的安全防护机制
- ✅ 支持设备级别的防薅羊毛策略

## 📞 技术支持

如有技术问题，请联系开发团队：
- 📧 邮箱: <EMAIL>
- 📱 微信群: 扫描二维码加入技术交流群
- 🐛 Bug反馈: 通过GitHub Issues提交

---

**文档版本**: v1.0.0
**最后更新**: 2025-07-16
**维护团队**: 无界多开开发团队
