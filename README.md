
![应用图标](host/src/main/res/drawable-xxxhdpi/ic_launcher.webp)

App名字: 无界多开

包名: com.yingyu.zh

release签名路径: host/keystore/sign_release.jks

signingConfigs {

      create("release") {
      
      storeFile = file("keystore/sign_release.jks")
      
      storePassword = "vDLQoHv4"
       
       keyAlias = "aoq"
       
       keyPassword = "6dR6ZZKA"
      }
}

```text
MD5 签名: D7207E6C5557549E4FD620ACA5F964E9

SHA-1 签名: 571A2F7DD819715B6B9D9034E1DCE60CA131FEBD

SHA-256 签名: 1ED0BE0AB3A8054754128CD94F2A1E74767C9ACEC2142A00E6AC70C0C9384009

公钥: 29265200428427277032407259655489582366736140689415598467561299037744405895730466781662501035078329159691938354827459980739846806880954601106720608288012383450141331627229570828469166039288528511220560618866712898875418249018256825862276086232388490174828895562721601713533858408073146897472959103225379865103420341036938132486582683844492379552217456777702216924269591791198601892001391074182986046750598867741659154926392539281502186275653442527292510107710122384106256442696714441019883806144205652492800630298939382413201840092125428567445069551270938768061668393702178569255827994486205081596403536968192422632883
```

# 应用市场上架简介

```text
todolist：
1, 域名模版需要实名认证
2，需要创建官网
3，微信开发者账号创建移动应用app，然后申请appkey
4，备案需要法人人脸识别
5，qq，高德地图申请appkey
6，应用市场账号注册
小米开发者：https://dev.mi.com/
华为开发者：https://developer.huawei.com/consumer/cn/
荣耀开发者：https://developer.honor.com/cn/
应用宝：https://app.open.qq.com/
```