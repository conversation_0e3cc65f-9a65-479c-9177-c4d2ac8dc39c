package com.meta.entity;

import android.os.Parcel;
import android.os.Parcelable;

import com.meta.annotations.Keep;

@Keep
public final class VAppInfo implements Parcelable {
    public static final int MODE_APP_FROM_SYSTEM = 0;
    public static final int MODE_APP_FROM_FILE = 1;

    public String packageName;
    public String path;
    public int userId;
    public int appMode = MODE_APP_FROM_SYSTEM;
    public boolean is64bit;

    public long versionCode;

    public static VAppInfo makeFromSys(String packageName, int userId) {
        VAppInfo appInfo = new VAppInfo();
        appInfo.packageName = packageName;
        appInfo.path = "";
        appInfo.userId= userId;
        return appInfo;
    }

    private VAppInfo() {

    }

    public VAppInfo(VAppInfo appInfo) {
        this.packageName = appInfo.packageName;
        this.path = appInfo.path;
        this.userId= appInfo.userId;
        this.appMode = appInfo.appMode;
        this.is64bit = appInfo.is64bit;
        this.versionCode = appInfo.versionCode;
    }

    public VAppInfo(String packageName, int userId, int appMode, boolean is64bit) {
        this.packageName = packageName;
        this.path = "";
        this.userId= userId;
        this.appMode = appMode;
        this.is64bit = is64bit;
    }

    public VAppInfo(String packageName, String path, int userId, int appMode, boolean is64bit) {
        this.packageName = packageName;
        this.path = path;
        this.userId= userId;
        this.appMode = appMode;
        this.is64bit = is64bit;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.packageName);
        dest.writeString(this.path);
        dest.writeInt(this.userId);
        dest.writeInt(this.appMode);
        dest.writeByte((byte) (this.is64bit ? 1 : 0));
        dest.writeLong(this.versionCode);
    }

    private VAppInfo(Parcel in) {
        this.packageName = in.readString();
        this.path = in.readString();
        this.userId = in.readInt();
        this.appMode = in.readInt();
        this.is64bit = in.readByte() != 0;
        this.versionCode = in.readLong();
    }

    public static final Creator<VAppInfo> CREATOR = new Creator<VAppInfo>() {
        @Override
        public VAppInfo createFromParcel(Parcel source) {
            return new VAppInfo(source);
        }

        @Override
        public VAppInfo[] newArray(int size) {
            return new VAppInfo[size];
        }
    };
}
