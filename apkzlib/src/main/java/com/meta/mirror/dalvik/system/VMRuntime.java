package com.meta.mirror.dalvik.system;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefInt;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefStaticMethod;

public class VMRuntime {
    public static Class<?> TYPE = RefClass.load(VMRuntime.class, "dalvik.system.VMRuntime");
    public static RefStaticMethod<Object> getRuntime;
    @MethodParams({int.class})
    public static RefMethod<Void> setTargetSdkVersion;
    public static RefMethod<Boolean> is64Bit;
    @MethodParams({String.class})
    public static RefStaticMethod<Boolean> is64BitAbi;
    public static RefStaticMethod<String> getCurrentInstructionSet;

    public static RefInt targetSdkVersion;
}
