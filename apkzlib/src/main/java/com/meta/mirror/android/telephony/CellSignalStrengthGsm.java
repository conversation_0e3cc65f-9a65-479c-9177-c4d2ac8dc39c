package com.meta.mirror.android.telephony;

import android.annotation.TargetApi;
import android.os.Build;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefInt;

@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
public class CellSignalStrengthGsm {
    public static Class<?> TYPE = RefClass.load(CellSignalStrengthGsm.class, android.telephony.CellSignalStrengthGsm.class);
    public static RefConstructor<android.telephony.CellSignalStrengthGsm> ctor;
    public static RefInt mSignalStrength;
    public static RefInt mBitErrorRate;
}
