package com.meta.mirror.android.app;

import android.os.IInterface;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;
import com.meta.reflect.RefStaticObject;


public class ActivityManagerOreo {
    public static Class<?> TYPE = RefClass.load(ActivityManagerOreo.class, "android.app.ActivityManager");

    public static RefStaticMethod<IInterface> getService;
    public static RefStaticObject<Object> IActivityManagerSingleton;

}
