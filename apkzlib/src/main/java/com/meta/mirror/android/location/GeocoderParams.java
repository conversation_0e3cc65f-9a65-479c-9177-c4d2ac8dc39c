package com.meta.mirror.android.location;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefObject;

public class GeocoderParams {
    public static Class<?> TYPE = RefClass.load(GeocoderParams.class, "android.location.GeocoderParams");
    public static RefObject<String> mPackageName;
    public static RefObject<Integer> mUid;

    public static void mPackageName(Object obj, String str) {
        RefObject<String> field = mPackageName;
        if (field != null) {
            field.set(obj, str);
        }
    }

    public static void mUid(Object obj, int i2) {
        RefObject<Integer> field = mUid;
        if (field != null) {
            field.set(obj, Integer.valueOf(i2));
        }
    }

    public static String mPackageName(Object obj) {
        RefObject<String> field = mPackageName;
        if (field != null) {
            return field.get(obj);
        }
        return null;
    }

    public static int mUid(Object obj) {
        RefObject<Integer> field = mUid;
        if (field != null) {
            return field.get(obj).intValue();
        }
        return 0;
    }

}
