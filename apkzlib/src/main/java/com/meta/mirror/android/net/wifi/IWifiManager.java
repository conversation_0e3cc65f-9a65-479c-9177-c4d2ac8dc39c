package com.meta.mirror.android.net.wifi;

import android.os.IBinder;
import android.os.IInterface;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;

public class IWifiManager {
    public static Class<?> TYPE = RefClass.load(IWifiManager.class, "android.net.wifi.IWifiManager");

    public static class Stub {
        public static Class<?> TYPE = RefClass.load(Stub.class, "android.net.wifi.IWifiManager$Stub");
        @MethodParams({IBinder.class})
        public static RefStaticMethod<IInterface> asInterface;
    }
}
