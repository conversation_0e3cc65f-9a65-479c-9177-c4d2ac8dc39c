package com.meta.mirror.android.app;

import android.os.IInterface;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;
import com.meta.reflect.RefStaticObject;

public class ActivityManagerNative {
    public static Class<?> TYPE = RefClass.load(ActivityManagerNative.class, "android.app.ActivityManagerNative");
    public static RefStaticObject<Object> gDefault;
    public static RefStaticMethod<IInterface> getDefault;
}
