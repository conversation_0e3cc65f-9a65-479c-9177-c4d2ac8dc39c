package com.meta.mirror.android.os;

import android.os.IBinder;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefMethod;

public class Bundle {
    public static Class<?> TYPE = RefClass.load(Bundle.class, android.os.Bundle.class);

    @MethodParams({String.class, IBinder.class})
    public static RefMethod<Void> putIBinder;

    @MethodParams({String.class})
    public static RefMethod<IBinder> getIBinder;
}
