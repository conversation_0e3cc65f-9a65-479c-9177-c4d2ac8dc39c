package com.meta.mirror.android.location;

import com.meta.reflect.RefBoolean;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefLong;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefObject;

public class LocationRequestL {
    public static Class<?> TYPE = RefClass.load(LocationRequestL.class, "android.location.LocationRequest");
    public static RefBoolean mHideFromAppOps;
    public static RefObject<Object> mWorkSource;
    public static RefObject<String> mProvider;
    public static RefMethod<String> getProvider;
    public static RefLong mInterval;
}
