package com.meta.mirror.android.app;

import android.app.Application;
import android.app.Instrumentation;
import android.os.IInterface;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefObject;
import com.meta.reflect.RefStaticMethod;
import com.meta.reflect.RefStaticObject;

public class ActivityThread {
    public static Class<?> TYPE = RefClass.load(ActivityThread.class, "android.app.ActivityThread");
    public static RefStaticObject<IInterface> sPackageManager;
    public static RefStaticMethod<Object> currentActivityThread;
    public static RefMethod<Object> getSystemContext;
    public static RefObject<Instrumentation> mInstrumentation;
    public static RefObject<Application> mInitialApplication;
}
