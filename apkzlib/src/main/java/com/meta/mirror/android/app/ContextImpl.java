package com.meta.mirror.android.app;

import android.content.Context;
import android.content.pm.PackageManager;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefObject;

public class ContextImpl {
    public static Class<?> TYPE = RefClass.load(ContextImpl.class, "android.app.ContextImpl");
    @MethodParams({Context.class})
    public static RefObject<String> mBasePackageName;
    public static RefObject<Object> mPackageInfo;
    public static RefObject<PackageManager> mPackageManager;

    public static RefMethod<Context> getReceiverRestrictedContext;

    @MethodParams({Context.class})
    public static RefMethod<Void> setOuterContext;
}
