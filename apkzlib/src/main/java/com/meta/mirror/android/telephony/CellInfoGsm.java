package com.meta.mirror.android.telephony;

import android.annotation.TargetApi;
import android.os.Build;
import android.telephony.CellIdentityGsm;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefObject;

@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
public class CellInfoGsm {
    public static Class<?> TYPE = RefClass.load(CellInfoGsm.class, android.telephony.CellInfoGsm.class);
    public static RefConstructor<android.telephony.CellInfoGsm> ctor;
    public static RefObject<CellIdentityGsm> mCellIdentityGsm;
    public static RefObject<android.telephony.CellSignalStrengthGsm> mCellSignalStrengthGsm;

}
