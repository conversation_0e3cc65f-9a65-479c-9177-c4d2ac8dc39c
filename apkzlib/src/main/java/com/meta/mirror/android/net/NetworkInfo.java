package com.meta.mirror.android.net;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefBoolean;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefInt;
import com.meta.reflect.RefObject;

public class NetworkInfo {
    public static Class<?> TYPE = RefClass.load(NetworkInfo.class, android.net.NetworkInfo.class);
    @MethodParams({int.class, int.class, String.class, String.class})
    public static RefConstructor<android.net.NetworkInfo> ctor;
    @MethodParams({int.class})
    public static RefConstructor<android.net.NetworkInfo> ctorOld;
    public static RefInt mNetworkType;
    public static RefObject<String> mTypeName;
    public static RefObject<android.net.NetworkInfo.State> mState;
    public static RefObject<android.net.NetworkInfo.DetailedState> mDetailedState;
    public static RefBoolean mIsAvailable;
}
