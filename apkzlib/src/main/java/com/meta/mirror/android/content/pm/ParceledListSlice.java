package com.meta.mirror.android.content.pm;

import android.os.Parcelable;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefStaticObject;

import java.util.List;

public class ParceledListSlice {
    public static RefStaticObject<Parcelable.Creator> CREATOR;
    public static Class<?> TYPE = RefClass.load(ParceledListSlice.class, "android.content.pm.ParceledListSlice");
    public static RefMethod<Boolean> append;
    public static RefConstructor<Parcelable> ctor;
    @MethodParams({List.class})
    public static RefConstructor<Parcelable> ctorQ;
    public static RefMethod<Boolean> isLastSlice;
    public static RefMethod<Parcelable> populateList;
    public static RefMethod<Void> setLastSlice;
    public static RefMethod<List<?>> getList;
}
