package com.meta.mirror.android.location;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefMethod;

public class GpsStatusL {
    public static Class<?> TYPE = RefClass.load(GpsStatusL.class, android.location.GpsStatus.class);

    @MethodParams({int.class, int[].class, float[].class, float[].class, float[].class, int[].class, int[].class, int[].class})
    public static RefMethod<Void> setStatus;

}
