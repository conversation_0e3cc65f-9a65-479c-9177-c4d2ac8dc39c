package com.meta.mirror.android.net;

import android.os.IBinder;
import android.os.IInterface;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;


/**
 * <AUTHOR>
 *
 */
public class IConnectivityManager {
    public static Class<?> TYPE = RefClass.load(IConnectivityManager.class, "android.net.IConnectivityManager");

    public static class Stub {
        public static Class<?> TYPE = RefClass.load(Stub.class, "android.net.IConnectivityManager$Stub");
        @MethodParams({IBinder.class})
        public static RefStaticMethod<IInterface> asInterface;
    }
}
