package com.meta.mirror.android.net.wifi;

import android.os.IInterface;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefObject;
import com.meta.reflect.RefStaticObject;

public class WifiManager {
    public static Class<?> TYPE = RefClass.load(WifiManager.class, android.net.wifi.WifiManager.class);
    public static RefObject<IInterface> mService;
    public static RefStaticObject<IInterface> sService;
}
