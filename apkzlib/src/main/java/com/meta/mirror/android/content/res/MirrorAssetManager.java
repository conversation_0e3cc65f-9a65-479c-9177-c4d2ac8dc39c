package com.meta.mirror.android.content.res;

import android.content.res.AssetManager;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefMethod;

public class MirrorAssetManager {
    public static Class<?> TYPE = RefClass.load(MirrorAssetManager.class, AssetManager.class);
    public static RefConstructor<AssetManager> ctor;
    @MethodParams(String.class)
    public static RefMethod<Integer> addAssetPath;
    @MethodParams(String.class)
    public static RefMethod<Integer> addAssetPathAsSharedLibrary;
}
