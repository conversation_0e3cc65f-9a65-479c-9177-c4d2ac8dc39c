package com.meta.mirror.android.app;

import android.os.IInterface;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefObject;
import com.meta.reflect.RefStaticMethod;
import com.meta.reflect.RefStaticObject;

public class ActivityClient {
    public static Class<?> TYPE = RefClass.load(ActivityClient.class, "android.app.ActivityClient");
    public static RefStaticMethod<IInterface> getActivityClientController;
    public static RefStaticObject<Object> INTERFACE_SINGLETON;

    public static class ActivityClientControllerSingleton {
        public static Class<?> TYPE = RefClass.load(ActivityClientControllerSingleton.class, "android.app.ActivityClient$ActivityClientControllerSingleton");
        public static RefObject<IInterface> mKnownInstance;
    }
}