package com.meta.mirror.android.telephony;

import android.annotation.TargetApi;
import android.os.Build;
import android.telephony.CellIdentityCdma;
import android.telephony.CellSignalStrengthCdma;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefObject;

@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
public class CellInfoCdma {
    public static Class<?> TYPE = RefClass.load(CellInfoCdma.class, android.telephony.CellInfoCdma.class);
    public static RefConstructor<android.telephony.CellInfoCdma> ctor;
    public static RefObject<CellIdentityCdma> mCellIdentityCdma;
    public static RefObject<CellSignalStrengthCdma> mCellSignalStrengthCdma;
}
