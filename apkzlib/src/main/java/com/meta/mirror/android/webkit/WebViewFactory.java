package com.meta.mirror.android.webkit;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;
import com.meta.reflect.RefStaticObject;

public class WebViewFactory {
    public static Class<?> TYPE =
            RefClass.load(WebViewFactory.class, "android.webkit.WebViewFactory");
    public static RefStaticMethod<Object> getUpdateService;
    public static RefStaticObject<Boolean> sWebViewSupported;
}
