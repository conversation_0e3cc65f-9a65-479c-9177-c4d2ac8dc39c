package com.meta.mirror.android.content.pm;

import android.os.Parcelable;

import com.meta.reflect.MethodParams;
import com.meta.reflect.RefClass;
import com.meta.reflect.RefConstructor;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefStaticObject;

import java.util.List;

public class ParceledListSliceJBMR2 {
    public static RefStaticObject<Parcelable.Creator> CREATOR;
    public static Class<?> TYPE = RefClass.load(ParceledListSliceJBMR2.class, "android.content.pm.ParceledListSlice");
    @MethodParams({List.class})
    public static RefConstructor<Parcelable> ctor;
    public static RefMethod<List> getList;
}
