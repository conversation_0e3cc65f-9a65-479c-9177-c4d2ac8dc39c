package com.meta.mirror.huawei.android.app;

import android.content.pm.PackageManager;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefMethod;
import com.meta.reflect.RefObject;
import com.meta.reflect.RefStaticMethod;

public class HwApiCacheManagerEx {
    public static Class<?> TYPE = RefClass.load(HwApiCacheManagerEx.class, "huawei.android.app.HwApiCacheMangerEx");
    public static RefStaticMethod<Object> getDefault;
    public static RefObject<PackageManager> mPkg;

    public static RefMethod<Object> disableCache;

    //清空sAppInfoCache，sPackageInfoCache等对象
    public static void disableCache(Object obj) {
        RefMethod<Object> method = disableCache;
        if (method != null) {
            method.call(obj, new Object[0]);
        }
    }

}