package com.meta.mirror.huawei.android.app;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticMethod;

public class HwFrameworkFactory {
    public static Class<?> TYPE = RefClass.load(HwFrameworkFactory.class, "android.common.HwFrameworkFactory");
    public static RefStaticMethod<Object> getHwApiCacheManagerEx;

    public static Object getHwApiCacheManagerEx() {
        RefStaticMethod<Object> obj = getHwApiCacheManagerEx;
        if (obj != null) {
            return obj.call(new Object[0]);
        }
        return null;
    }

}
