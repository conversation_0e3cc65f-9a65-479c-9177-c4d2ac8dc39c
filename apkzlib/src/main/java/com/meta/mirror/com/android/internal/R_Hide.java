package com.meta.mirror.com.android.internal;

import com.meta.reflect.RefClass;
import com.meta.reflect.RefStaticInt;
import com.meta.reflect.RefStaticObject;

public final class R_Hide {

    public static Class<?> TYPE = RefClass.load(R_Hide.class, "com.android.internal.R");

    public static class id {
        public static Class<?> TYPE = RefClass.load(id.class, "com.android.internal.R$id");
        public static RefStaticInt icon;
        public static RefStaticInt contentPanel;
        public static RefStaticInt topPanel;
        public static RefStaticInt buttonPanel;
        public static RefStaticInt customPanel;
        public static RefStaticInt custom;
        public static RefStaticInt titleDivider;
        public static RefStaticInt titleDividerTop;
        public static RefStaticInt title_template;
        public static RefStaticInt scrollView;
        public static RefStaticInt alertTitle;
        public static RefStaticInt message;
        public static RefStaticInt button1;
        public static RefStaticInt button2;
        public static RefStaticInt button3;
        public static RefStaticInt text1;
        public static RefStaticInt text2;
        public static RefStaticInt leftSpacer;
        public static RefStaticInt rightSpacer;
        public static RefStaticInt resolver_list;
    }

    public static class layout {
        public static Class<?> TYPE = RefClass.load(id.class, "com.android.internal.R$layout");
        public static RefStaticInt resolver_list;
    }

    public static class drawable {
        public static Class<?> TYPE = RefClass.load(id.class, "com.android.internal.R$drawable");
        public static RefStaticInt popup_full_dark;
        public static RefStaticInt popup_top_dark;
        public static RefStaticInt popup_bottom_dark;
        public static RefStaticInt popup_full_bright;
        public static RefStaticInt popup_top_bright;
        public static RefStaticInt popup_center_bright;
        public static RefStaticInt popup_bottom_bright;
        public static RefStaticInt popup_bottom_medium;
        public static RefStaticInt popup_center_dark;
    }

    public static class styleable {

        public static Class<?> TYPE = RefClass.load(styleable.class, "com.android.internal.R$styleable");

        public static RefStaticObject<int[]> AccountAuthenticator;
        public static RefStaticInt AccountAuthenticator_accountPreferences;
        public static RefStaticInt AccountAuthenticator_accountType;
        public static RefStaticInt AccountAuthenticator_customTokens;
        public static RefStaticInt AccountAuthenticator_icon;
        public static RefStaticInt AccountAuthenticator_label;
        public static RefStaticInt AccountAuthenticator_smallIcon;
        public static RefStaticObject<int[]> SyncAdapter;
        public static RefStaticInt SyncAdapter_accountType;
        public static RefStaticInt SyncAdapter_allowParallelSyncs;
        public static RefStaticInt SyncAdapter_contentAuthority;
        public static RefStaticInt SyncAdapter_isAlwaysSyncable;
        public static RefStaticInt SyncAdapter_settingsActivity;
        public static RefStaticInt SyncAdapter_supportsUploading;
        public static RefStaticInt SyncAdapter_userVisible;
        public static RefStaticObject<int[]> Window;
        public static RefStaticObject<int[]> View;
        public static RefStaticInt Window_background;
        public static RefStaticInt View_background;
        public static RefStaticInt Window_windowDisablePreview;
        public static RefStaticInt Window_windowBackground;
        public static RefStaticInt Window_windowFullscreen;
        public static RefStaticInt Window_windowIsFloating;
        public static RefStaticInt Window_windowIsTranslucent;
        public static RefStaticInt Window_windowShowWallpaper;
    }
}
