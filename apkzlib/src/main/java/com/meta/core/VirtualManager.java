package com.meta.core;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.meta.MCore;
import com.meta.activity.CommandActivity;
import com.meta.entity.VAppInfo;
import com.meta.ext.ICmdCallback;
import com.meta.utils.AbiUtils;

public class VirtualManager {
    public static void launchApp(Activity activity, VAppInfo appInfo) {
        launchApp(activity, appInfo, false);
    }

    public static void launchApp(Activity activity, VAppInfo appInfo, boolean onlyInstall) {
        if (TextUtils.isEmpty(appInfo.packageName)) {
            return;
        }
        Intent intent = new Intent();
        boolean isRunExt = AbiUtils.isRunExt(appInfo);
        if (isRunExt) {
            intent.setComponent(new ComponentName(MCore.getConfig().getExtPackageName(), "com.meta.activity.LaunchActivity"));
        } else {
            intent.setComponent(new ComponentName(MCore.getConfig().getHostPackageName(), "com.meta.activity.LaunchActivity"));
        }
        intent.putExtra("_VBOX_|_info_", appInfo);
        intent.putExtra("_VBOX_|_onlyInstall_", onlyInstall);
        try {
            activity.startActivityForResult(intent, 0);
        } catch (Throwable e) {
            // Ignored.
        }
    }

    public static void uninstallApp(Activity activity, VAppInfo appInfo) {
        boolean isRunExt = AbiUtils.isRunExt(appInfo);
        callCmd(activity, CommandActivity.CMD_UNINSTALL, appInfo.packageName, appInfo.userId, isRunExt, null);
    }

    public static void forceStop(Activity activity, VAppInfo appInfo) {
        boolean isRunExt = AbiUtils.isRunExt(appInfo);
        callCmd(activity, CommandActivity.CMD_FORCE_STOP, appInfo.packageName, appInfo.userId, isRunExt, null);
    }

    public static void cleanApp(Activity activity, VAppInfo appInfo) {
        boolean isRunExt = AbiUtils.isRunExt(appInfo);
        callCmd(activity, CommandActivity.CMD_CLEAN, appInfo.packageName, appInfo.userId, isRunExt, null);
    }

    private static void callCmd(Activity activity, int cmd, String pkg, int userId, boolean isRunExt, ICmdCallback callback) {
        try {
            Intent intent = getCmdIntent(cmd, pkg, userId, isRunExt, callback);
            activity.startActivity(intent);
        } catch (Throwable e) {
            // Ignored.
        }
    }

    private static Intent getCmdIntent(int cmd, String pkg, int userId, boolean isRunExt, ICmdCallback callback) {
        Intent intent = new Intent();
        if (isRunExt) {
            intent.setComponent(new ComponentName(MCore.getConfig().getExtPackageName(), "com.meta.activity.CommandActivity"));
        } else {
            intent.setComponent(new ComponentName(MCore.getConfig().getHostPackageName(), "com.meta.activity.CommandActivity"));
        }
        intent.putExtra("_VBOX_|_pkg_", pkg);
        intent.putExtra("_VBOX_|_id_", userId);
        intent.putExtra("_VBOX_|_cmd_", cmd);
        if (callback != null) {
            Bundle bundle = new Bundle();
            bundle.putBinder("binder", callback.asBinder());
            intent.putExtra("_VBOX_|_callback_", bundle);
        }
        return intent;
    }
}
