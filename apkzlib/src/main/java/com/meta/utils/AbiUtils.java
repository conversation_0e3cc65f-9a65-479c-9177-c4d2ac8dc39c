package com.meta.utils;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.text.TextUtils;

import com.meta.MCore;
import com.meta.entity.VAppInfo;
import com.meta.mirror.android.content.pm.ApplicationInfoL;

import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class AbiUtils {
    private static final ConcurrentHashMap<String, Boolean> sMapBit64 = new ConcurrentHashMap<>();

    public static boolean is64BitAbi(ApplicationInfo applicationInfo) {
        String primaryCpuAbi = "armeabi-v7a";
        try {
            primaryCpuAbi = ApplicationInfoL.primaryCpuAbi.get(applicationInfo);
        } catch (Throwable e) {
            //
        }
        return is64bitAbi(primaryCpuAbi);
    }

    public static boolean isRunExt(VAppInfo appInfo) {
        if (appInfo.appMode == VAppInfo.MODE_APP_FROM_FILE) {
            return !appInfo.is64bit;
        }
        return !is64BitAbi(appInfo.packageName);
    }

    private static boolean is64bitAbi(String abi) {
        return "arm64-v8a".equals(abi)
                || "x86_64".equals(abi)
                || "mips64".equals(abi);
    }

    public static boolean is64BitAbi(String packageName) {
        if (sMapBit64.containsKey(packageName)) {
            return Boolean.TRUE.equals(sMapBit64.get(packageName));
        }
        boolean ret = true;
        ApplicationInfo applicationInfo;
        try {
            applicationInfo = MCore.getApp().getPackageManager()
                    .getApplicationInfo(packageName, 0);
            ret = is64BitAbi(applicationInfo);
        } catch (PackageManager.NameNotFoundException e) {
            // Ignored.
        }
        sMapBit64.put(packageName, ret);
        return ret;
    }

    public static boolean is64BitAbi2(ApplicationInfo applicationInfo) {
        if (applicationInfo == null) return false;

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) { // android 5.0
            return false;
        }

        String sourcePath = applicationInfo.publicSourceDir;
        if (TextUtils.isEmpty(sourcePath)) {
            return false;
        }

        boolean hasArm64 = false;
        boolean hasArmeabi = false;
        try (ZipFile zipFile = new ZipFile(sourcePath)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String name = entry.getName();
                if (name.contains("../")) {
                    continue;
                }

                if (name.startsWith("lib/arm64") && !entry.isDirectory()) {
                    hasArm64 = true;
                } else if (name.startsWith("lib/armeabi") && !entry.isDirectory()) {
                    hasArmeabi = true;
                }
            }
        } catch (Exception e) {
            // Ignored.
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // android 5.0
            if (hasArm64 && hasArmeabi) {
                return is64bitCPU();
            } else if (!hasArm64 && !hasArmeabi) {
                return is64bitCPU();
            } else {
                return hasArm64;
            }
        } else {
            return false;
        }
    }

    // 判断手机是64位
    private static boolean is64bitCPU() {
        boolean is64bit = false;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            is64bit = Build.SUPPORTED_64_BIT_ABIS.length > 0;
        }

        return is64bit;
    }
}
