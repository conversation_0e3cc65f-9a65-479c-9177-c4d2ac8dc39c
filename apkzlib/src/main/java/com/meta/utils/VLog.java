package com.meta.utils;

import android.os.Bundle;
import android.util.Log;

import com.meta.reflect.Reflect;

import java.util.Set;

public class VLog {

    public static boolean OPEN_LOG = false;
    public static final String TAG_DEFAULT = "vbox_lg";

    public static void i(String tag, String msg, Object... format) {
        if (OPEN_LOG) {
            Log.i(tag, String.format(msg, format));
        }
    }

    public static void d(String tag, String msg, Object... format) {
        if (OPEN_LOG) {
            Log.d(tag, String.format(msg, format));
        }
    }

    public static void logbug(String tag, String msg) {
        d(tag, msg);
    }

    public static void w(String tag, String msg, Object... format) {
        if (OPEN_LOG) {
            Log.w(tag, String.format(msg, format));
        }
    }

    public static void w(String tag, String msg, Throwable th) {
        if (OPEN_LOG) {
            Log.w(tag, msg, th);
        }
    }

    public static void w(String tag, Throwable e) {
        if (OPEN_LOG) {
            Log.w(tag, e);
        }
    }

    public static void e(String tag, String msg) {
        Log.e(tag, msg);
    }

    public static void e(String tag, String msg, Object... format) {
        if (OPEN_LOG) {
            Log.e(tag, String.format(msg, format));
        }
    }

    public static void e(String tag, String msg, Throwable tr) {
        if (OPEN_LOG) {
            Log.e(tag, msg, tr);
        }
    }

    public static void v(String tag, String msg) {
        if (OPEN_LOG) {
            Log.v(tag, msg);
        }
    }

    public static void v(String tag, String msg, Object... format) {
        if (OPEN_LOG) {
            Log.v(tag, String.format(msg, format));
        }
    }

    public static String toString(Bundle bundle) {
        if (bundle == null) return null;
        if (Reflect.on(bundle).get("mParcelledData") != null) {
            Set<String> keys = bundle.keySet();
            StringBuilder stringBuilder = new StringBuilder("Bundle[");
            if (keys != null) {
                for (String key : keys) {
                    stringBuilder.append(key);
                    stringBuilder.append("=");
                    stringBuilder.append(bundle.get(key));
                    stringBuilder.append(",");
                }
            }
            stringBuilder.append("]");
            return stringBuilder.toString();
        }
        return bundle.toString();
    }

    public static String getStackTraceString(Throwable tr) {
        return Log.getStackTraceString(tr);
    }

    public static void printStackTrace(String tag) {
        Log.e(tag, getStackTraceString(new Exception()));
    }

    public static void e(String tag, Throwable e) {
        Log.e(tag, getStackTraceString(e));
    }
}
