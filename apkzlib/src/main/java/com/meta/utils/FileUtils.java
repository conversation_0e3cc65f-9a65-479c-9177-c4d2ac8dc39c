package com.meta.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Parcel;
import android.text.format.Formatter;

import com.meta.MCore;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class FileUtils {
    public static long copyInputStreamToFile(InputStream inputStream, File file) throws Exception {
        long length = 0;
        FileOutputStream fileOutputStream = null;
        FileChannel fileChannel = null;
        try {
            fileOutputStream = new FileOutputStream(file);
            fileChannel = fileOutputStream.getChannel();
            byte[] bArr = new byte[4096];
            while (true) {
                int read = inputStream.read(bArr);
                if (read <= 0) break;
                fileChannel.write(ByteBuffer.wrap(bArr, 0, read));
                length += read;
            }
            return length;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (fileChannel != null) {
                try {
                    fileChannel.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void copyFile(File source, File target) throws IOException {
        FileInputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            inputStream = new FileInputStream(source);
            outputStream = new FileOutputStream(target);
            FileChannel iChannel = inputStream.getChannel();
            FileChannel oChannel = outputStream.getChannel();

            ByteBuffer buffer = ByteBuffer.allocate(1024);
            while (true) {
                buffer.clear();
                int r = iChannel.read(buffer);
                if (r == -1)
                    break;
                buffer.limit(buffer.position());
                buffer.position(0);
                oChannel.write(buffer);
            }
        } finally {
            closeQuietly(inputStream);
            closeQuietly(outputStream);
        }
    }

    public static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {
            }
        }
    }

    public static byte[] toByteArray(InputStream inStream) throws IOException {
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc;
        while ((rc = inStream.read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        return swapStream.toByteArray();
    }

    public static void writeParcelToFile(Parcel p, File file) throws IOException {
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(p.marshall());
        fos.close();
    }

    public static long writeToFile(InputStream dataIns, File target) throws IOException {
        final int BUFFER = 1024;
        long total = 0;
        BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(target));
        try {
            int count;
            byte[] data = new byte[BUFFER];
            while ((count = dataIns.read(data, 0, BUFFER)) != -1) {
                bos.write(data, 0, count);
                total += count;
            }
            bos.close();
            return total;
        } catch (IOException e) {
            throw e;
        } finally {
            FileUtils.closeQuietly(bos);
        }
    }

    public static boolean ensureDirCreate(File dir) {
        return dir.exists() || dir.mkdirs();
    }

    public static boolean ensureDirCreate(File... dirs) {
        boolean created = true;
        for (File dir : dirs) {
            if (!ensureDirCreate(dir)) {
                created = false;
            }
        }
        return created;
    }

    public static long getFileSize(File file) {
        long size = 0;
        if (!file.exists()) {
            return 0;
        }
        if (!file.isDirectory()) {
            return file.length();
        }
        File[] listFiles = file.listFiles();
        if (listFiles != null) {
            for (File childFile : listFiles) {
                size += getFileSize(childFile);
            }
        }
        return size;
    }

    public static String formatFileSize(Context context, long sizeBytes) {
        return Formatter.formatFileSize(context, sizeBytes);
    }

    public static void deleteDir(File dir) {
        boolean isDir = dir.isDirectory();
        if (isDir) {
            boolean link = false;
            try {
                link = isSymlink(dir);
            } catch (Exception e) {
                //ignore
            }
            if (!link) {
                String[] children = dir.list();
                if (children != null) {
                    for (String file : children) {
                        deleteDir(new File(dir, file));
                    }
                }
            }
        }
        dir.delete();
    }

    public static void deleteDir(String dir) {
        deleteDir(new File(dir));
    }

    public static boolean isSymlink(File file) throws IOException {
        if (file == null)
            throw new NullPointerException("File must not be null");
        File canon;
        if (file.getParent() == null) {
            canon = file;
        } else {
            File canonDir = file.getParentFile().getCanonicalFile();
            canon = new File(canonDir, file.getName());
        }
        return !canon.getCanonicalFile().equals(canon.getAbsoluteFile());
    }

    public static String getFileMD5(File file) {
        if (!file.exists() || !file.isFile()) {
            return null;
        }
        MessageDigest digest = null;
        FileInputStream in = null;
        byte buffer[] = new byte[2048];
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
            in = new FileInputStream(file);
            while ((len = in.read(buffer)) != -1) {
                digest.update(buffer, 0, len);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        BigInteger bigInt = new BigInteger(1, digest.digest());
        return bigInt.toString(16);
    }

    public static boolean unZip4Asset(String name, String destDirectory) {
        AssetManager assetManager = MCore.getApp().getAssets();
        try {
            InputStream inputStream = assetManager.open(name);
            ZipInputStream zipInputStream = new ZipInputStream(inputStream);

            byte[] buffer = new byte[1024];
            ZipEntry zipEntry;
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String entryName = zipEntry.getName();
                String entryPath = destDirectory + File.separator + entryName;

                if (zipEntry.isDirectory()) {
                    File dir = new File(entryPath);
                    boolean isSuccess = dir.mkdirs();
                    if (!isSuccess) {
                        VLog.w("vbox_zip", "dir.mkdirs failed " + dir);
                    }
                } else {
                    FileOutputStream outputStream = new FileOutputStream(entryPath);
                    int readLength;
                    while ((readLength = zipInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, readLength);
                    }
                    outputStream.close();
                }
                zipInputStream.closeEntry();
            }
            zipInputStream.close();
            return true;
        } catch (Throwable e) {
            VLog.e("vbox_zip", "Failed to unzip asset file: " + name, e);
            return false;
        }
    }
}
