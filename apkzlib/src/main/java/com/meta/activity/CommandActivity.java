package com.meta.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.text.TextUtils;

import com.meta.ext.ICmdCallback;
import com.meta.utils.BundleCompat;
import com.meta.utils.VLog;

import net_62v.external.MetaActivityManager;
import net_62v.external.MetaPackageManager;

public class CommandActivity extends Activity {
    public static final int CMD_FORCE_STOP = 0;
    public static final int CMD_UNINSTALL = 1;
    public static final int CMD_CLEAN = 2;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        finish();
        Intent intent = getIntent();
        if (intent == null) {
            VLog.w("vbox_launch_ext", "cmd intent is null!!!");
            return;
        }

        String packageName = intent.getStringExtra("_VBOX_|_pkg_");
        int userId = intent.getIntExtra("_VBOX_|_id_", 0);
        int cmd = intent.getIntExtra("_VBOX_|_cmd_", CMD_FORCE_STOP);
        IBinder binder = BundleCompat.getBinder(intent, "_VBOX_|_callback_");

        ICmdCallback callback = ICmdCallback.Stub.asInterface(binder);

        if (TextUtils.isEmpty(packageName)) {
            return;
        }
        if (cmd == CMD_FORCE_STOP) {
            VLog.i("vbox_launch_ext", "forceStop:" + packageName + "," + userId);
            MetaActivityManager.killAppByPkg(userId, packageName);
        } else if (cmd == CMD_UNINSTALL) {
            VLog.i("vbox_launch_ext", "uninstall:" + packageName + "," + userId);
            MetaActivityManager.killAppByPkg(userId, packageName);
            int[] ids = MetaPackageManager.getInstalledUserId(packageName);
            if (ids == null || ids.length <= 1) {
                MetaPackageManager.uninstallAppFully(packageName);
            } else {
                MetaPackageManager.cleanPackageDataAsUser(packageName, userId);
            }
        } else if (cmd == CMD_CLEAN) {
            MetaActivityManager.killAppByPkg(userId, packageName);
            MetaPackageManager.cleanPackageDataAsUser(packageName, userId);
        }
    }
}
