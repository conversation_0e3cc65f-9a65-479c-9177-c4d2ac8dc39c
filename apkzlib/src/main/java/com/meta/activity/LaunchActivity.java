package com.meta.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;

import com.meta.MCore;
import com.meta.entity.VAppInfo;
import com.meta.utils.FileUtils;
import com.meta.utils.VLog;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

import net_62v.external.MetaActivationManager;
import net_62v.external.MetaActivityManager;
import net_62v.external.MetaApplicationInstaller;

public class LaunchActivity extends Activity {
    private static final Handler sHandler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Intent intent = getIntent();
        if (intent == null) {
            return;
        }

        VAppInfo appInfo = intent.getParcelableExtra("_VBOX_|_info_");
        boolean onlyInstall = intent.getBooleanExtra("_VBOX_|_onlyInstall_", false);
        if (appInfo == null) {
            VLog.w("vbox_launch_ext", "appInfo is null!!!");
            return;
        }

        String mCallingPackage = getCallingPackage();
        if (!MCore.getConfig().getHostPackageName().equals(mCallingPackage)) {
            VLog.w("vbox_launch_ext", "s");
            return;
        }

        if (!TextUtils.isEmpty(appInfo.path)) {
            Uri uri = Uri.parse(appInfo.path);
            String scheme = uri.getScheme();
            if ("package".equals(scheme)) {
                String pkg = uri.getSchemeSpecificPart();
                try {
                    ApplicationInfo info = getPackageManager().getApplicationInfo(pkg, 0);
                    String apkPath = info.publicSourceDir != null ? info.publicSourceDir : info.sourceDir;
                    MetaApplicationInstaller.installAppByPath(apkPath);
                } catch (PackageManager.NameNotFoundException e) {
                    // Ignored.
                }
            } else {
                VLog.i("vbox_launch_ext", "start getFileFromUri:" + uri);
                String filePath = getFileFromUri(appInfo.packageName, uri);
                VLog.i("vbox_launch_ext", "end getFileFromUri:" + filePath);
                MetaApplicationInstaller.installAppByPath(filePath);
            }
        }
        if (onlyInstall) {
            finish();
            return;
        }
        launchApp(appInfo.packageName, appInfo.userId);
    }

    private void launchApp(String pkg, int userId) {
        if (MetaActivationManager.getActivationStatus()) {
            VLog.i("vbox_launch_ext", "launchApp");
            MetaActivityManager.launchApp(userId, pkg);
            finish();
        } else {
            sHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    launchApp(pkg, userId);
                }
            }, 100);
        }
    }

    private String getFileFromUri(String pkg, Uri uri) {
        ParcelFileDescriptor fileDescriptor;
        FileOutputStream outputStream = null;
        FileInputStream inputStream = null;
        try {
            fileDescriptor = getContentResolver().openFileDescriptor(uri, "r");
            inputStream = new FileInputStream(fileDescriptor.getFileDescriptor());
            File file = new File(getExternalCacheDir(), pkg + ".apk");
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            outputStream = new FileOutputStream(file);
            byte[] b = new byte[1024 * 5]; // 5KB
            int len;
            while ((len = inputStream.read(b)) != -1) {
                outputStream.write(b, 0, len);
            }
            outputStream.flush();
            return file.getAbsolutePath();
        } catch (Exception e) {
            VLog.e("vbox_launch_ext", "getFileFromUri", e);
            return "";
        } finally {
            FileUtils.closeQuietly(inputStream);
            FileUtils.closeQuietly(outputStream);
        }
    }
}
