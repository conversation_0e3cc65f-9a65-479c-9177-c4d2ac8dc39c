package com.meta;

import android.app.Activity;
import android.app.Application;

import com.meta.core.SettingConfig;
import com.meta.core.VirtualManager;
import com.meta.entity.VAppInfo;
import com.meta.utils.ProcessUtils;

import java.io.File;

public class MCore {
    public static Application getApp() {
        return sApplication;
    }
    public static SettingConfig getConfig() {
        return sConfig;
    }

    private static Application sApplication = null;
    private static SettingConfig sConfig = null;
    private static String sProcessName = "";
    private static boolean sIsInitSpaceCore = false;

    public static class ApkInstallResult {
        private boolean success;
        private String packageName;
        private String msg;

        public ApkInstallResult() {}

        public final boolean getSuccess() {
            return this.success;
        }

        public final void setSuccess(boolean var1) {
            this.success = var1;
        }

        public final String getPackageName() {
            return this.packageName;
        }

        public final void setPackageName(String var1) {
            this.packageName = var1;
        }

        public final String getMsg() {
            return this.msg;
        }

        public final void setMsg(String var1) {
            this.msg = var1;
        }
    }

    private static boolean isInit = false;
    public static void init(Application context, SettingConfig config) {
        if (isInit) {
            return;
        }
        sApplication = context;
        sConfig = config;

        sProcessName = ProcessUtils.getCurrentProcessName(context);
        isInit = true;
    }

    public static String getProcessName() {
        return sProcessName;
    }

    public static ApkInstallResult installPackage(Activity activity, VAppInfo appInfo) {
        ApkInstallResult apkInstallResult = new ApkInstallResult();
        apkInstallResult.setPackageName(appInfo.packageName);
        apkInstallResult.setSuccess(true);
        apkInstallResult.setMsg("success");
        return apkInstallResult;
    }

    public static ApkInstallResult installPackage(Activity activity, File file, VAppInfo appInfo) {
        VirtualManager.launchApp(activity, appInfo, true);
        ApkInstallResult apkInstallResult = new ApkInstallResult();
        apkInstallResult.setPackageName(appInfo.packageName);
        apkInstallResult.setSuccess(true);
        apkInstallResult.setMsg("success");
        return apkInstallResult;
    }

    public static void launchApp(Activity activity, VAppInfo appInfo) {
        VirtualManager.launchApp(activity, appInfo);
    }

    public static void forceStop(Activity activity, VAppInfo appInfo) {
        VirtualManager.forceStop(activity, appInfo);
    }

    public static void cleanData(Activity activity, VAppInfo appInfo) {
        VirtualManager.cleanApp(activity, appInfo);
    }

    public static void uninstallPackage(Activity activity, VAppInfo appInfo) {
        VirtualManager.uninstallApp(activity, appInfo);
    }

    public static boolean isOutsideInstalled(String packageName) {
        if (packageName == null) {
            return false;
        }
        try {
            return sApplication.getPackageManager().getApplicationInfo(packageName, 0) != null;
        } catch (Throwable e) {
            // Ignore
        }
        return false;
    }
}
