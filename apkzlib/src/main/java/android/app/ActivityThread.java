package android.app;

import android.os.IBinder;

public class ActivityThread {
    Application mInitialApplication;
    public static ActivityThread currentActivityThread() {
        return null;
    }
    public static String currentProcessName() {
        return null;
    }
    public static Application currentApplication() {
        ActivityThread am = currentActivityThread();
        return am != null ? am.mInitialApplication : null;
    }

    public ActivityClientRecord getActivityClient(IBinder token) {
        return null;
    }

    public static final class ActivityClientRecord {}
}
