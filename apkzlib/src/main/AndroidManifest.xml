<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="top.bienvenido.mundo"
    android:installLocation="preferExternal" >

    <permission
        android:name="meta.magic.permission.SAFE_ACCESS"
        android:protectionLevel="signature" />

    <!-- <uses-permission android:name="com.android.permission.GET_INSTALLED_APPS" /> -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="com.open.gallery.smart.Read" />
    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission
        android:name="android.permission.CAPTURE_VIDEO_OUTPUT"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.RUN_USER_INITIATED_JOBS" />
    <uses-permission android:name="android.permission.ACCEPT_HANDOVER" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.BODY_SENSORS_BACKGROUND" />
    <uses-permission android:name="android.permission.UWB_RANGING" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <uses-permission
        android:name="android.permission.MANAGE_MEDIA_PROJECTION"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MANAGE_GAME_MODE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="miui.permission.WRITE_STEPS" />
    <uses-permission android:name="miui.permission.READ_STEPS" />
<!--    <uses-permission android:name="com.xiaomi.sdk.permission.PAYMENT" />-->
    <uses-permission android:name="com.xiaomi.permission.AUTH_SERVICE" />
    <uses-permission
        android:name="android.permission.SET_PREFERRED_APPLICATIONS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.NETWORK_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MANAGE_ROLE_HOLDERS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.READ_SEARCH_INDEXABLES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="com.huawei.permission.MANAGE_VOICERECOGNITION" />
    <uses-permission android:name="com.huawei.permission.MANAGE_USE_SECURITY" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="ohos.permission.READ_MESSAGES" />
    <uses-permission android:name="ohos.permission.RECEIVE_MMS" />
    <uses-permission android:name="ohos.permission.RECEIVE_SMS" />
    <uses-permission android:name="ohos.permission.RECEIVE_WAP_MESSAGES" />
    <uses-permission android:name="ohos.permission.SEND_MESSAGES" />
    <uses-permission android:name="ohos.permission.READ_CELL_MESSAGES" />
    <uses-permission android:name="ohos.permission.READ_CALL_LOG" />
    <uses-permission android:name="ohos.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="ohos.permission.READ_CONTACTS" />
    <uses-permission android:name="ohos.permission.WRITE_CONTACTS" />
    <uses-permission android:name="ohos.permission.ANSWER_CALL" />
    <uses-permission android:name="ohos.permission.GET_BUNDLE_INFO" />
    <uses-permission android:name="ohos.permission.DISTRIBUTED_DATASYNC" />
    <uses-permission android:name="ohos.permission.DISTRIBUTED_DEVICE_STATE_CHANGE" />
    <uses-permission android:name="ohos.permission.GET_DISTRIBUTED_DEVICE_INFO" />
    <uses-permission android:name="ohos.permission.ACCESS_SERVICE" />
    <uses-permission android:name="ohos.permission.LOCATION" />
    <uses-permission android:name="ohos.permission.LOCATION_IN_BACKGROUND" />
    <uses-permission android:name="ohos.permission.CAMERA" />
    <uses-permission android:name="ohos.permission.MICROPHONE" />
    <uses-permission android:name="ohos.permission.READ_CALENDAR" />
    <uses-permission android:name="ohos.permission.BIND_SERVICE" />
    <uses-permission android:name="ohos.permission.ACTIVITY_MOTION" />
    <uses-permission android:name="ohos.permission.READ_HEALTH_DATA" />
    <uses-permission android:name="ohos.permission.MEDIA_LOCATION" />
    <uses-permission android:name="ohos.permission.READ_MEDIA" />
    <uses-permission android:name="ohos.permission.WRITE_MEDIA" />
    <uses-permission android:name="ohos.permission.GET_APP_ACCOUNTS" />
    <uses-permission android:name="ohos.permission.GET_NETWORK_INFO" />
    <uses-permission android:name="ohos.permission.GET_WIFI_INFO" />
    <uses-permission android:name="ohos.permission.USE_BLUETOOTH" />
    <uses-permission android:name="ohos.permission.DISCOVER_BLUETOOTH" />
    <uses-permission android:name="ohos.permission.SET_NETWORK_INFO" />
    <uses-permission android:name="ohos.permission.SET_WIFI_INFO" />
    <uses-permission android:name="ohos.permission.SPREAD_STATUS_BAR" />
    <uses-permission android:name="ohos.permission.INTERNET" />
    <uses-permission android:name="ohos.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="ohos.permission.RECEIVER_STARTUP_COMPLETED" />
    <uses-permission android:name="ohos.permission.RUNNING_LOCK" />
    <uses-permission android:name="ohos.permission.ACCESS_BIOMETRIC" />
    <uses-permission android:name="ohos.permission.RCV_NFC_TRANSACTION_EVENT" />
    <uses-permission android:name="ohos.permission.COMMONEVENT_STICKY" />
    <uses-permission android:name="ohos.permission.SYSTEM_FLOAT_WINDOW" />
    <uses-permission android:name="ohos.permission.VIBRATE" />
    <uses-permission android:name="ohos.permission.USE_TRUSTCIRCLE_MANAGER" />
    <uses-permission android:name="ohos.permission.USE_WHOLE_SCREEN" />
    <uses-permission android:name="ohos.permission.SET_WALLPAPER" />
    <uses-permission android:name="ohos.permission.SET_WALLPAPER_DIMENSION" />
    <uses-permission android:name="ohos.permission.REARRANGE_MISSIONS" />
    <uses-permission android:name="ohos.permission.CLEAN_BACKGROUND_PROCESSES" />
    <uses-permission android:name="ohos.permission.KEEP_BACKGROUND_RUNNING" />
    <uses-permission android:name="ohos.permission.ACCELEROMETER" />
    <uses-permission android:name="ohos.permission.GYROSCOPE" />
    <uses-permission android:name="ohos.permission.MULTIMODAL_INTERACTIVE" />
    <uses-permission android:name="ohos.permission.ACCESS_FM_AM" />
    <uses-permission android:name="ohos.permission.NFC_TAG" />
    <uses-permission android:name="ohos.permission.NFC_CARD_EMULATION" />
    <uses-permission
        android:name="android.permission.READ_DEVICE_CONFIG"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_PRIVILEGED"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
    <uses-permission
        android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-permission-sdk-23 android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <uses-permission
        android:name="android.permission.USE_EXACT_ALARM"
        tools:ignore="ExactAlarm" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.RUN_LONG_JOBS" />
    <uses-permission android:name="android.hardware.usb.host" />
    <uses-permission
        android:name="android.permission.HARDWARE_TEST"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.INTERACT_ACROSS_USERS_FULL"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.WRITE_PROFILE" />
    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" /> <!-- samsung start -->
    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" /> <!-- samsung end -->
    <!-- other app start -->
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" /> <!-- other app end -->
    <!-- google play store start -->
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="com.android.vending.CHECK_LICENSE" /> <!-- google play store end -->
    <!-- google services framework start -->
    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" /> <!-- google services framework end -->
    <!-- shortcut and badge start -->
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.READ_APP_BADGE" />
    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" />
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" />
    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" />
    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" />
    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <!-- 常用 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" /> <!-- 位置服务 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" /> <!-- 网络 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- sdcard -->
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.BATTERY_STATS"
        tools:ignore="ProtectedPermissions" /> <!-- 身体传感器 -->
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" /> <!-- 锁屏 -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" /> <!-- 允许应用程序展开或折叠状态栏 -->
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" /> <!-- 账号相关 -->
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" /> <!-- 通话状态 -->
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        android:maxSdkVersion="29" /> <!-- 修改当前配置 -->
    <uses-permission
        android:name="android.permission.CHANGE_CONFIGURATION"
        tools:ignore="ProtectedPermissions" /> <!-- 设置 -->
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" /> <!-- 最近任务/进程 -->
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" /> <!-- 录音 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- bluetooth -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" /> <!-- nfc -->
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" /> <!-- 震动 -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 指纹 -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- 下面自己按需保留 -->
    <!-- browser history start -->
    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" /> <!-- browser history end -->
    <!-- 清空应用缓存 -->
    <uses-permission
        android:name="android.permission.CLEAR_APP_CACHE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" /> <!-- 同步设置 -->
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" /> <!-- rss -->
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" /> <!-- 日历 -->
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.READ_CALENDAR" /> <!-- 通讯录 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" /> <!-- 系统日志 -->
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- 重启应用 -->
    <uses-permission android:name="android.permission.RESTART_PACKAGES" /> <!-- 时间/时区 -->
    <uses-permission
        android:name="android.permission.SET_TIME"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.SET_TIME_ZONE"
        tools:ignore="ProtectedPermissions" /> <!-- 墙纸 -->
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" /> <!-- 红外线 -->
    <uses-permission android:name="android.permission.TRANSMIT_IR" /> <!-- SIP服务 -->
    <uses-permission android:name="android.permission.USE_SIP" /> <!-- 唤醒屏幕 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- float window -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" /> <!-- boot -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- system app start -->
    <uses-permission
        android:name="android.permission.GET_INTENT_SENDER_INTENT"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.BIND_DIRECTORY_SEARCH"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.UPDATE_APP_OPS_STATS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.DELETE_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.CLEAR_APP_USER_DATA"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_MEDIA_STORAGE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.ACCESS_CACHE_FILESYSTEM"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.DEVICE_POWER"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_APN_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.BIND_APPWIDGET"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.ACCOUNT_MANAGER"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" /> <!-- system app end -->
    <!-- not found -->
    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
    <uses-permission android:name="android.permission.WRITE_CLIPS" />
    <uses-permission android:name="android.permission.READ_CLIPS" />
    <uses-permission android:name="android.permission.GET_CLIPS" />
    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
    <uses-permission android:name="android.permission.ACCESS_GPS" />
    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- call phone -->
    <uses-permission android:name="com.sk.spatch.permission.SAFE_ACCESS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
    <uses-permission android:name="android.permission.SEND_SMS" /> <!-- google app start -->
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.WRITE_SMS" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission
        android:name="android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_HEALTH" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_REMOTE_MESSAGING" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.INTERACT_ACROSS_USERS"
        tools:ignore="ProtectedPermissions" />

    <application
        android:allowNativeHeapPointerTagging="false"
        android:usesCleartextTraffic="true"
        tools:targetApi="r" >

        <activity android:name="com.meta.activity.LaunchActivity"
            android:permission="meta.magic.permission.SAFE_ACCESS"
            android:excludeFromRecents="true"
            android:exported="true"
            android:taskAffinity="virtual.shortcut.task"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            >
        </activity>

        <activity android:name="com.meta.activity.CommandActivity"
            android:permission="meta.magic.permission.SAFE_ACCESS"
            android:excludeFromRecents="true"
            android:exported="true"
            android:taskAffinity="virtual.shortcut.task"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            >
        </activity>

        <provider
            android:name="top.bienvenido.mundo.supervisor.MundoSupervisorProvider"
            android:authorities="${applicationId}.service.provider"
            android:exported="false" />

        <meta-data android:name="MUNDO_APP_KEY" android:value="MWGQP58CHN22IY2G" />
        <meta-data android:name="MUNDO_API_VERSION" android:value="1" />

        <activity android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoIntermediary"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="Mundo Login"
            android:launchMode="singleTask"
            android:noHistory="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="fbconnect" />
            </intent-filter>
        </activity>
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoSplashLauncher"
            android:exported="false"
            android:launchMode="singleTask"
            android:noHistory="true"
            android:theme="@android:style/Theme.DeviceDefault.NoActionBar.Fullscreen" />

        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB"
            android:permission="android.permission.BIND_VPN_SERVICE" />

        <receiver
            android:name="top.bienvenido.mundo.manifest.MundoReceiver"
            android:exported="true"
            tools:ignore="ExportedReceiver" >
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="${applicationId}.mundo.receiver.stub" />
            </intent-filter>
        </receiver>

        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB1"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub1"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task1"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB2"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub2"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task2"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB3"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub3"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task3"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB4"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub4"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task4"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB5"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub5"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task5"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB6"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub6"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task6"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB7"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub7"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task7"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB8"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub8"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task8"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB9"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub9"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task9"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB10"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub10"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task10"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB11"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub11"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task11"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB12"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub12"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task12"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB13"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub13"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task13"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB14"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub14"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task14"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB15"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub15"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task15"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB16"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub16"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task16"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB17"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub17"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task17"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB18"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub18"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task18"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB19"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub19"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task19"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB20"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub20"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task20"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB21"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub21"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task21"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB22"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub22"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task22"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB23"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub23"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task23"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB24"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub24"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task24"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB1H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub1"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task1"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB2H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub2"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task2"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB3H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub3"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task3"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB4H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub4"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task4"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB5H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub5"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task5"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB6H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub6"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task6"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB7H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub7"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task7"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB8H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub8"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task8"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB9H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub9"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task9"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB10H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub10"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task10"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB11H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub11"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task11"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB12H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub12"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task12"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB13H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub13"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task13"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB14H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub14"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task14"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB15H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub15"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task15"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB16H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub16"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task16"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB17H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub17"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task17"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB18H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub18"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task18"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB19H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub19"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task19"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB20H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub20"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task20"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB21H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub21"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task21"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB22H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub22"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task22"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB23H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub23"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task23"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />
        <activity
            android:name="top.bienvenido.mundo.manifest.MundoActivity$Companion$STUB24H"
            android:configChanges="touchscreen|keyboard|keyboardHidden|navigation|mcc|mnc|screenLayout|screenSize|uiMode|fontScale|fontWeightAdjustment|smallestScreenSize|density|colorMode|layoutDirection|locale|orientation"
            android:process=":stub24"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true"
            android:taskAffinity="top.bienvenido.mundo.task24"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            tools:ignore="DiscouragedApi" />

        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB1"
            android:authorities="${applicationId}.provider.stub1"
            android:exported="true"
            android:process=":stub1"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB2"
            android:authorities="${applicationId}.provider.stub2"
            android:exported="true"
            android:process=":stub2"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB3"
            android:authorities="${applicationId}.provider.stub3"
            android:exported="true"
            android:process=":stub3"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB4"
            android:authorities="${applicationId}.provider.stub4"
            android:exported="true"
            android:process=":stub4"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB5"
            android:authorities="${applicationId}.provider.stub5"
            android:exported="true"
            android:process=":stub5"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB6"
            android:authorities="${applicationId}.provider.stub6"
            android:exported="true"
            android:process=":stub6"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB7"
            android:authorities="${applicationId}.provider.stub7"
            android:exported="true"
            android:process=":stub7"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB8"
            android:authorities="${applicationId}.provider.stub8"
            android:exported="true"
            android:process=":stub8"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB9"
            android:authorities="${applicationId}.provider.stub9"
            android:exported="true"
            android:process=":stub9"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB10"
            android:authorities="${applicationId}.provider.stub10"
            android:exported="true"
            android:process=":stub10"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB11"
            android:authorities="${applicationId}.provider.stub11"
            android:exported="true"
            android:process=":stub11"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB12"
            android:authorities="${applicationId}.provider.stub12"
            android:exported="true"
            android:process=":stub12"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB13"
            android:authorities="${applicationId}.provider.stub13"
            android:exported="true"
            android:process=":stub13"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB14"
            android:authorities="${applicationId}.provider.stub14"
            android:exported="true"
            android:process=":stub14"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB15"
            android:authorities="${applicationId}.provider.stub15"
            android:exported="true"
            android:process=":stub15"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB16"
            android:authorities="${applicationId}.provider.stub16"
            android:exported="true"
            android:process=":stub16"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB17"
            android:authorities="${applicationId}.provider.stub17"
            android:exported="true"
            android:process=":stub17"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB18"
            android:authorities="${applicationId}.provider.stub18"
            android:exported="true"
            android:process=":stub18"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB19"
            android:authorities="${applicationId}.provider.stub19"
            android:exported="true"
            android:process=":stub19"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB20"
            android:authorities="${applicationId}.provider.stub20"
            android:exported="true"
            android:process=":stub20"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB21"
            android:authorities="${applicationId}.provider.stub21"
            android:exported="true"
            android:process=":stub21"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB22"
            android:authorities="${applicationId}.provider.stub22"
            android:exported="true"
            android:process=":stub22"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB23"
            android:authorities="${applicationId}.provider.stub23"
            android:exported="true"
            android:process=":stub23"
            tools:ignore="ExportedContentProvider" />
        <provider
            android:name="top.bienvenido.mundo.manifest.MundoProvider$Companion$STUB24"
            android:authorities="${applicationId}.provider.stub24"
            android:exported="true"
            android:process=":stub24"
            tools:ignore="ExportedContentProvider" />

        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB1"
            android:exported="false"

            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub1"
            />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB2"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub2" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB3"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub3" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB4"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub4" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB5"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub5" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB6"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub6" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB7"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub7" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB8"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub8" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB9"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub9" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB10"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub10" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB11"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub11" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB12"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub12" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB13"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub13" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB14"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub14" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB15"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub15" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB16"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub16" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB17"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub17" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB18"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub18" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB19"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub19" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB20"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub20" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB21"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub21" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB22"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub22" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB23"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub23" />
        <service
            android:name="top.bienvenido.mundo.manifest.MundoService$Companion$STUB24"
            android:exported="false"
            
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":stub24" />
    </application>

</manifest>