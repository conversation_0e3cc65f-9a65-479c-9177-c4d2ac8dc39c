package com.gw.swipeback;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 解决滑动结束后，finish过程中还设置view可见或者隐藏等触发onLayout出现闪屏的bug
 */
public class SuperSwipeBackLayout extends SwipeBackLayout {
    public SuperSwipeBackLayout(@NonNull Context context) {
        this(context, null);
    }

    public SuperSwipeBackLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SuperSwipeBackLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        if (isFinish) {
            return;
        }
        super.onLayout(changed, l, t, r, b);
    }


    private boolean isFinish = false;

    @Override
    public void finish() {
        isFinish = true;
        super.finish();

    }
}
