apply plugin: 'com.android.library'

android {
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 26
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'vlib-proguard-rules.pro'
        }
    }

    lintOptions {
        //IJobService need NewApi
        abortOnError false
        warning 'NewA<PERSON>', 'OnClick', 'EllipsizeMaxLines'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}