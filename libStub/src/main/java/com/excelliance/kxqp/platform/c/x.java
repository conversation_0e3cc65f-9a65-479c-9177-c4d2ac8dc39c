//package com.excelliance.kxqp.platform.c;
//
//import android.content.ComponentName;
//import android.content.Intent;
//import android.content.pm.ActivityInfo;
//import android.content.pm.ApplicationInfo;
//import android.content.pm.ComponentInfo;
//import android.content.pm.PackageInfo;
//import android.content.pm.PackageStats;
//import android.content.pm.PermissionGroupInfo;
//import android.content.pm.PermissionInfo;
//import android.content.pm.ProviderInfo;
//import android.content.pm.ResolveInfo;
//import android.content.pm.ServiceInfo;
//import android.graphics.Bitmap;
//import android.os.Bundle;
//
//import java.util.List;
//import java.util.Map;
//
//public class x {
//    public static native x a();
//
//    private static native void a(ApplicationInfo applicationInfo);
//
//    private static native void a(ComponentInfo componentInfo);
//
//    private static native void a(PackageInfo packageInfo);
//
//    private static native void a(ResolveInfo resolveInfo);
//
//    private static native void a(Exception exc);
//
//    private static native void a(List list);
//
//    private native List b(int i, ActivityInfo activityInfo);
//
//    private static native void b(List list);
//
//    private static native void c(List list);
//
//    private static native void d(List list);
//
//    public final native Map A(int i, String str);
//
//    public final native int B(int i, String str);
//
//    public final native int C(int i, String str);
//
//    public final native String D(int i, String str);
//
//    public final native String E(int i, String str);
//
//    public final native int a(int i, int i2, boolean z);
//
//    public final native int a(int i, ComponentName componentName);
//
//    public final native int a(int i, String str);
//
//    public final native int a(int i, String str, String str2, int i2);
//
//    public final native int a(int i, Map map);
//
//    public final native int a(String str, int i);
//
//    public final native int a(String str, String str2, int i);
//
//    public final native ActivityInfo a(int i, ComponentName componentName, int i2);
//
//    public final native ActivityInfo a(int i, Intent intent);
//
//    public final native ActivityInfo a(int i, Intent intent, int i2);
//
//    public final native PackageInfo a(int i);
//
//    public final native PackageInfo a(int i, String str, int i2);
//
//    public final native ProviderInfo a(int i, String str, Integer num);
//
//    public final native ResolveInfo a(int i, Intent intent, String str, int i2);
//
//    public final native ResolveInfo a(int i, Intent intent, String str, Integer num);
//
//    public final native CharSequence a(int i, ApplicationInfo applicationInfo);
//
//    public final native CharSequence a(int i, ResolveInfo resolveInfo);
//
//    public final native List a(int i, int i2);
//
//    public final native List a(int i, String str, int i2, int i3);
//
//    public final native List a(int i, List list);
//
//    public final native List a(ActivityInfo activityInfo);
//
//    public final native List a(ServiceInfo serviceInfo);
//
//    public final native List a(String[] strArr, boolean z);
//
//    public final native Map a(String[] strArr, int i);
//
//    public final native void a(int i, ComponentName componentName, int i2, int i3);
//
//    public final native void a(int i, String str, Bundle bundle);
//
//    public final native void a(int i, String str, String str2, Object obj);
//
//    public final native void a(int i, String str, String str2, Object obj, boolean z);
//
//    public final native boolean a(int i, ActivityInfo activityInfo);
//
//    public final native boolean a(int i, String str, int i2, boolean z);
//
//    public final native boolean a(int i, String str, long j, String str2);
//
//    public final native boolean a(int i, String str, long j, boolean z);
//
//    public final native boolean a(int i, String str, String str2);
//
//    public final native boolean a(int i, String str, Map map);
//
//    public final native boolean a(int i, String str, String[] strArr);
//
//    public final native boolean a(String str);
//
//    public final native int b(int i, String str, String str2, int i2);
//
//    public final native ActivityInfo b(int i, ComponentName componentName, int i2);
//
//    public final native ActivityInfo b(int i, Intent intent);
//
//    public final native PermissionInfo b(int i, String str, int i2);
//
//    public final native ServiceInfo b(int i, Intent intent, int i2);
//
//    public final native Bitmap b(int i, ApplicationInfo applicationInfo);
//
//    public final native Bitmap b(int i, ResolveInfo resolveInfo);
//
//    public final native List b(int i, int i2);
//
//    public final native List b(int i, Intent intent, String str, int i2);
//
//    public final native void b(int i);
//
//    public final native boolean b(int i, String str);
//
//    public final native boolean b(int i, String str, long j, boolean z);
//
//    public final native boolean b(int i, String str, Bundle bundle);
//
//    public final native boolean b(int i, String str, String str2);
//
//    public final native boolean b(int i, String str, String[] strArr);
//
//    public final native boolean b(String str, int i);
//
//    public final native int c(int i, String str, String str2);
//
//    public final native int c(int i, String str, String str2, int i2);
//
//    public final native ServiceInfo c(int i, ComponentName componentName, int i2);
//
//    public final native List c(int i);
//
//    public final native List c(int i, int i2);
//
//    public final native List c(int i, Intent intent, String str, int i2);
//
//    public final native List c(int i, String str, int i2);
//
//    public final native void c();
//
//    public final native boolean c(int i, String str);
//
//    public final native boolean c(int i, String str, String[] strArr);
//
//    public final native int d(int i, String str, String str2);
//
//    public final native PermissionGroupInfo d(int i, String str, int i2);
//
//    public final native ProviderInfo d(int i, ComponentName componentName, int i2);
//
//    public final native List d();
//
//    public final native List d(int i, int i2);
//
//    public final native List d(int i, Intent intent, String str, int i2);
//
//    public final native Map d(int i);
//
//    public final native int e();
//
//    public final native ApplicationInfo e(int i, String str, int i2);
//
//    public final native String e(int i);
//
//    public final native List e(int i, int i2);
//
//    public final native List e(int i, Intent intent, String str, int i2);
//
//    public final native boolean e(int i, String str);
//
//    public final native boolean e(int i, String str, String str2);
//
//    public final native int f(int i, String str, int i2);
//
//    public final native String f();
//
//    public final native List f(int i, String str);
//
//    public final native boolean f(int i);
//
//    public final native boolean f(int i, String str, String str2);
//
//    public final native int g();
//
//    public final native int g(int i, String str, int i2);
//
//    public final native Map g(int i, String str);
//
//    public final native boolean g(int i);
//
//    public final native boolean g(int i, String str, String str2);
//
//    public final native int h();
//
//    public final native int h(int i, String str, int i2);
//
//    public final native List h(int i);
//
//    public final native Map h(int i, String str);
//
//    public final native boolean h(int i, String str, String str2);
//
//    public final native int i(int i, String str, int i2);
//
//    public final native long i(int i, String str);
//
//    public final native boolean i(int i, String str, String str2);
//
//    public final native String j();
//
//    public final native void j(int i, String str, int i2);
//
//    public final native boolean j(int i);
//
//    public final native boolean j(int i, String str);
//
//    public final native boolean j(int i, String str, String str2);
//
//    public final native int k();
//
//    public final native String k(int i, String str);
//
//    public final native boolean k(int i);
//
//    public final native boolean k(int i, String str, int i2);
//
//    public final native boolean k(int i, String str, String str2);
//
//    public final native int l();
//
//    public final native void l(int i);
//
//    public final native boolean l(int i, String str);
//
//    public final native boolean l(int i, String str, int i2);
//
//    public final native boolean l(int i, String str, String str2);
//
//    public final native int m();
//
//    public final native int m(int i, String str);
//
//    public final native boolean m(int i);
//
//    public final native boolean m(int i, String str, int i2);
//
//    public final native boolean m(int i, String str, String str2);
//
//    public final native boolean n(int i, String str, String str2);
//
//    public final native String[] n(int i, String str);
//
//    public final native String o(int i, String str);
//
//    public final native boolean o(int i, String str, String str2);
//
//    public final native PackageStats p(int i, String str);
//
//    public final native String[] q(int i, String str);
//
//    public final native String r(int i, String str);
//
//    public final native Bundle s(int i, String str);
//
//    public final native Bundle t(int i, String str);
//
//    public final native Map u(int i, String str);
//
//    public final native String v(int i, String str);
//
//    public final native String w(int i, String str);
//
//    public final native String x(int i, String str);
//
//    public final native String y(int i, String str);
//
//    public final native String[] z(int i, String str);
//}